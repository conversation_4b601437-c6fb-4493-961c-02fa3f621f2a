<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>From BAM to Counting: Velocyto Processing Pipeline</title>
    
    <!-- MathJax 3 for LaTeX rendering -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #000;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
            border-radius: 15px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #4a6491 100%);
            color: white;
            padding: 2.5rem;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.8rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 1rem 0 0 0;
            font-size: 1.3rem;
            opacity: 0.9;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .content {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border-left: 5px solid #3498db;
            background: #ffffff;
            border-radius: 0 10px 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        }
        
        .section h2 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.8rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        
        .section h3 {
            color: #34495e;
            margin-top: 2rem;
            font-size: 1.4rem;
        }
        
        .math-box {
            background: #ffffff;
            border: 2px solid #3498db;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            box-shadow: 0 4px 8px rgba(52, 152, 219, 0.1);
        }
        
        .code-box {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 0.2rem 0.5rem;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
            font-size: 1.1rem;
        }
        
        .workflow-step {
            display: flex;
            align-items: flex-start;
            margin: 1.5rem 0;
            padding: 1.2rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
            border-left: 4px solid #3498db;
        }
        
        .svg-container {
            text-align: center;
            margin: 2.5rem 0;
            padding: 1.5rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        
        .info-box {
            background: #d1ecf1;
            border: 2px solid #17a2b8;
            border-radius: 10px;
            padding: 1.2rem;
            margin: 1.5rem 0;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 1.2rem;
            margin: 1.5rem 0;
        }
        
        .success-box {
            background: #d4edda;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 1.2rem;
            margin: 1.5rem 0;
        }
        
        .toc {
            background: #34495e;
            color: white;
            padding: 1.8rem;
            border-radius: 10px;
            margin-bottom: 2.5rem;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .toc h3 {
            margin-top: 0;
            color: #ecf0f1;
            border-bottom: 1px solid #ecf0f1;
            padding-bottom: 0.5rem;
        }
        
        .toc ul {
            list-style: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 0.7rem 0;
            padding-left: 1.2rem;
            position: relative;
        }
        
        .toc li:before {
            content: "▶";
            position: absolute;
            left: 0;
            color: #3498db;
        }
        
        .toc a {
            color: #3498db;
            text-decoration: none;
            transition: color 0.3s;
            font-weight: 500;
        }
        
        .toc a:hover {
            color: #5dade2;
            text-decoration: underline;
        }
        
        .gene-model {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
            overflow-x: auto;
        }
        
        .decision-tree {
            background: white;
            border: 2px solid #3498db;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        }
        
        .read-type {
            padding: 0.6rem 1.2rem;
            border-radius: 25px;
            font-weight: bold;
            margin: 0.3rem;
            display: inline-block;
            text-align: center;
            min-width: 120px;
        }
        
        .spliced {
            background: #d4edda;
            color: #155724;
            border: 2px solid #28a745;
        }
        
        .unspliced {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffc107;
        }
        
        .ambiguous {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #dc3545;
        }
        
        .feature-type {
            padding: 0.4rem 0.8rem;
            border-radius: 5px;
            font-weight: bold;
            margin: 0.2rem;
            display: inline-block;
        }
        
        .exon {
            background: #28a745;
            color: white;
        }
        
        .intron {
            background: #6c757d;
            color: white;
        }
        
        .validated {
            background: #17a2b8;
            color: white;
        }
        
        .conclusion {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            margin: 2rem 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .conclusion h2 {
            margin-top: 0;
            font-size: 2rem;
        }
        
        .key-point {
            background: rgba(255,255,255,0.2);
            padding: 1.2rem;
            border-radius: 8px;
            margin: 1.2rem 0;
            text-align: left;
        }
        
        .footer {
            text-align: center;
            padding: 2rem;
            background: #2c3e50;
            color: #ecf0f1;
            margin-top: 2rem;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }
            
            .header {
                padding: 1.5rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧬 From BAM to Counting</h1>
            <p>Understanding the Velocyto Processing Pipeline for RNA Velocity Analysis</p>
        </div>
        
        <div class="content">
            <!-- Table of Contents -->
            <div class="toc">
                <h3>📋 Table of Contents</h3>
                <ul>
                    <li><a href="#introduction">1. Introduction and Overview</a></li>
                    <li><a href="#bam-structure">2. BAM File Structure and Content</a></li>
                    <li><a href="#cigar-parsing">3. CIGAR Parsing for Splicing Detection</a></li>
                    <li><a href="#gene-annotation">4. Gene Annotation and Transcript Models</a></li>
                    <li><a href="#feature-assignment">5. Feature Assignment Process</a></li>
                    <li><a href="#read-classification">6. Read Classification Logic</a></li>
                    <li><a href="#intron-validation">7. Intron Validation Process</a></li>
                    <li><a href="#counting-process">8. Counting Process and Matrices</a></li>
                    <li><a href="#conclusion">9. Conclusion</a></li>
                </ul>
            </div>

            <!-- Section 1: Introduction -->
            <div class="section" id="introduction">
                <h2>1. Introduction and Overview</h2>
                
                <p>RNA velocity analysis requires precise quantification of spliced and unspliced RNA molecules to estimate the direction and speed of gene expression changes. The velocyto pipeline transforms sequencing data in BAM format into count matrices that distinguish between mature mRNA (spliced) and pre-mRNA (unspliced).</p>
                
                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>BAM Input:</strong> Aligned sequencing reads with positional and CIGAR information
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>Annotation Integration:</strong> Gene models and transcript structures from GTF files
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div>
                        <strong>Read Classification:</strong> Determining if reads represent spliced or unspliced transcripts
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="step-number">4</div>
                    <div>
                        <strong>Counting:</strong> Generating spliced, unspliced, and ambiguous count matrices
                    </div>
                </div>
                
                <h3>1.1 Why This Process Matters</h3>
                <p>The ratio of unspliced to spliced reads provides the foundation for RNA velocity calculations:</p>
                
                <div class="math-box">
                    <p><strong>RNA Velocity Principle:</strong></p>
                    $$\frac{du}{dt} = \alpha - \beta u$$
                    $$\frac{ds}{dt} = \beta u - \gamma s$$
                    
                    <p>Where:</p>
                    <ul>
                        <li>$u$ = unspliced mRNA abundance</li>
                        <li>$s$ = spliced mRNA abundance</li>
                        <li>$\alpha$ = transcription rate</li>
                        <li>$\beta$ = splicing rate</li>
                        <li>$\gamma$ = degradation rate</li>
                    </ul>
                </div>
                
                <div class="info-box">
                    <strong>🎯 Key Insight:</strong> Accurate read classification is crucial because the unspliced/spliced ratio directly determines velocity estimates. Misclassified reads lead to incorrect velocity vectors and erroneous trajectory inference.
                </div>
            </div>

            <!-- Section 2: BAM Structure -->
            <div class="section" id="bam-structure">
                <h2>2. BAM File Structure and Content</h2>
                
                <p>BAM (Binary Alignment Map) files contain aligned sequencing reads with rich information about their mapping to the reference genome. Each read alignment includes several critical fields for splicing analysis.</p>
                
                <h3>2.1 Essential BAM Fields</h3>
                
                <div class="svg-container">
                    <svg width="1000" height="600" viewBox="0 0 1000 600">
                        <!-- Background -->
                        <rect width="1000" height="600" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
                        
                        <!-- Title -->
                        <text x="500" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">BAM Alignment Record Structure</text>
                        
                        <!-- BAM Record Box -->
                        <rect x="100" y="60" width="800" height="480" fill="white" stroke="#ccc" stroke-width="2" rx="10"/>
                        
                        <!-- Chromosome -->
                        <rect x="150" y="100" width="200" height="40" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
                        <text x="250" y="125" text-anchor="middle" font-size="14" font-weight="bold" fill="#1976d2">Chromosome (RNAME)</text>
                        <text x="250" y="145" text-anchor="middle" font-size="12" fill="#000">e.g., chr1, chrX</text>
                        
                        <!-- Position -->
                        <rect x="400" y="100" width="200" height="40" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
                        <text x="500" y="125" text-anchor="middle" font-size="14" font-weight="bold" fill="#1976d2">Position (POS)</text>
                        <text x="500" y="145" text-anchor="middle" font-size="12" fill="#000">1-based genomic position</text>
                        
                        <!-- Strand -->
                        <rect x="650" y="100" width="200" height="40" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
                        <text x="750" y="125" text-anchor="middle" font-size="14" font-weight="bold" fill="#1976d2">Strand (FLAG)</text>
                        <text x="750" y="145" text-anchor="middle" font-size="12" fill="#000">+ (forward) or - (reverse)</text>
                        
                        <!-- CIGAR -->
                        <rect x="150" y="180" width="700" height="60" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="500" y="205" text-anchor="middle" font-size="14" font-weight="bold" fill="#f57c00">CIGAR String</text>
                        <text x="500" y="225" text-anchor="middle" font-size="12" fill="#000">Compact Idiosyncratic Gapped Alignment Report</text>
                        <text x="500" y="240" text-anchor="middle" font-size="12" fill="#000">e.g., 50M1000N50M (spliced alignment)</text>
                        
                        <!-- Cell Barcode -->
                        <rect x="150" y="280" width="200" height="50" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="250" y="305" text-anchor="middle" font-size="14" font-weight="bold" fill="#4caf50">Cell Barcode (CB)</text>
                        <text x="250" y="325" text-anchor="middle" font-size="12" fill="#000">Identifies cell of origin</text>
                        
                        <!-- UMI -->
                        <rect x="400" y="280" width="200" height="50" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="500" y="305" text-anchor="middle" font-size="14" font-weight="bold" fill="#4caf50">UMI (UB)</text>
                        <text x="500" y="325" text-anchor="middle" font-size="12" fill="#000">Unique Molecular Identifier</text>
                        
                        <!-- NH Tag -->
                        <rect x="650" y="280" width="200" height="50" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="750" y="305" text-anchor="middle" font-size="14" font-weight="bold" fill="#4caf50">NH Tag</text>
                        <text x="750" y="325" text-anchor="middle" font-size="12" fill="#000">Number of alignments</text>
                        
                        <!-- Segments Visualization -->
                        <text x="150" y="380" font-size="14" font-weight="bold" fill="#2c3e50">Alignment Segments:</text>
                        
                        <!-- Reference Genome -->
                        <text x="150" y="410" font-size="12" fill="#000">Reference Genome:</text>
                        <line x1="150" y1="420" x2="800" y2="420" stroke="#000" stroke-width="2"/>
                        
                        <!-- Spliced Alignment Example -->
                        <rect x="200" y="440" width="100" height="20" fill="#28a745" rx="3"/>
                        <text x="250" y="455" text-anchor="middle" font-size="10" fill="white">Segment 1</text>
                        
                        <line x1="300" y1="450" x2="500" y2="450" stroke="#6c757d" stroke-width="2" stroke-dasharray="5,5"/>
                        <text x="400" y="440" text-anchor="middle" font-size="10" fill="#000">Intron (1000N)</text>
                        
                        <rect x="500" y="440" width="100" height="20" fill="#28a745" rx="3"/>
                        <text x="550" y="455" text-anchor="middle" font-size="10" fill="white">Segment 2</text>
                        
                        <text x="350" y="480" text-anchor="middle" font-size="12" fill="#28a745">CIGAR: 100M1000N100M</text>
                        <text x="350" y="495" text-anchor="middle" font-size="12" fill="#28a745">is_spliced: True</text>
                        
                        <!-- Unspliced Alignment Example -->
                        <rect x="200" y="520" width="400" height="20" fill="#6c757d" rx="3"/>
                        <text x="400" y="535" text-anchor="middle" font-size="10" fill="white">Continuous Alignment</text>
                        
                        <text x="400" y="555" text-anchor="middle" font-size="12" fill="#6c757d">CIGAR: 400M</text>
                        <text x="400" y="570" text-anchor="middle" font-size="12" fill="#6c757d">is_spliced: False</text>
                    </svg>
                </div>
                
                <h3>2.2 Key BAM Tags for Velocyto</h3>
                
                <table style="width:100%; border-collapse: collapse; margin: 1.5rem 0;">
                    <thead>
                        <tr style="background-color: #3498db; color: white;">
                            <th style="padding: 1rem; text-align: left;">Tag</th>
                            <th style="padding: 1rem; text-align: left;">Description</th>
                            <th style="padding: 1rem; text-align: left;">Usage in Velocyto</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="background-color: #f8f9fa;">
                            <td style="padding: 1rem; border-bottom: 1px solid #ddd;"><strong>CB</strong></td>
                            <td style="padding: 1rem; border-bottom: 1px solid #ddd;">Cell Barcode</td>
                            <td style="padding: 1rem; border-bottom: 1px solid #ddd;">Identifies which cell the read originated from</td>
                        </tr>
                        <tr>
                        
                        <rect x="200" y="250" width="200" height="40" fill="#e8f5e8" stroke="#c8e6c9" stroke-width="1"/>
                        <text x="300" y="275" text-anchor="middle" font-size="14" fill="#4caf50">BAM_CINS</text>
                        
                        <rect x="400" y="250" width="300" height="40" fill="#e8f5e8" stroke="#c8e6c9" stroke-width="1"/>
                        <text x="550" y="275" text-anchor="middle" font-size="14" fill="#4caf50">Insertion</text>
                        
                        <rect x="700" y="250" width="300" height="40" fill="#e8f5e8" stroke="#c8e6c9" stroke-width="1"/>
                        <text x="850" y="275" text-anchor="middle" font-size="14" fill="#4caf50">Read sequence insertion</text>
                        
                        <!-- S Row -->
                        <rect x="100" y="290" width="100" height="40" fill="#fce4ec" stroke="#f8bbd0" stroke-width="1"/>
                        <text x="150" y="315" text-anchor="middle" font-size="14" fill="#c2185b">S</text>
                        
                        <rect x="200" y="290" width="200" height="40" fill="#fce4ec" stroke="#f8bbd0" stroke-width="1"/>
                        <text x="300" y="315" text-anchor="middle" font-size="14" fill="#c2185b">BAM_CSOFT_CLIP</text>
                        
                        <rect x="400" y="290" width="300" height="40" fill="#fce4ec" stroke="#f8bbd0" stroke-width="1"/>
                        <text x="550" y="315" text-anchor="middle" font-size="14" fill="#c2185b">Soft clipping</text>
                        
                        <rect x="700" y="290" width="300" height="40" fill="#fce4ec" stroke="#f8bbd0" stroke-width="1"/>
                        <text x="850" y="315" text-anchor="middle" font-size="14" fill="#c2185b">Unaligned read ends</text>
                        
                        <!-- Spliced Read Visualization -->
                        <text x="100" y="370" font-size="16" font-weight="bold" fill="#2c3e50">Spliced Read Example:</text>
                        
                        <!-- Reference Genome -->
                        <text x="100" y="400" font-size="12" fill="#666">Reference Genome:</text>
                        <line x1="100" y1="410" x2="1100" y2="410" stroke="#666" stroke-width="2"/>
                        
                        <!-- Exon 1 -->
                        <rect x="200" y="400" width="200" height="30" fill="#28a745" rx="5"/>
                        <text x="300" y="420" text-anchor="middle" font-size="12" fill="white">Exon 1</text>
                        
                        <!-- Intron -->
                        <rect x="400" y="400" width="300" height="30" fill="#6c757d" rx="5"/>
                        <text x="550" y="420" text-anchor="middle" font-size="12" fill="white">Intron (1000 bp)</text>
                        
                        <!-- Exon 2 -->
                        <rect x="700" y="400" width="200" height="30" fill="#28a745" rx="5"/>
                        <text x="800" y="420" text-anchor="middle" font-size="12" fill="white">Exon 2</text>
                        
                        <!-- Aligned Read Segments -->
                        <text x="100" y="470" font-size="12" fill="#007bff">Read Alignment (CIGAR: 100M1000N100M):</text>
                        
                        <rect x="220" y="480" width="160" height="20" fill="#007bff" rx="3"/>
                        <text x="300" y="495" text-anchor="middle" font-size="10" fill="white">100M (Segment 1)</text>
                        
                        <line x1="380" y1="490" x2="700" y2="490" stroke="#007bff" stroke-width="2" stroke-dasharray="5,5"/>
                        <text x="540" y="480" text-anchor="middle" font-size="10" fill="#007bff">1000N (Skipped Region)</text>
                        
                        <rect x="700" y="480" width="160" height="20" fill="#007bff" rx="3"/>
                        <text x="780" y="495" text-anchor="middle" font-size="10" fill="white">100M (Segment 2)</text>
                        
                        <text x="550" y="520" text-anchor="middle" font-size="14" fill="#007bff">
                            <tspan font-weight="bold">ref_skipped = True</tspan> → <tspan font-weight="bold">is_spliced = True</tspan>
                        </text>
                        
                        <!-- Unspliced Read Visualization -->
                        <text x="100" y="570" font-size="16" font-weight="bold" fill="#2c3e50">Unspliced Read Example:</text>
                        
                        <text x="100" y="600" font-size="12" fill="#6c757d">Reference Genome (intron region):</text>
                        <line x1="100" y1="610" x2="1100" y2="610" stroke="#666" stroke-width="2"/>
                        <rect x="400" y="605" width="300" height="10" fill="#6c757d" rx="2"/>
                        
                        <text x="100" y="640" font-size="12" fill="#6c757d">Read Alignment (CIGAR: 400M):</text>
                        <rect x="350" y="650" width="400" height="20" fill="#6c757d" rx="3"/>
                        <text x="550" y="665" text-anchor="middle" font-size="10" fill="white">400M (Continuous Alignment)</text>
                        
                        <text x="550" y="690" text-anchor="middle" font-size="14" fill="#6c757d">
<!-- Section 4: Gene Annotation -->
            <div class="section" id="gene-annotation">
                <h2>4. Gene Annotation and Transcript Models</h2>
                
                <p>Velocyto requires detailed gene annotations to classify reads accurately. These annotations come from GTF (Gene Transfer Format) files that describe gene structures.</p>
                
                <h3>4.1 GTF File Structure</h3>
                
                <div class="gene-model">
                    <strong>Example GTF Entry:</strong><br>
                    chr1 &nbsp;&nbsp; HAVANA &nbsp;&nbsp; gene &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 11869 &nbsp; 14409 &nbsp; . &nbsp; + &nbsp; . &nbsp; gene_id "ENSG00000223972"; gene_type "transcribed_unprocessed_pseudogene";<br>
                    chr1 &nbsp;&nbsp; HAVANA &nbsp;&nbsp; transcript &nbsp; 11869 &nbsp; 14409 &nbsp; . &nbsp; + &nbsp; . &nbsp; gene_id "ENSG00000223972"; transcript_id "ENST00000456328";<br>
                    chr1 &nbsp;&nbsp; HAVANA &nbsp;&nbsp; exon &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 11869 &nbsp; 12227 &nbsp; . &nbsp; + &nbsp; . &nbsp; gene_id "ENSG00000223972"; transcript_id "ENST00000456328"; exon_number "1";<br>
                    chr1 &nbsp;&nbsp; HAVANA &nbsp;&nbsp; exon &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 12613 &nbsp; 12721 &nbsp; . &nbsp; + &nbsp; . &nbsp; gene_id "ENSG00000223972"; transcript_id "ENST00000456328"; exon_number "2";<br>
                    chr1 &nbsp;&nbsp; HAVANA &nbsp;&nbsp; exon &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 13221 &nbsp; 14409 &nbsp; . &nbsp; + &nbsp; . &nbsp; gene_id "ENSG00000223972"; transcript_id "ENST00000456328"; exon_number "3";
                </div>
                
                <h3>4.2 Transcript Model Construction</h3>
                
                <div class="svg-container">
                    <svg width="1200" height="600" viewBox="0 0 1200 600">
                        <!-- Background -->
                        <rect width="1200" height="600" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
                        
                        <!-- Title -->
                        <text x="600" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Transcript Model Construction from GTF</text>
                        
                        <!-- GTF Parsing Process -->
                        <text x="50" y="70" font-size="14" font-weight="bold" fill="#2c3e50">1. GTF Parsing:</text>
                        
                        <!-- GTF Entries -->
                        <rect x="50" y="90" width="1100" height="100" fill="white" stroke="#ccc" stroke-width="1" rx="5"/>
                        
                        <text x="70" y="115" font-size="12" fill="#666">gene &nbsp;&nbsp;&nbsp;&nbsp;chr1 1000 5000 + gene_id "GENE001"</text>
                        <text x="70" y="135" font-size="12" fill="#666">transcript chr1 1000 5000 + gene_id "GENE001"; transcript_id "TRANS001"</text>
                        <text x="70" y="155" font-size="12" fill="#666">exon &nbsp;&nbsp;&nbsp;&nbsp;chr1 1000 1200 + gene_id "GENE001"; transcript_id "TRANS001"; exon_number "1"</text>
                        <text x="70" y="175" font-size="12" fill="#666">exon &nbsp;&nbsp;&nbsp;&nbsp;chr1 2000 2500 + gene_id "GENE001"; transcript_id "TRANS001"; exon_number "2"</text>
                        
                        <!-- Parsing Arrow -->
                        <path d="M 600 200 L 600 240" stroke="#666" stroke-width="3" marker-end="url(#arrow1)"/>
                        
                        <!-- Transcript Model Creation -->
                        <text x="50" y="270" font-size="14" font-weight="bold" fill="#2c3e50">2. Transcript Model:</text>
                        
                        <rect x="50" y="290" width="1100" height="120" fill="white" stroke="#ccc" stroke-width="1" rx="5"/>
                        
                        <!-- Transcript Model Visualization -->
                        <text x="100" y="320" font-size="12" font-weight="bold" fill="#2c3e50">TranscriptModel (TRANS001):</text>
                        
                        <!-- Exons -->
                        <rect x="200" y="340" width="80" height="30" fill="#28a745" stroke="#155724" stroke-width="2" rx="5"/>
                        <text x="240" y="360" text-anchor="middle" font-size="10" fill="white">Exon 1</text>
                        
                        <rect x="350" y="340" width="80" height="30" fill="#28a745" stroke="#155724" stroke-width="2" rx="5"/>
                        <text x="390" y="360" text-anchor="middle" font-size="10" fill="white">Exon 2</text>
                        
                        <!-- Introns -->
                        <line x1="280" y1="355" x2="350" y2="355" stroke="#6c757d" stroke-width="3"/>
                        <text x="315" y="350" text-anchor="middle" font-size="10" fill="#6c757d">Intron</text>
                        
                        <!-- Features -->
                        <text x="100" y="390" font-size="12" font-weight="bold" fill="#2c3e50">Features:</text>
                        
                        <rect x="200" y="400" width="15" height="15" fill="#28a745"/>
                        <text x="225" y="412" font-size="10" fill="#666">Exon Feature (kind=101)</text>
                        
                        <rect x="350" y="400" width="15" height="15" fill="#6c757d"/>
                        <text x="375" y="412" font-size="10" fill="#666">Intron Feature (kind=105)</text>
                        
                        <!-- Deriving Introns -->
                        <text x="50" y="460" font-size="14" font-weight="bold" fill="#2c3e50">3. Intron Derivation:</text>
                        
                        <rect x="50" y="480" width="1100" height="80" fill="white" stroke="#ccc" stroke-width="1" rx="5"/>
                        
                        <text x="100" y="510" font-size="12" fill="#666">From Exon 1 (1000-1200) and Exon 2 (2000-2500):</text>
                        <text x="100" y="530" font-size="12" fill="#666">Derived Intron Feature: start=1201, end=1999, kind=105, is_validated=false</text>
                        
                        <!-- Arrow markers -->
                        <defs>
                            <marker id="arrow1" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto" markerUnits="strokeWidth">
                                <path d="M0,0 L0,6 L9,3 z" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>
                
                <h3>4.3 Feature Types in Velocyto</h3>
                
                <div class="workflow-step">
                    <div class="feature-type exon">EXON</div>
                    <div>
                        <strong>Kind Code: 101 ('e')</strong> - Genomic regions that code for parts of the mature mRNA
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="feature-type intron">INTRON</div>
                    <div>
                        <strong>Kind Code: 105 ('i')</strong> - Genomic regions that are removed during splicing
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="feature-type validated">VALIDATED INTRON</div>
                    <div>
                        <strong>is_validated: true</strong> - Introns confirmed by reads spanning exon-intron boundaries
                    </div>
                </div>
                
                <div class="code-box">
// Pseudocode for TranscriptModel construction
class TranscriptModel:
    constructor(trid, geneid, chromstrand):
        this.trid = trid          // Transcript ID
        this.geneid = geneid      // Gene ID
        this.chromstrand = chromstrand  // Chromosome and strand
        this.exons = []           // List of exon features
        this.introns = []         // List of intron features
    
    function append_exon(feature):
        this.exons.append(feature)
        // Automatically derive introns between consecutive exons
        if len(this.exons) > 1:
            prev_exon = this.exons[-2]
            curr_exon = this.exons[-1]
            intron_start = prev_exon.end + 1
            intron_end = curr_exon.start - 1
            intron_feature = Feature(
                start=intron_start,
                end=intron_end,
                kind=105,  // 'i'
                is_validated=false
            )
            this.introns.append(intron_feature)
                </div>
                
                <div class="math-box">
                    <p><strong>Feature Representation:</strong></p>
                    <p>Each genomic feature is represented as:</p>
                    $$\text{Feature} = \{start, end, kind, is\_validated\}$$
<!-- Section 5: Feature Assignment -->
            <div class="section" id="feature-assignment">
                <h2>5. Feature Assignment Process</h2>
                
                <p>After parsing both the BAM file and gene annotations, velocyto assigns each read to genomic features through overlap detection.</p>
                
                <h3>5.1 Read-to-Feature Mapping</h3>
                
                <div class="svg-container">
                    <svg width="1200" height="700" viewBox="0 0 1200 700">
                        <!-- Background -->
                        <rect width="1200" height="700" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
                        
                        <!-- Title -->
                        <text x="600" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Read-to-Feature Assignment Process</text>
                        
                        <!-- Gene Model -->
                        <text x="100" y="80" font-size="14" font-weight="bold" fill="#2c3e50">Gene Model:</text>
                        
                        <!-- Exons -->
                        <rect x="200" y="100" width="100" height="30" fill="#28a745" stroke="#155724" stroke-width="2" rx="5"/>
                        <text x="250" y="120" text-anchor="middle" font-size="12" fill="white">Exon 1</text>
                        
                        <rect x="350" y="100" width="100" height="30" fill="#28a745" stroke="#155724" stroke-width="2" rx="5"/>
                        <text x="400" y="120" text-anchor="middle" font-size="12" fill="white">Exon 2</text>
                        
                        <rect x="500" y="100" width="100" height="30" fill="#28a745" stroke="#155724" stroke-width="2" rx="5"/>
                        <text x="550" y="120" text-anchor="middle" font-size="12" fill="white">Exon 3</text>
                        
                        <!-- Introns -->
                        <line x1="300" y1="115" x2="350" y2="115" stroke="#6c757d" stroke-width="3"/>
                        <text x="325" y="110" text-anchor="middle" font-size="10" fill="#6c757d">Intron 1</text>
                        
                        <line x1="450" y1="115" x2="500" y2="115" stroke="#6c757d" stroke-width="3"/>
                        <text x="475" y="110" text-anchor="middle" font-size="10" fill="#6c757d">Intron 2</text>
                        
                        <!-- Read Examples -->
                        <text x="100" y="180" font-size="14" font-weight="bold" fill="#2c3e50">Read Assignment Examples:</text>
                        
                        <!-- Example 1: Exon-only read -->
                        <rect x="150" y="200" width="300" height="80" fill="white" stroke="#ccc" stroke-width="1" rx="5"/>
                        <text x="170" y="220" font-size="12" font-weight="bold" fill="#28a745">Example 1: Exon-only Read</text>
                        
                        <rect x="220" y="240" width="60" height="15" fill="#28a745" stroke="#155724" stroke-width="1"/>
                        <text x="250" y="252" text-anchor="middle" font-size="10" fill="white">Read</text>
                        
                        <text x="300" y="252" font-size="12" fill="#666">→ Overlaps Exon 1 only</text>
                        <text x="300" y="270" font-size="12" fill="#666">→ Assigned to Exon 1 feature</text>
                        
                        <!-- Example 2: Intron-overlapping read -->
                        <rect x="500" y="200" width="300" height="80" fill="white" stroke="#ccc" stroke-width="1" rx="5"/>
                        <text x="520" y="220" font-size="12" font-weight="bold" fill="#6c757d">Example 2: Intron-overlapping Read</text>
                        
                        <rect x="550" y="240" width="60" height="15" fill="#6c757d" stroke="#495057" stroke-width="1"/>
                        <text x="580" y="252" text-anchor="middle" font-size="10" fill="white">Read</text>
                        
                        <text x="630" y="252" font-size="12" fill="#666">→ Overlaps Intron 1</text>
                        <text x="630" y="270" font-size="12" fill="#666">→ Assigned to Intron 1 feature</text>
                        
                        <!-- Example 3: Spanning read -->
                        <rect x="150" y="300" width="300" height="100" fill="white" stroke="#ccc" stroke-width="1" rx="5"/>
                        <text x="170" y="320" font-size="12" font-weight="bold" fill="#ffc107">Example 3: Exon-intron Spanning Read</text>
                        
                        <rect x="200" y="350" width="40" height="15" fill="#28a745" stroke="#155724" stroke-width="1"/>
                        <rect x="240" y="350" width="40" height="15" fill="#6c757d" stroke="#495057" stroke-width="1"/>
                        <rect x="280" y="350" width="40" height="15" fill="#28a745" stroke="#155724" stroke-width="1"/>
                        <text x="260" y="370" text-anchor="middle" font-size="10" fill="#666">Spanning Read</text>
                        
                        <text x="350" y="360" font-size="12" fill="#666">→ Spans Exon 1 - Intron 1 - Exon 2 boundary</text>
                        <text x="350" y="380" font-size="12" fill="#666">→ Assigned to multiple features with span indicator</text>
                        
                        <!-- Example 4: Multi-exon read -->
                        <rect x="500" y="300" width="300" height="100" fill="white" stroke="#ccc" stroke-width="1" rx="5"/>
                        <text x="520" y="320" font-size="12" font-weight="bold" fill="#17a2b8">Example 4: Multi-exon Read</text>
                        
                        <rect x="550" y="350" width="180" height="15" fill="#28a745" stroke="#155724" stroke-width="1"/>
                        <text x="640" y="362" text-anchor="middle" font-size="10" fill="white">Read</text>
                        
                        <text x="750" y="360" font-size="12" fill="#666">→ Overlaps Exon 1 and Exon 2</text>
                        <text x="750" y="380" font-size="12" fill="#666">→ Assigned to both exon features</text>
                        
                        <!-- Feature Index Structure -->
                        <text x="100" y="440" font-size="14" font-weight="bold" fill="#2c3e50">5.2 Feature Index for Efficient Lookup:</text>
                        
                        <rect x="100" y="460" width="1000" height="200" fill="white" stroke="#ccc" stroke-width="1" rx="5"/>
                        
                        <!-- Feature Index Visualization -->
                        <text x="150" y="490" font-size="12" font-weight="bold" fill="#2c3e50">FeatureIndex Structure:</text>
                        
                        <text x="150" y="520" font-size="12" fill="#666">Chromosome 1 (+):</text>
                        
                        <!-- Sorted Features -->
                        <rect x="150" y="540" width="800" height="40" fill="#f1f3f5" stroke="#ccc" stroke-width="1"/>
                        
                        <rect x="160" y="545" width="60" height="30" fill="#28a745" stroke="#155724" stroke-width="1" rx="3"/>
                        <text x="190" y="565" text-anchor="middle" font-size="10" fill="white">Exon1</text>
                        
                        <rect x="250" y="545" width="80" height="30" fill="#6c757d" stroke="#495057" stroke-width="1" rx="3"/>
                        <text x="290" y="565" text-anchor="middle" font-size="10" fill="white">Intron1</text>
                        
                        <rect x="360" y="545" width="60" height="30" fill="#28a745" stroke="#155724" stroke-width="1" rx="3"/>
                        <text x="390" y="565" text-anchor="middle" font-size="10" fill="white">Exon2</text>
                        
                        <rect x="450" y="545" width="80" height="30" fill="#6c757d" stroke="#495057" stroke-width="1" rx="3"/>
                        <text x="490" y="565" text-anchor="middle" font-size="10" fill="white">Intron2</text>
                        
                        <rect x="560" y="545" width="60" height="30" fill="#28a745" stroke="#155724" stroke-width="1" rx="3"/>
                        <text x="590" y="565" text-anchor="middle" font-size="10" fill="white">Exon3</text>
                        
                        <!-- Overlap Detection -->
                        <text x="150" y="600" font-size="12" fill="#666">Read segments: [(220, 279)]</text>
                        <text x="150" y="620" font-size="12" fill="#666">Overlapping features: Exon1 (200-300)</text>
                        <text x="150" y="640" font-size="12" fill="#666">Result: MappingsRecord with Exon1 feature</text>
                    </svg>
                </div>
                
                <h3>5.3 MappingsRecord Structure</h3>
                
                <div class="code-box">
// Pseudocode for mappings record structure
class SegmentMatch:
    constructor(segment, feature, is_spliced):
        this.segment = segment      // (start, end) of read segment
        this.feature = feature      // Feature object (exon/intron)
        this.is_spliced = is_spliced  // Whether segment spans splice junction

class MappingsRecord:
    constructor():
        this.segment_matches = []   // List of SegmentMatch objects
        this.gene_id = null         // Associated gene
        this.transcript_model = null // Associated transcript
    
    function add_segment_match(segment_match):
        this.segment_matches.append(segment_match)
        
    function get_features():
        return [match.feature for match in this.segment_matches]
        
    function has_exon_features():
        return any(match.feature.kind == 101 for match in this.segment_matches)
<!-- Section 6: Read Classification -->
            <div class="section" id="read-classification">
                <h2>6. Read Classification Logic</h2>
                
                <p>With read-to-feature mappings established, velocyto applies sophisticated logic to classify each read as spliced, unspliced, or ambiguous.</p>
                
                <h3>6.1 Classification Decision Factors</h3>
                
                <div class="decision-tree">
                    <h4>Primary Classification Factors:</h4>
                    <ul>
                        <li><strong>Gene Mapping:</strong> Does the read map to one gene, multiple genes, or no genes?</li>
                        <li><strong>Feature Types:</strong> Does the read overlap exons, introns, or both?</li>
                        <li><strong>Intron Validation:</strong> Are the overlapping introns validated by spanning reads?</li>
                        <li><strong>Spanning Behavior:</strong> Does the read span exon-intron boundaries?</li>
                        <li><strong>Splice Junctions:</strong> Does the read contain splice junctions from the aligner?</li>
                    </ul>
                </div>
                
                <h3>6.2 Detailed Classification Rules</h3>
                
                <div class="workflow-step">
                    <div class="read-type ambiguous">AMBIGUOUS</div>
                    <div>
                        <strong>Multi-gene mapping:</strong> Read maps to multiple genes → Cannot determine origin
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="read-type ambiguous">AMBIGUOUS</div>
                    <div>
                        <strong>No gene mapping:</strong> Read doesn't map to any annotated gene → Intergenic
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="read-type spliced">SPLICED</div>
                    <div>
                        <strong>Exon-only mapping:</strong> Read maps only to exonic sequences → Mature mRNA
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="read-type unspliced">UNSPLICED</div>
                    <div>
                        <strong>Exon-intron spanning:</strong> Read spans exon-intron boundaries → Active splicing
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="read-type unspliced">UNSPLICED</div>
                    <div>
                        <strong>Validated intron:</strong> Read maps to intron validated by spanning reads → Pre-mRNA
                    </div>
                </div>
                
                <h3>6.3 Boolean Logic Variables</h3>
                
                <div class="code-box">
// Boolean variables used in classification logic

has_only_exon_model = false      // Read maps only to exons
has_only_intron_model = false    // Read maps only to introns
has_mixed_model = false          // Read maps to both exons and introns
has_validated_intron = false     // Read maps to validated intron
has_exon_intron_span = false     // Read spans exon-intron boundary
has_only_span_exon_intron_model = false  // All models span exon-intron boundaries
multi_gene = false               // Read maps to multiple genes

// Classification logic (simplified)
function classify_read(molitem, geneid2ix, spliced_matrix, unspliced_matrix, ambiguous_matrix):
    // Initialize classification variables based on mappings
    multi_gene = check_multi_gene_mapping(molitem)
    has_only_exon_model = check_exon_only_mapping(molitem)
    has_only_intron_model = check_intron_only_mapping(molitem)
    has_validated_intron = check_validated_intron_mapping(molitem)
    has_exon_intron_span = check_exon_intron_spanning(molitem)
    has_mixed_model = check_mixed_mapping(molitem)
    has_only_span_exon_intron_model = check_only_spanning_mapping(molitem)
    
    // Apply decision tree
    if multi_gene:
        return "AMBIGUOUS"  // Multi-gene mapping
    
    if not molitem.mappings_record:
        return "AMBIGUOUS"  // No gene mapping
    
    if has_only_exon_model and not has_only_intron_model and not has_mixed_model:
        // Exon-only mapping -> SPLICED
        gene_ix = geneid2ix[transcript_model.geneid]
        spliced_matrix[gene_ix, cell_bcidx] += 1
        return "SPLICED"
    
    if has_only_span_exon_intron_model:
        // Exon-intron spanning -> UNSPLICED
        gene_ix = geneid2ix[transcript_model.geneid]
        unspliced_matrix[gene_ix, cell_bcidx] += 1
        return "UNSPLICED"
    
    if has_only_intron_model and has_validated_intron:
        // Validated intron mapping -> UNSPLICED
        gene_ix = geneid2ix[transcript_model.geneid]
        unspliced_matrix[gene_ix, cell_bcidx] += 1
        return "UNSPLICED"
    
    // Additional complex cases...
    return "AMBIGUOUS"  // Default fallback
                </div>
                
                <h3>6.4 Complete Decision Tree</h3>
                
                <div class="svg-container">
                    <svg width="1200" height="800" viewBox="0 0 1200 800">
                        <!-- Background -->
                        <rect width="1200" height="800" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
                        
                        <!-- Title -->
                        <text x="600" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Velocyto Read Classification Decision Tree</text>
                        
                        <!-- Start Node -->
                        <ellipse cx="600" cy="80" rx="80" ry="30" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
                        <text x="600" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#1976d2">Input Read</text>
                        
                        <!-- First Decision: Multi-gene -->
                        <rect x="520" y="130" width="160" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="600" y="155" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">Multi-gene mapping?</text>
                        
                        <!-- Multi-gene YES -->
                        <rect x="100" y="200" width="120" height="40" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="5"/>
                        <text x="160" y="225" text-anchor="middle" font-size="11" font-weight="bold" fill="#721c24">AMBIGUOUS</text>
                        
                        <!-- Multi-gene NO -->
                        <rect x="520" y="200" width="160" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="600" y="225" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">No gene mapping?</text>
                        
                        <!-- No gene YES -->
                        <rect x="300" y="270" width="120" height="40" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="5"/>
                        <text x="360" y="295" text-anchor="middle" font-size="11" font-weight="bold" fill="#721c24">AMBIGUOUS</text>
                        
                        <!-- No gene NO - Continue classification -->
                        <rect x="520" y="270" width="160" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="600" y="295" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">Exon-only mapping?</text>
                        
                        <!-- Exon-only YES -->
                        <rect x="750" y="340" width="120" height="40" fill="#d4edda" stroke="#28a745" stroke-width="2" rx="5"/>
                        <text x="810" y="365" text-anchor="middle" font-size="11" font-weight="bold" fill="#155724">SPLICED</text>
                        
                        <!-- Exon-only NO -->
                        <rect x="520" y="340" width="160" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="600" y="365" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">Exon-intron span?</text>
                        
                        <!-- Span YES -->
                        <rect x="750" y="410" width="120" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5"/>
                        <text x="810" y="435" text-anchor="middle" font-size="11" font-weight="bold" fill="#856404">UNSPLICED</text>
                        
                        <!-- Span NO -->
                        <rect x="520" y="410" width="160" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="600" y="435" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">Validated intron?</text>
                        
                        <!-- Validated YES -->
                        <rect x="750" y="480" width="120" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5"/>
                        <text x="810" y="505" text-anchor="middle" font-size="11" font-weight="bold" fill="#856404">UNSPLICED</text>
                        
                        <!-- Validated NO -->
                        <rect x="520" y="480" width="160" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="600" y="505" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">Mixed model?</text>
                        
                        <!-- Mixed YES -->
                        <rect x="400" y="550" width="120" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5"/>
                        <text x="460" y="575" text-anchor="middle" font-size="11" font-weight="bold" fill="#856404">UNSPLICED</text>
                        
                        <!-- Mixed NO -->
                        <rect x="600" y="550" width="120" height="40" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="5"/>
                        <text x="660" y="575" text-anchor="middle" font-size="11" font-weight="bold" fill="#721c24">AMBIGUOUS</text>
                        
                        <!-- Decision paths -->
                        <path d="M 600 110 L 600 130" stroke="#666" stroke-width="2" marker-end="url(#arrow3)"/>
                        
                        <path d="M 550 150 L 160 200" stroke="#dc3545" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="300" y="175" text-anchor="middle" font-size="10" fill="#dc3545">YES</text>
                        
                        <path d="M 600 170 L 600 200" stroke="#666" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="620" y="185" font-size="10" fill="#666">NO</text>
                        
                        <path d="M 550 225 L 360 270" stroke="#dc3545" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="430" y="245" text-anchor="middle" font-size="10" fill="#dc3545">YES</text>
                        
                        <path d="M 600 240 L 600 270" stroke="#666" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="620" y="255" font-size="10" fill="#666">NO</text>
                        
                        <path d="M 650 295 L 810 340" stroke="#28a745" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="750" y="315" text-anchor="middle" font-size="10" fill="#28a745">YES</text>
                        
                        <path d="M 600 310 L 600 340" stroke="#666" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="620" y="325" font-size="10" fill="#666">NO</text>
<!-- Section 7: Intron Validation -->
            <div class="section" id="intron-validation">
                <h2>7. Intron Validation Process</h2>
                
                <p>To distinguish true introns from genomic DNA contamination or mapping artifacts, velocyto validates introns by requiring evidence of exon-intron spanning reads.</p>
                
                <h3>7.1 Validation Mechanism</h3>
                
                <div class="svg-container">
                    <svg width="1200" height="700" viewBox="0 0 1200 700">
                        <!-- Background -->
                        <rect width="1200" height="700" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
                        
                        <!-- Title -->
                        <text x="600" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Intron Validation Process</text>
                        
                        <!-- Gene Structure -->
                        <text x="100" y="80" font-size="14" font-weight="bold" fill="#2c3e50">Gene Structure with Intron:</text>
                        
                        <!-- Exon 1 -->
                        <rect x="200" y="100" width="150" height="40" fill="#28a745" stroke="#155724" stroke-width="2" rx="5"/>
                        <text x="275" y="125" text-anchor="middle" font-size="12" fill="white">Exon 1</text>
                        
                        <!-- Intron -->
                        <rect x="350" y="100" width="300" height="40" fill="#6c757d" stroke="#495057" stroke-width="2" rx="5"/>
                        <text x="500" y="125" text-anchor="middle" font-size="12" fill="white">Intron (Not Validated)</text>
                        
                        <!-- Exon 2 -->
                        <rect x="650" y="100" width="150" height="40" fill="#28a745" stroke="#155724" stroke-width="2" rx="5"/>
                        <text x="725" y="125" text-anchor="middle" font-size="12" fill="white">Exon 2</text>
                        
                        <!-- Non-spanning read (intronic) -->
                        <text x="100" y="180" font-size="14" font-weight="bold" fill="#6c757d">Non-validated Intron Read:</text>
                        
                        <rect x="400" y="200" width="100" height="20" fill="#6c757d" stroke="#495057" stroke-width="1"/>
                        <text x="450" y="215" text-anchor="middle" font-size="10" fill="white">Read</text>
                        
                        <text x="550" y="215" font-size="12" fill="#666">Maps to intron only</text>
                        <text x="550" y="235" font-size="12" fill="#dc3545">→ Classified as AMBIGUOUS (low confidence)</text>
                        
                        <!-- Spanning read -->
                        <text x="100" y="280" font-size="14" font-weight="bold" fill="#28a745">Exon-intron Spanning Read:</text>
                        
                        <rect x="250" y="300" width="80" height="20" fill="#28a745" stroke="#155724" stroke-width="1"/>
                        <rect x="330" y="300" width="80" height="20" fill="#6c757d" stroke="#495057" stroke-width="1"/>
                        <rect x="410" y="300" width="80" height="20" fill="#28a745" stroke="#155724" stroke-width="1"/>
                        <text x="370" y="330" text-anchor="middle" font-size="10" fill="#666">Spanning Read</text>
                        
                        <text x="550" y="315" font-size="12" fill="#666">Spans Exon 1 - Intron - Exon 2 boundary</text>
                        <text x="550" y="335" font-size="12" fill="#28a745">→ Validates the intron</text>
                        
                        <!-- After validation -->
                        <text x="100" y="390" font-size="14" font-weight="bold" fill="#17a2b8">After Intron Validation:</text>
                        
                        <!-- Exon 1 -->
                        <rect x="200" y="410" width="150" height="40" fill="#28a745" stroke="#155724" stroke-width="2" rx="5"/>
                        <text x="275" y="435" text-anchor="middle" font-size="12" fill="white">Exon 1</text>
                        
                        <!-- Validated Intron -->
                        <rect x="350" y="410" width="300" height="40" fill="#17a2b8" stroke="#117a8b" stroke-width="2" rx="5"/>
                        <text x="500" y="435" text-anchor="middle" font-size="12" fill="white">Intron (Validated)</text>
                        
                        <!-- Exon 2 -->
                        <rect x="650" y="410" width="150" height="40" fill="#28a745" stroke="#155724" stroke-width="2" rx="5"/>
                        <text x="725" y="435" text-anchor="middle" font-size="12" fill="white">Exon 2</text>
                        
                        <!-- Validated intron read -->
                        <text x="100" y="490" font-size="14" font-weight="bold" fill="#17a2b8">Validated Intron Read:</text>
                        
                        <rect x="400" y="510" width="100" height="20" fill="#17a2b8" stroke="#117a8b" stroke-width="1"/>
                        <text x="450" y="525" text-anchor="middle" font-size="10" fill="white">Read</text>
                        
                        <text x="550" y="525" font-size="12" fill="#666">Maps to validated intron</text>
                        <text x="550" y="545" font-size="12" fill="#17a2b8">→ Classified as UNSPLICED (high confidence)</text>
                        
                        <!-- Validation Process -->
                        <text x="100" y="590" font-size="14" font-weight="bold" fill="#2c3e50">Validation Process:</text>
                        
                        <rect x="100" y="610" width="1000" height="60" fill="white" stroke="#ccc" stroke-width="1" rx="5"/>
                        
                        <text x="120" y="635" font-size="12" fill="#666">1. Identify reads spanning exon-intron boundaries</text>
                        <text x="120" y="655" font-size="12" fill="#666">2. Mark corresponding introns as "validated"</text>
                        <text x="600" y="635" font-size="12" fill="#666">3. Only validated introns are used for unspliced read classification</text>
                        <text x="600" y="655" font-size="12" fill="#666">4. Non-validated intronic reads are classified as ambiguous</text>
                    </svg>
                </div>
                
                <h3>7.2 Validation Implementation</h3>
                
                <div class="code-box">
// Pseudocode for intron validation
function mark_overlapping_ivls(read, feature_index):
    // For each segment of the read
    for segment in read.segments:
        // Find overlapping features
        overlapping_features = feature_index.find_overlapping_features(segment)
        
        // For each overlapping feature
        for feature in overlapping_features:
            // If the read spans an exon-intron boundary
            if is_exon_intron_spanning_read(read, feature):
                // Mark the intron as validated
                feature.is_validated = true
                
    return overlapping_features

// Check if read spans exon-intron boundary
function is_exon_intron_spanning_read(read, feature):
    // A read spans a boundary if:
    // 1. It overlaps both an exon and an intron
    // 2. It has segments in both features
    // 3. The CIGAR indicates a splice junction at the boundary
    
    has_exon_overlap = false
    has_intron_overlap = false
    
    for segment in read.segments:
        if overlaps_exon(segment, feature):
            has_exon_overlap = true
        if overlaps_intron(segment, feature):
            has_intron_overlap = true
    
    return has_exon_overlap && has_intron_overlap && read.is_spliced
                </div>
                
                <div class="math-box">
                    <p><strong>Validation Logic:</strong></p>
                    <p>Let $R$ be a read with segments $S = \{s_1, s_2, ..., s_n\}$ and $F$ be a feature (intron).</p>
                    
                    $$\text{is_validated}(F) = \begin{cases} 
                    \text{True}, & \text{if } \exists R \text{ such that } R \text{ spans } F \text{ boundary} \\
                    \text{False}, & \text{otherwise}
                    \end{cases}$$
                    
                    <p>Where spanning is defined as:</p>
                    $$\text{spans_boundary}(R, F) = \text{overlaps_exon}(R) \land \text{overlaps_intron}(R) \land \text{is_spliced}(R)$$
                </div>
                
                <div class="info-box">
                    <strong>🛡️ Quality Control:</strong> Intron validation significantly reduces false positives from genomic DNA contamination and improves the accuracy of unspliced read classification.
                </div>
            </div>
                        
                        <path d="M 650 365 L 810 410" stroke="#ffc107" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="750" y="385" text-anchor="middle" font-size="10" fill="#ffc107">YES</text>
<!-- Section 8: Counting Process -->
            <div class="section" id="counting-process">
                <h2>8. Counting Process and Matrices</h2>
                
                <p>After classifying all reads, velocyto generates count matrices for spliced, unspliced, and ambiguous reads for each gene and cell.</p>
                
                <h3>8.1 Count Matrix Structure</h3>
                
                <div class="svg-container">
                    <svg width="1200" height="700" viewBox="0 0 1200 700">
                        <!-- Background -->
                        <rect width="1200" height="700" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
                        
                        <!-- Title -->
                        <text x="600" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Count Matrix Structure</text>
                        
                        <!-- Matrix Visualization -->
                        <text x="100" y="80" font-size="14" font-weight="bold" fill="#2c3e50">Spliced Count Matrix (G genes × C cells):</text>
                        
                        <!-- Matrix Grid -->
                        <rect x="100" y="100" width="500" height="300" fill="white" stroke="#ccc" stroke-width="2"/>
                        
                        <!-- Column Headers (Cells) -->
                        <rect x="150" y="100" width="50" height="30" fill="#d1ecf1" stroke="#17a2b8" stroke-width="1"/>
                        <text x="175" y="120" text-anchor="middle" font-size="10" fill="#17a2b8">Cell 1</text>
                        
                        <rect x="200" y="100" width="50" height="30" fill="#d1ecf1" stroke="#17a2b8" stroke-width="1"/>
                        <text x="225" y="120" text-anchor="middle" font-size="10" fill="#17a2b8">Cell 2</text>
                        
                        <rect x="250" y="100" width="50" height="30" fill="#d1ecf1" stroke="#17a2b8" stroke-width="1"/>
                        <text x="275" y="120" text-anchor="middle" font-size="10" fill="#17a2b8">Cell 3</text>
                        
                        <rect x="300" y="100" width="50" height="30" fill="#d1ecf1" stroke="#17a2b8" stroke-width="1"/>
                        <text x="325" y="120" text-anchor="middle" font-size="10" fill="#17a2b8">...</text>
                        
                        <rect x="350" y="100" width="50" height="30" fill="#d1ecf1" stroke="#17a2b8" stroke-width="1"/>
                        <text x="375" y="120" text-anchor="middle" font-size="10" fill="#17a2b8">Cell C</text>
                        
                        <!-- Row Headers (Genes) -->
                        <rect x="100" y="130" width="50" height="40" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                        <text x="125" y="150" text-anchor="middle" font-size="10" fill="#28a745">Gene 1</text>
                        
                        <rect x="100" y="170" width="50" height="40" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                        <text x="125" y="190" text-anchor="middle" font-size="10" fill="#28a745">Gene 2</text>
                        
                        <rect x="100" y="210" width="50" height="40" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                        <text x="125" y="230" text-anchor="middle" font-size="10" fill="#28a745">Gene 3</text>
                        
                        <rect x="100" y="250" width="50" height="40" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                        <text x="125" y="270" text-anchor="middle" font-size="10" fill="#28a745">...</text>
                        
                        <rect x="100" y="290" width="50" height="40" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                        <text x="125" y="310" text-anchor="middle" font-size="10" fill="#28a745">Gene G</text>
                        
                        <!-- Matrix Values -->
                        <rect x="150" y="130" width="50" height="40" fill="#fff" stroke="#ddd" stroke-width="1"/>
                        <text x="175" y="155" text-anchor="middle" font-size="12" fill="#333">15</text>
                        
                        <rect x="200" y="130" width="50" height="40" fill="#fff" stroke="#ddd" stroke-width="1"/>
                        <text x="225" y="155" text-anchor="middle" font-size="12" fill="#333">8</text>
                        
                        <rect x="250" y="130" width="50" height="40" fill="#fff" stroke="#ddd" stroke-width="1"/>
                        <text x="275" y="155" text-anchor="middle" font-size="12" fill="#333">22</text>
                        
                        <rect x="350" y="130" width="50" height="40" fill="#fff" stroke="#ddd" stroke-width="1"/>
                        <text x="375" y="155" text-anchor="middle" font-size="12" fill="#333">5</text>
                        
                        <rect x="150" y="170" width="50" height="40" fill="#fff" stroke="#ddd" stroke-width="1"/>
                        <text x="175" y="195" text-anchor="middle" font-size="12" fill="#333">3</text>
                        
                        <rect x="200" y="170" width="50" height="40" fill="#fff" stroke="#ddd" stroke-width="1"/>
                        <text x="225" y="195" text-anchor="middle" font-size="12" fill="#333">12</text>
                        
                        <rect x="350" y="170" width="50" height="40" fill="#fff" stroke="#ddd" stroke-width="1"/>
                        <text x="375" y="195" text-anchor="middle" font-size="12" fill="#333">7</text>
                        
                        <rect x="150" y="290" width="50" height="40" fill="#fff" stroke="#ddd" stroke-width="1"/>
                        <text x="175" y="315" text-anchor="middle" font-size="12" fill="#333">9</text>
                        
                        <rect x="350" y="290" width="50" height="40" fill="#fff" stroke="#ddd" stroke-width="1"/>
                        <text x="375" y="315" text-anchor="middle" font-size="12" fill="#333">14</text>
                        
                        <!-- Matrix Legend -->
                        <rect x="700" y="100" width="300" height="150" fill="white" stroke="#ccc" stroke-width="1" rx="5"/>
                        <text x="850" y="125" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Matrix Legend</text>
                        
                        <rect x="720" y="140" width="20" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                        <text x="740" y="155" font-size="12" fill="#666">Rows: Genes (G)</text>
                        
                        <rect x="850" y="140" width="20" height="20" fill="#d1ecf1" stroke="#17a2b8" stroke-width="1"/>
                        <text x="870" y="155" font-size="12" fill="#666">Columns: Cells (C)</text>
                        
                        <text x="720" y="180" font-size="12" fill="#666">Values: Read counts</text>
                        <text x="720" y="200" font-size="12" fill="#666">Example: Gene 1 in Cell 1 has 15 spliced reads</text>
                        
                        <!-- All Three Matrices -->
                        <text x="100" y="420" font-size="14" font-weight="bold" fill="#2c3e50">Three Output Matrices:</text>
                        
                        <rect x="100" y="440" width="300" height="200" fill="white" stroke="#ccc" stroke-width="1" rx="5"/>
                        <text x="250" y="465" text-anchor="middle" font-size="14" font-weight="bold" fill="#28a745">Spliced (S)</text>
                        <text x="250" y="490" text-anchor="middle" font-size="12" fill="#666">Mature mRNA reads</text>
                        <text x="250" y="510" text-anchor="middle" font-size="12" fill="#666">High confidence</text>
                        
                        <rect x="450" y="440" width="300" height="200" fill="white" stroke="#ccc" stroke-width="1" rx="5"/>
                        <text x="600" y="465" text-anchor="middle" font-size="14" font-weight="bold" fill="#ffc107">Unspliced (U)</text>
                        <text x="600" y="490" text-anchor="middle" font-size="12" fill="#666">Pre-mRNA reads</text>
                        <text x="600" y="510" text-anchor="middle" font-size="12" fill="#666">Validated intron reads</text>
                        
                        <rect x="800" y="440" width="300" height="200" fill="white" stroke="#ccc" stroke-width="1" rx="5"/>
                        <text x="950" y="465" text-anchor="middle" font-size="14" font-weight="bold" fill="#dc3545">Ambiguous (A)</text>
                        <text x="950" y="490" text-anchor="middle" font-size="12" fill="#666">Unclassifiable reads</text>
                        <text x="950" y="510" text-anchor="middle" font-size="12" fill="#666">Multi-gene or low confidence</text>
                    </svg>
                </div>
                
                <h3>8.2 Counting Algorithm</h3>
                
                <div class="code-box">
// Pseudocode for counting process
function count_cell_batch(reads, geneid2ix):
    // Initialize count matrices
    num_genes = len(geneid2ix)
    num_cells = count_unique_cells(reads)
    
    spliced = zeros(num_genes, num_cells)
    unspliced = zeros(num_genes, num_cells)
    ambiguous = zeros(num_genes, num_cells)
    
    // Group reads by cell and UMI (molecules)
    molitems = defaultdict(Molitem)
    
    for read in reads:
        bcumi = read.bc + "$" + read.umi
        molitems[bcumi].add_read(read)
    
    // Classify and count each molecule
    for bcumi, molitem in molitems.items():
        cell_idx = get_cell_index(bcumi.split("$")[0])
        classification = classify_molitem(molitem)
        
        if classification == "SPLICED":
            gene_idx = geneid2ix[molitem.gene_id]
            spliced[gene_idx, cell_idx] += 1
        elif classification == "UNSPLICED":
            gene_idx = geneid2ix[molitem.gene_id]
            unspliced[gene_idx, cell_idx] += 1
        elif classification == "AMBIGUOUS":
            gene_idx = geneid2ix[molitem.gene_id]
            ambiguous[gene_idx, cell_idx] += 1
    
    return spliced, unspliced, ambiguous

// Molitem class for grouping reads by UMI
class Molitem:
    constructor():
        this.reads = []
        this.mappings_record = []
        
    function add_read(read):
        this.reads.append(read)
        // Combine mappings from all reads with same UMI
        this.mappings_record.extend(read.mappings_record)
        
    function get_consensus_classification():
        // Logic to determine consensus classification
        // from all reads sharing the same UMI
        pass
                </div>
                
                <div class="math-box">
                    <p><strong>Count Matrix Notation:</strong></p>
                    <p>Let $S$, $U$, and $A$ represent the spliced, unspliced, and ambiguous count matrices respectively:</p>
                    
                    $$S, U, A \in \mathbb{N}^{G \times C}$$
                    
                    <p>Where:</p>
                    <ul>
                        <li>$G$: Number of genes</li>
                        <li>$C$: Number of cells</li>
                        <li>$S_{g,c}$: Number of spliced reads for gene $g$ in cell $c$</li>
<!-- Section 9: Conclusion -->
            <div class="section" id="conclusion">
                <h2>9. Conclusion</h2>
                
                <p>The velocyto pipeline transforms raw sequencing data into biologically meaningful count matrices through a series of carefully designed steps.</p>
                
                <div class="conclusion">
                    <h2>🧬 Key Takeaways</h2>
                    
                    <div class="key-point">
                        <h3>1. Splicing Detection</h3>
                        <p>CIGAR string parsing with BAM_CREF_SKIP (N) operations is the key to distinguishing spliced from unspliced reads.</p>
                    </div>
                    
                    <div class="key-point">
                        <h3>2. Accurate Classification</h3>
                        <p>Multi-level classification logic ensures high-confidence assignment of reads to spliced, unspliced, or ambiguous categories.</p>
                    </div>
                    
                    <div class="key-point">
                        <h3>3. Quality Control</h3>
                        <p>Intron validation eliminates false positives from genomic DNA contamination and mapping artifacts.</p>
                    </div>
                    
                    <div class="key-point">
                        <h3>4. Biological Relevance</h3>
                        <p>The resulting count matrices enable RNA velocity analysis to predict future cell states and understand dynamic gene expression.</p>
                    </div>
                </div>
                
                <h3>9.1 Pipeline Summary</h3>
                
                <div class="svg-container">
                    <svg width="1200" height="800" viewBox="0 0 1200 800">
                        <!-- Background -->
                        <rect width="1200" height="800" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
                        
                        <!-- Title -->
                        <text x="600" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">Velocyto Pipeline Summary</text>
                        
                        <!-- Step 1: BAM Input -->
                        <rect x="100" y="100" width="200" height="100" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="10"/>
                        <text x="200" y="140" text-anchor="middle" font-size="16" font-weight="bold" fill="#1976d2">1. BAM Input</text>
                        <text x="200" y="170" text-anchor="middle" font-size="12" fill="#666">Aligned reads with</text>
                        <text x="200" y="190" text-anchor="middle" font-size="12" fill="#666">CB, UB, CIGAR</text>
                        
                        <!-- Arrow 1 -->
                        <path d="M 300 150 L 350 150" stroke="#666" stroke-width="3" marker-end="url(#arrow4)"/>
                        
                        <!-- Step 2: GTF Annotation -->
                        <rect x="350" y="100" width="200" height="100" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="10"/>
                        <text x="450" y="140" text-anchor="middle" font-size="16" font-weight="bold" fill="#f57c00">2. GTF Parsing</text>
                        <text x="450" y="170" text-anchor="middle" font-size="12" fill="#666">Gene models and</text>
                        <text x="450" y="190" text-anchor="middle" font-size="12" fill="#666">transcript features</text>
                        
                        <!-- Arrow 2 -->
                        <path d="M 550 150 L 600 150" stroke="#666" stroke-width="3" marker-end="url(#arrow4)"/>
                        
                        <!-- Step 3: Feature Assignment -->
                        <rect x="600" y="100" width="200" height="100" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10"/>
                        <text x="700" y="140" text-anchor="middle" font-size="16" font-weight="bold" fill="#4caf50">3. Feature Assignment</text>
                        <text x="700" y="170" text-anchor="middle" font-size="12" fill="#666">Map reads to</text>
                        <text x="700" y="190" text-anchor="middle" font-size="12" fill="#666">genomic features</text>
                        
                        <!-- Arrow 3 -->
                        <path d="M 800 150 L 850 150" stroke="#666" stroke-width="3" marker-end="url(#arrow4)"/>
                        
                        <!-- Step 4: Intron Validation -->
                        <rect x="850" y="100" width="200" height="100" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="10"/>
                        <text x="950" y="140" text-anchor="middle" font-size="16" font-weight="bold" fill="#1976d2">4. Intron Validation</text>
                        <text x="950" y="170" text-anchor="middle" font-size="12" fill="#666">Validate introns</text>
                        <text x="950" y="190" text-anchor="middle" font-size="12" fill="#666">with spanning reads</text>
                        
                        <!-- Down Arrow -->
                        <path d="M 950 200 L 950 250" stroke="#666" stroke-width="3" marker-end="url(#arrow4)"/>
                        
                        <!-- Step 5: Read Classification -->
                        <rect x="850" y="250" width="200" height="100" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="10"/>
                        <text x="950" y="290" text-anchor="middle" font-size="16" font-weight="bold" fill="#f57c00">5. Read Classification</text>
                        <text x="950" y="320" text-anchor="middle" font-size="12" fill="#666">Classify as spliced,</text>
                        <text x="950" y="340" text-anchor="middle" font-size="12" fill="#666">unspliced, or ambiguous</text>
                        
                        <!-- Down Arrow -->
                        <path d="M 950 350 L 950 400" stroke="#666" stroke-width="3" marker-end="url(#arrow4)"/>
                        
                        <!-- Step 6: Counting -->
                        <rect x="850" y="400" width="200" height="100" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10"/>
                        <text x="950" y="440" text-anchor="middle" font-size="16" font-weight="bold" fill="#4caf50">6. Counting</text>
                        <text x="950" y="470" text-anchor="middle" font-size="12" fill="#666">Generate count</text>
                        <text x="950" y="490" text-anchor="middle" font-size="12" fill="#666">matrices by UMI</text>
                        
                        <!-- Left Arrow -->
                        <path d="M 850 450 L 800 450" stroke="#666" stroke-width="3" marker-end="url(#arrow4)"/>
                        
                        <!-- Step 7: Output Matrices -->
                        <rect x="600" y="400" width="200" height="100" fill="#d1ecf1" stroke="#17a2b8" stroke-width="2" rx="10"/>
                        <text x="700" y="440" text-anchor="middle" font-size="16" font-weight="bold" fill="#17a2b8">7. Output Matrices</text>
                        <text x="700" y="470" text-anchor="middle" font-size="12" fill="#666">S, U, A matrices</text>
                        <text x="700" y="490" text-anchor="middle" font-size="12" fill="#666">for RNA velocity</text>
                        
                        <!-- Left Arrow -->
                        <path d="M 600 450 L 550 450" stroke="#666" stroke-width="3" marker-end="url(#arrow4)"/>
                        
                        <!-- RNA Velocity -->
                        <rect x="350" y="400" width="200" height="100" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="10"/>
                        <text x="450" y="440" text-anchor="middle" font-size="16" font-weight="bold" fill="#dc3545">8. RNA Velocity</text>
                        <text x="450" y="470" text-anchor="middle" font-size="12" fill="#666">Calculate velocity</text>
                        <text x="450" y="490" text-anchor="middle" font-size="12" fill="#666">vectors and trajectories</text>
                        
                        <!-- Left Arrow -->
                        <path d="M 350 450 L 300 450" stroke="#666" stroke-width="3" marker-end="url(#arrow4)"/>
                        
                        <!-- Final Output -->
                        <rect x="100" y="400" width="200" height="100" fill="#d4edda" stroke="#28a745" stroke-width="2" rx="10"/>
                        <text x="200" y="440" text-anchor="middle" font-size="16" font-weight="bold" fill="#28a745">9. Biological Insights</text>
                        <text x="200" y="470" text-anchor="middle" font-size="12" fill="#666">Cell trajectories,</text>
                        <text x="200" y="490" text-anchor="middle" font-size="12" fill="#666">gene dynamics</text>
                        
                        <!-- Up Arrow -->
                        <path d="M 200 400 L 200 250" stroke="#666" stroke-width="3" marker-end="url(#arrow4)"/>
                        <path d="M 200 250 L 200 200" stroke="#666" stroke-width="3" marker-end="url(#arrow4)"/>
                        
                        <!-- Arrow marker -->
                        <defs>
                            <marker id="arrow4" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto" markerUnits="strokeWidth">
                                <path d="M0,0 L0,6 L9,3 z" fill="#666"/>
                            </marker>
                        </defs>
                        
                        <!-- Process Loop -->
                        <path d="M 300 150 Q 250 150 250 250 Q 250 450 300 450" fill="none" stroke="#666" stroke-width="2" stroke-dasharray="5,5"/>
                        <text x="230" y="300" text-anchor="middle" font-size="12" fill="#666" transform="rotate(-90 230,300)">Processing Loop</text>
                        
                        <!-- Legend -->
                        <rect x="100" y="550" width="1000" height="200" fill="white" stroke="#ccc" stroke-width="1" rx="5"/>
                        <text x="600" y="580" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Pipeline Components</text>
                        
                        <rect x="150" y="600" width="20" height="20" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <text x="180" y="615" font-size="12" fill="#666">Input/Output Data</text>
                        
                        <rect x="350" y="600" width="20" height="20" fill="#fff3e0" stroke="#f57c00" stroke-width="1"/>
                        <text x="380" y="615" font-size="12" fill="#666">Annotation Processing</text>
                        
                        <rect x="550" y="600" width="20" height="20" fill="#e8f5e8" stroke="#4caf50" stroke-width="1"/>
                        <text x="580" y="615" font-size="12" fill="#666">Mapping and Counting</text>
                        
                        <rect x="150" y="640" width="20" height="20" fill="#d1ecf1" stroke="#17a2b8" stroke-width="1"/>
                        <text x="180" y="655" font-size="12" fill="#666">Analysis Results</text>
                        
                        <rect x="350" y="640" width="20" height="20" fill="#f8d7da" stroke="#dc3545" stroke-width="1"/>
                        <text x="380" y="655" font-size="12" fill="#666">Downstream Applications</text>
                        
                        <rect x="550" y="640" width="20" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                        <text x="580" y="655" font-size="12" fill="#666">Biological Insights</text>
                        
                        <text x="150" y="690" font-size="14" font-weight="bold" fill="#2c3e50">🎯 Success Factors:</text>
                        <text x="150" y="710" font-size="12" fill="#666">1. High-quality annotations  2. Proper UMI handling  3. Rigorous validation  4. Accurate classification</text>
                    </svg>
                </div>
                
                <h3>9.2 Future Considerations</h3>
                
                <div class="info-box">
                    <strong>🚀 Advanced Topics:</strong>
                    <ul>
                        <li><strong>Alternative Splicing:</strong> Complex transcript isoforms may require more sophisticated classification</li>
                        <li><strong>Repeat Elements:</strong> Masking strategies for repetitive genomic regions</li>
                        <li><strong>Multi-mapping Reads:</strong> Handling reads that map to multiple locations</li>
                        <li><strong>Batch Effects:</strong> Normalization across different sequencing runs</li>
                    </ul>
                </div>
                
                <div class="success-box">
                    <strong>✅ Best Practices:</strong>
                    <ul>
                        <li>Use high-quality, comprehensive gene annotations (GENCODE/Ensembl)</li>
                        <li>Ensure proper UMI and cell barcode formatting in BAM files</li>
                        <li>Validate results with known spliced/unspliced ratios</li>
                        <li>Monitor ambiguous read fractions as a quality metric</li>
                    </ul>
                </div>
            </div>
            
            <div class="footer">
                <p>🧬 Velocyto BAM to Counting Pipeline | RNA Velocity Analysis</p>
                <p>Understanding the journey from raw sequencing data to biological insights</p>
            </div>
        </div>
    </div>
</body>
</html>
                        <li>$U_{g,c}$: Number of unspliced reads for gene $g$ in cell $c$</li>
                        <li>$A_{g,c}$: Number of ambiguous reads for gene $g$ in cell $c$</li>
                    </ul>
                    
                    <p>The total count for gene $g$ in cell $c$ is:</p>
                    $$T_{g,c} = S_{g,c} + U_{g,c} + A_{g,c}$$
                </div>
                
                <div class="warning-box">
                    <strong>⚠️ Important:</strong> Each read is grouped by cell barcode (BC) and unique molecular identifier (UMI) to count molecules rather than reads, reducing amplification bias.
                </div>
            </div>
                        
                        <path d="M 600 380 L 600 410" stroke="#666" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="620" y="395" font-size="10" fill="#666">NO</text>
                        
                        <path d="M 650 435 L 810 480" stroke="#ffc107" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="750" y="455" text-anchor="middle" font-size="10" fill="#ffc107">YES</text>
                        
                        <path d="M 600 450 L 600 480" stroke="#666" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="620" y="465" font-size="10" fill="#666">NO</text>
                        
                        <path d="M 570 505 L 460 550" stroke="#ffc107" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="490" y="525" text-anchor="middle" font-size="10" fill="#ffc107">YES</text>
                        
                        <path d="M 630 505 L 660 550" stroke="#dc3545" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="670" y="525" text-anchor="middle" font-size="10" fill="#dc3545">NO</text>
                        
                        <!-- Legend -->
                        <rect x="50" y="650" width="300" height="120" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="200" y="675" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Classification Results</text>
                        
                        <rect x="70" y="690" width="15" height="15" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                        <text x="95" y="702" font-size="11" fill="#666">SPLICED - Mature mRNA</text>
                        
                        <rect x="70" y="715" width="15" height="15" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                        <text x="95" y="727" font-size="11" fill="#666">UNSPLICED - Pre-mRNA</text>
                        
                        <rect x="70" y="740" width="15" height="15" fill="#f8d7da" stroke="#dc3545" stroke-width="1"/>
                        <text x="95" y="752" font-size="11" fill="#666">AMBIGUOUS - Cannot classify</text>
                        
                        <!-- Arrow marker -->
                        <defs>
                            <marker id="arrow3" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>
            </div>
        
    function has_intron_features():
        return any(match.feature.kind == 105 for match in this.segment_matches)
                </div>
                
                <div class="math-box">
                    <p><strong>Overlap Detection Algorithm:</strong></p>
                    <p>For a read segment $S = (s_{start}, s_{end})$ and feature $F = (f_{start}, f_{end})$:</p>
                    
                    $$\text{overlap}(S, F) = \begin{cases} 
                    \text{True}, & \text{if } s_{start} \leq f_{end} \text{ and } s_{end} \geq f_{start} \\
                    \text{False}, & \text{otherwise}
                    \end{cases}$$
                    
                    <p>The mapping process finds all features that overlap with any segment of the read.</p>
                </div>
                
                <div class="info-box">
                    <strong>🔍 Efficiency Consideration:</strong> Velocyto uses sorted FeatureIndex structures for each chromosome/strand combination to enable efficient overlap detection through binary search algorithms.
                </div>
            </div>
                    
                    <p>Where:</p>
                    <ul>
                        <li>$start, end$: Genomic coordinates (1-based)</li>
                        <li>$kind \in \{101, 105, 109\}$ for exon, intron, masked respectively</li>
                        <li>$is\_validated \in \{true, false\}$ for intron validation status</li>
                    </ul>
                </div>
            </div>
                            <tspan font-weight="bold">ref_skipped = False</tspan> → <tspan font-weight="bold">is_spliced = False</tspan>
                        </text>
                    </svg>
                </div>
                
                <h3>3.2 Velocyto's CIGAR Parsing Implementation</h3>
                
                <div class="code-box">
// Pseudocode for velocyto's CIGAR parsing
function parse_cigar_tuple(cigartuples, pos):
    segments = []
    ref_skip = false
    clip5 = clip3 = 0
    p = pos
    
    for each (operation_id, length) in cigartuples:
        if operation_id == 0:  // BAM_CMATCH
            segments.append((p, p + length - 1))
            p += length
        elif operation_id == 3:  // BAM_CREF_SKIP (intron)
            ref_skip = true
            p += length
        elif operation_id == 2:  // BAM_CDEL
            p += length
        elif operation_id == 4:  // BAM_CSOFT_CLIP
            if p == pos:
                clip5 = length
            else:
                clip3 = length
            p += length
        elif operation_id == 1:  // BAM_CINS
            // Insertions don't advance reference position
            continue
    
    return segments, ref_skip, clip5, clip3

// In Read class
class Read:
    constructor(..., ref_skipped):
        this.ref_skipped = ref_skipped
    
    property is_spliced:
        return this.ref_skipped
                </div>
                
                <div class="math-box">
                    <p><strong>Mathematical Representation:</strong></p>
                    <p>Let $CIGAR = \{(op_1, len_1), (op_2, len_2), ..., (op_n, len_n)\}$ be the parsed CIGAR operations.</p>
                    
                    $$\text{ref_skipped} = \begin{cases} 
                    \text{True}, & \text{if } \exists i \text{ such that } op_i = 3 \\
                    \text{False}, & \text{otherwise}
                    \end{cases}$$
                    
                    $$\text{is_spliced} = \text{ref_skipped}$$
                </div>
                
                <div class="info-box">
                    <strong>🔬 Key Point:</strong> Only operation code 3 (BAM_CREF_SKIP) indicates intron skipping, which is the definitive signature of spliced reads in RNA-seq data.
                </div>
            </div>
                            <td style="padding: 1rem; border-bottom: 1px solid #ddd;"><strong>CB</strong></td>
                            <td style="padding: 1rem; border-bottom: 1px solid #ddd;">Cell Barcode</td>
                            <td style="padding: 1rem; border-bottom: 1px solid #ddd;">Identifies which cell the read originated from</td>
                        </tr>
                        <tr>
                            <td style="padding: 1rem; border-bottom: 1px solid #ddd;"><strong>UB</strong></td>
                            <td style="padding: 1rem; border-bottom: 1px solid #ddd;">Unique Molecular Identifier</td>
                            <td style="padding: 1rem; border-bottom: 1px solid #ddd;">Deduplicates PCR duplicates</td>
                        </tr>
                        <tr style="background-color: #f8f9fa;">
                            <td style="padding: 1rem; border-bottom: 1px solid #ddd;"><strong>NH</strong></td>
                            <td style="padding: 1rem; border-bottom: 1px solid #ddd;">Number of Hits</td>
                            <td style="padding: 1rem; border-bottom: 1px solid #ddd;">Used to filter for unique alignments (NH=1)</td>
                        </tr>
                        <tr>
                            <td style="padding: 1rem; border-bottom: 1px solid #ddd;"><strong>CIGAR</strong></td>
                            <td style="padding: 1rem; border-bottom: 1px solid #ddd;">Alignment Operations</td>
                            <td style="padding: 1rem; border-bottom: 1px solid #ddd;">Determines if read is spliced (contains 'N' operations)</td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="warning-box">
                    <strong>⚠️ Important Considerations:</strong>
                    <ul>
                        <li>BAM files must be coordinate-sorted for efficient processing</li>
                        <li>Cell barcodes and UMIs must be properly formatted tags</li>
                        <li>Quality filtering may be applied to remove low-confidence alignments</li>
                    </ul>
                </div>
            </div>
                <ul>
                    <li><a href="#introduction">1. Introduction and Overview</a></li>
                    <li><a href="#bam-structure">2. BAM File Structure and Content</a></li>
                    <li><a href="#cigar-parsing">3. CIGAR Parsing for Splicing Detection</a></li>
                    <li><a href="#gene-annotation">4. Gene Annotation and Transcript Models</a></li>
                    <li><a href="#feature-assignment">5. Feature Assignment Process</a></li>
                    <li><a href="#read-classification">6. Read Classification Logic</a></li>
                    <li><a href="#intron-validation">7. Intron Validation Process</a></li>
                    <li><a href="#counting-process">8. Counting Process and Matrices</a></li>
                    <li><a href="#conclusion">9. Conclusion</a></li>
                </ul>
            </div>

            <!-- Section 1: Introduction -->
            <div class="section" id="introduction">
                <h2>1. Introduction and Overview</h2>
                
                <p>RNA velocity analysis requires precise quantification of spliced and unspliced RNA molecules to estimate the direction and speed of gene expression changes. The velocyto pipeline transforms sequencing data in BAM format into count matrices that distinguish between mature mRNA (spliced) and pre-mRNA (unspliced).</p>
                
                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>BAM Input:</strong> Aligned sequencing reads with positional and CIGAR information
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>Annotation Integration:</strong> Gene models and transcript structures from GTF files
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div>
                        <strong>Read Classification:</strong> Determining if reads represent spliced or unspliced transcripts
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="step-number">4</div>
                    <div>
                        <strong>Counting:</strong> Generating spliced, unspliced, and ambiguous count matrices
                    </div>
                </div>
                
                <h3>1.1 Why This Process Matters</h3>
                <p>The ratio of unspliced to spliced reads provides the foundation for RNA velocity calculations:</p>
                
                <div class="math-box">
                    <p><strong>RNA Velocity Principle:</strong></p>
                    $$\frac{du}{dt} = \alpha - \beta u$$
                    $$\frac{ds}{dt} = \beta u - \gamma s$$
                    
                    <p>Where:</p>
                    <ul>
                        <li>$u$ = unspliced mRNA abundance</li>
                        <li>$s$ = spliced mRNA abundance</li>
                        <li>$\alpha$ = transcription rate</li>
                        <li>$\beta$ = splicing rate</li>
                        <li>$\gamma$ = degradation rate</li>
                    </ul>
                </div>
                
                <div class="info-box">
                    <strong>🎯 Key Insight:</strong> Accurate read classification is crucial because the unspliced/spliced ratio directly determines velocity estimates. Misclassified reads lead to incorrect velocity vectors and erroneous trajectory inference.
                </div>
            </div>
        </div>
    </div>
</body>
</html>