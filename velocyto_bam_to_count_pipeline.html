<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Velocyto: BAM to Count Pipeline</title>
    
    <!-- MathJax 3 for LaTeX rendering -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.8rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 1rem 0 0 0;
            font-size: 1.3rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
            border-radius: 0 8px 8px 0;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.8rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        
        .section h3 {
            color: #34495e;
            margin-top: 2rem;
            font-size: 1.4rem;
        }
        
        .code-box {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            font-size: 0.9rem;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .svg-container {
            text-align: center;
            margin: 2rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-x: auto;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 2px solid #17a2b8;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .success-box {
            background: #d4edda;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .pipeline-step {
            background: white;
            border: 2px solid #3498db;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            position: relative;
        }
        
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            position: absolute;
            top: -20px;
            left: 20px;
            font-size: 1.2rem;
        }
        
        .data-flow {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 2rem 0;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .flow-item {
            background: #e3f2fd;
            border: 2px solid #1976d2;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            min-width: 120px;
            flex: 1;
        }
        
        .flow-arrow {
            font-size: 2rem;
            color: #666;
            margin: 0 0.5rem;
        }
        
        .toc {
            background: #34495e;
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .toc h3 {
            margin-top: 0;
            color: #ecf0f1;
        }
        
        .toc ul {
            list-style: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 0.5rem 0;
            padding-left: 1rem;
        }
        
        .toc a {
            color: #3498db;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .toc a:hover {
            color: #5dade2;
        }
        
        .performance-metric {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 0.5rem;
            margin: 0.5rem 0;
            display: inline-block;
            min-width: 150px;
        }
        
        .metric-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .metric-label {
            font-size: 0.9rem;
            color: #666;
        }
        
        .interactive-demo {
            background: #f8f9fa;
            border: 2px solid #6c757d;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .demo-button {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 0.5rem 1rem;
            cursor: pointer;
            margin: 0.5rem;
            transition: background 0.3s;
        }
        
        .demo-button:hover {
            background: #0056b3;
        }
        
        .demo-output {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 4px;
            padding: 1rem;
            margin-top: 1rem;
            font-family: monospace;
            min-height: 100px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧬 Velocyto: BAM to Count Pipeline</h1>
            <p>Complete Processing Pipeline from Raw Reads to Count Matrices</p>
        </div>
        
        <div class="content">
            <!-- Table of Contents -->
            <div class="toc">
                <h3>📚 Table of Contents</h3>
                <ul>
                    <li><a href="#overview">1. Pipeline Overview</a></li>
                    <li><a href="#input-preparation">2. Input Preparation</a></li>
                    <li><a href="#read-parsing">3. Read Parsing and Extraction</a></li>
                    <li><a href="#feature-mapping">4. Feature Mapping and Overlap Detection</a></li>
                    <li><a href="#intron-validation">5. Intron Validation</a></li>
                    <li><a href="#molecule-assembly">6. Molecule Assembly</a></li>
                    <li><a href="#classification">7. Read Classification</a></li>
                    <li><a href="#batch-processing">8. Batch Processing and Memory Management</a></li>
                    <li><a href="#output-generation">9. Output Generation</a></li>
                    <li><a href="#performance">10. Performance Optimization</a></li>
                </ul>
            </div>

            <!-- Section 1: Overview -->
            <div class="section" id="overview">
                <h2>1. Pipeline Overview</h2>
                
                <div class="info-box">
                    <strong>🎯 Pipeline Goal:</strong> Transform aligned single-cell RNA-seq reads (BAM format) into count matrices for spliced, unspliced, and ambiguous reads, enabling RNA velocity analysis.
                </div>
                
                <h3>1.1 High-Level Data Flow</h3>
                <div class="data-flow">
                    <div class="flow-item">
                        <strong>BAM File</strong><br>
                        <small>Aligned reads with cell barcodes</small>
                    </div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-item">
                        <strong>Read Objects</strong><br>
                        <small>Parsed bc, umi, segments</small>
                    </div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-item">
                        <strong>Feature Mapping</strong><br>
                        <small>Overlap with exons/introns</small>
                    </div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-item">
                        <strong>Classification</strong><br>
                        <small>Spliced/Unspliced/Ambiguous</small>
                    </div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-item">
                        <strong>Count Matrices</strong><br>
                        <small>S, U, A matrices</small>
                    </div>
                </div>
                
                <h3>1.2 Key Performance Metrics</h3>
                <div style="display: flex; flex-wrap: wrap; gap: 1rem; justify-content: space-around;">
                    <div class="performance-metric">
                        <div class="metric-value">~2-6 hours</div>
                        <div class="metric-label">Processing Time<br>(10k cells, 100M reads)</div>
                    </div>
                    <div class="performance-metric">
                        <div class="metric-value">8-16 GB</div>
                        <div class="metric-label">Memory Usage<br>(Human genome)</div>
                    </div>
                    <div class="performance-metric">
                        <div class="metric-value">60-80%</div>
                        <div class="metric-label">Spliced Reads<br>(Typical)</div>
                    </div>
                    <div class="performance-metric">
                        <div class="metric-value">15-30%</div>
                        <div class="metric-label">Unspliced Reads<br>(Typical)</div>
                    </div>
                    <div class="performance-metric">
                        <div class="metric-value">5-15%</div>
                        <div class="metric-label">Ambiguous Reads<br>(Typical)</div>
                    </div>
                </div>
            </div>

            <!-- Section 2: Input Preparation -->
            <div class="section" id="input-preparation">
                <h2>2. Input Preparation</h2>

                <div class="pipeline-step">
                    <div class="step-number">1</div>
                    <h3 style="margin-top: 0; padding-left: 60px;">Annotation Processing</h3>

                    <div class="svg-container">
                        <svg width="1400" height="300" viewBox="0 0 1400 300">
                            <!-- Background -->
                            <rect width="1400" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                            <!-- Title -->
                            <text x="700" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Annotation Processing and Index Building</text>

                            <!-- GTF File -->
                            <rect x="50" y="60" width="150" height="80" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
                            <text x="125" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#1976d2">GTF File</text>
                            <text x="60" y="105" font-size="9" fill="#666">• Gene annotations</text>
                            <text x="60" y="120" font-size="9" fill="#666">• Exon coordinates</text>
                            <text x="60" y="135" font-size="9" fill="#666">• Transcript models</text>

                            <!-- Processing -->
                            <rect x="250" y="60" width="150" height="80" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                            <text x="325" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">Parse & Build</text>
                            <text x="260" y="105" font-size="9" fill="#666">• Extract features</text>
                            <text x="260" y="120" font-size="9" fill="#666">• Generate introns</text>
                            <text x="260" y="135" font-size="9" fill="#666">• Sort by position</text>

                            <!-- TranscriptModel -->
                            <rect x="450" y="60" width="150" height="80" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                            <text x="525" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">TranscriptModel</text>
                            <text x="460" y="105" font-size="9" fill="#666">• Gene metadata</text>
                            <text x="460" y="120" font-size="9" fill="#666">• Feature list</text>
                            <text x="460" y="135" font-size="9" fill="#666">• Strand info</text>

                            <!-- FeatureIndex -->
                            <rect x="650" y="60" width="150" height="80" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="5"/>
                            <text x="725" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#9c27b0">FeatureIndex</text>
                            <text x="660" y="105" font-size="9" fill="#666">• Sorted features</text>
                            <text x="660" y="120" font-size="9" fill="#666">• Search optimization</text>
                            <text x="660" y="135" font-size="9" fill="#666">• Per chromosome</text>

                            <!-- Memory Structure -->
                            <rect x="850" y="60" width="200" height="80" fill="#ffebee" stroke="#e91e63" stroke-width="2" rx="5"/>
                            <text x="950" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#e91e63">In-Memory Indexes</text>
                            <text x="860" y="105" font-size="9" fill="#666">• feature_indexes: Dict[str, FeatureIndex]</text>
                            <text x="860" y="120" font-size="9" fill="#666">• mask_indexes: Dict[str, FeatureIndex]</text>
                            <text x="860" y="135" font-size="9" fill="#666">• Ready for fast lookup</text>

                            <!-- Flow arrows -->
                            <path d="M 200 100 L 250 100" stroke="#666" stroke-width="3" marker-end="url(#arrow)"/>
                            <path d="M 400 100 L 450 100" stroke="#666" stroke-width="3" marker-end="url(#arrow)"/>
                            <path d="M 600 100 L 650 100" stroke="#666" stroke-width="3" marker-end="url(#arrow)"/>
                            <path d="M 800 100 L 850 100" stroke="#666" stroke-width="3" marker-end="url(#arrow)"/>

                            <!-- Example gene structure -->
                            <text x="700" y="180" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Example: Gene Structure Processing</text>

                            <!-- Gene visualization -->
                            <line x1="200" y1="220" x2="1000" y2="220" stroke="#333" stroke-width="2"/>
                            <text x="600" y="210" text-anchor="middle" font-size="10" fill="#666">Genomic Coordinate →</text>

                            <!-- Exons -->
                            <rect x="250" y="210" width="60" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="280" y="225" text-anchor="middle" font-size="8" fill="#155724">Exon 1</text>

                            <rect x="400" y="210" width="60" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="430" y="225" text-anchor="middle" font-size="8" fill="#155724">Exon 2</text>

                            <rect x="550" y="210" width="60" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="580" y="225" text-anchor="middle" font-size="8" fill="#155724">Exon 3</text>

                            <!-- Introns (generated) -->
                            <rect x="320" y="235" width="70" height="15" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                            <text x="355" y="247" text-anchor="middle" font-size="7" fill="#856404">Intron 1</text>

                            <rect x="470" y="235" width="70" height="15" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                            <text x="505" y="247" text-anchor="middle" font-size="7" fill="#856404">Intron 2</text>

                            <text x="600" y="270" text-anchor="middle" font-size="10" fill="#666">Introns generated from exon boundaries</text>

                            <!-- Arrow marker -->
                            <defs>
                                <marker id="arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                    <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                                </marker>
                            </defs>
                        </svg>
                    </div>

                    <div class="code-box">
# Annotation processing pseudocode
def build_indexes(gtf_file):
    # 1. Parse GTF file
    transcript_models = parse_gtf(gtf_file)

    # 2. Generate introns from exon boundaries
    for tm in transcript_models:
        tm.add_introns_from_exons()

    # 3. Build per-chromosome feature indexes
    feature_indexes = {}
    for chrom in chromosomes:
        features = collect_features_for_chromosome(chrom, transcript_models)
        feature_indexes[chrom] = FeatureIndex(sorted(features))

    return feature_indexes, transcript_models
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ Memory Consideration:</strong> For human genome, this creates ~450,000 features per chromosome, requiring 3-6 GB RAM total. This one-time cost enables fast O(log n) lookups throughout processing.
                    </div>
                </div>
            </div>

            <!-- Section 3: Read Parsing -->
            <div class="section" id="read-parsing">
                <h2>3. Read Parsing and Extraction</h2>

                <div class="pipeline-step">
                    <div class="step-number">2</div>
                    <h3 style="margin-top: 0; padding-left: 60px;">BAM File Processing</h3>

                    <div class="svg-container">
                        <svg width="1400" height="400" viewBox="0 0 1400 400">
                            <!-- Background -->
                            <rect width="1400" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                            <!-- Title -->
                            <text x="700" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">BAM Read Processing Pipeline</text>

                            <!-- BAM File -->
                            <rect x="50" y="60" width="120" height="100" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
                            <text x="110" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#1976d2">BAM File</text>
                            <text x="60" y="105" font-size="9" fill="#666">• Aligned reads</text>
                            <text x="60" y="120" font-size="9" fill="#666">• Cell barcodes</text>
                            <text x="60" y="135" font-size="9" fill="#666">• UMI sequences</text>
                            <text x="60" y="150" font-size="9" fill="#666">• CIGAR strings</text>

                            <!-- Read Extraction -->
                            <rect x="220" y="60" width="120" height="100" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                            <text x="280" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">Extract Info</text>
                            <text x="230" y="105" font-size="9" fill="#666">• Parse tags</text>
                            <text x="230" y="120" font-size="9" fill="#666">• Extract BC/UMI</text>
                            <text x="230" y="135" font-size="9" fill="#666">• Parse CIGAR</text>
                            <text x="230" y="150" font-size="9" fill="#666">• Get segments</text>

                            <!-- Read Object -->
                            <rect x="390" y="60" width="120" height="100" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                            <text x="450" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">Read Object</text>
                            <text x="400" y="105" font-size="9" fill="#666">bc: str</text>
                            <text x="400" y="120" font-size="9" fill="#666">umi: str</text>
                            <text x="400" y="135" font-size="9" fill="#666">segments: List</text>
                            <text x="400" y="150" font-size="9" fill="#666">is_spliced: bool</text>

                            <!-- Flow arrows -->
                            <path d="M 170 110 L 220 110" stroke="#666" stroke-width="3" marker-end="url(#arrow2)"/>
                            <path d="M 340 110 L 390 110" stroke="#666" stroke-width="3" marker-end="url(#arrow2)"/>

                            <!-- Example read processing -->
                            <text x="700" y="200" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Example: 10X Read Processing</text>

                            <!-- BAM record example -->
                            <rect x="50" y="220" width="1300" height="150" fill="white" stroke="#ccc" stroke-width="1"/>
                            <text x="70" y="240" font-size="11" font-weight="bold" fill="#333">Raw BAM Record:</text>
                            <text x="70" y="260" font-size="9" font-family="monospace" fill="#666">QNAME: A00123:45:H2VKJDSXX:1:1101:1234:5678</text>
                            <text x="70" y="275" font-size="9" font-family="monospace" fill="#666">FLAG: 0 (properly paired)</text>
                            <text x="70" y="290" font-size="9" font-family="monospace" fill="#666">RNAME: chr1, POS: 1000000</text>
                            <text x="70" y="305" font-size="9" font-family="monospace" fill="#666">CIGAR: 25M1000N25M (spliced read)</text>
                            <text x="70" y="320" font-size="9" font-family="monospace" fill="#666">CB:Z:AAACCTGAGCGCTCCA-1 (cell barcode)</text>
                            <text x="70" y="335" font-size="9" font-family="monospace" fill="#666">UB:Z:AGTCAGTCAGTC (UMI)</text>

                            <text x="700" y="240" font-size="11" font-weight="bold" fill="#333">Extracted Read Object:</text>
                            <text x="700" y="260" font-size="9" font-family="monospace" fill="#666">bc = "AAACCTGAGCGCTCCA-1"</text>
                            <text x="700" y="275" font-size="9" font-family="monospace" fill="#666">umi = "AGTCAGTCAGTC"</text>
                            <text x="700" y="290" font-size="9" font-family="monospace" fill="#666">chrom = "chr1", strand = "+"</text>
                            <text x="700" y="305" font-size="9" font-family="monospace" fill="#666">segments = [(1000000, 1000024), (1001025, 1001049)]</text>
                            <text x="700" y="320" font-size="9" font-family="monospace" fill="#666">is_spliced = True</text>
                            <text x="700" y="335" font-size="9" font-family="monospace" fill="#666">pos = 1000000, span = 1049</text>

                            <!-- Arrow marker -->
                            <defs>
                                <marker id="arrow2" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                    <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                                </marker>
                            </defs>
                        </svg>
                    </div>

                    <div class="code-box">
# Read parsing implementation
def parse_bam_read(alignment):
    """Extract velocyto Read object from BAM alignment"""

    # Extract cell barcode and UMI from tags
    bc = alignment.get_tag("CB") if alignment.has_tag("CB") else None
    umi = alignment.get_tag("UB") if alignment.has_tag("UB") else None

    # Skip reads without proper barcodes
    if not bc or not umi:
        return None

    # Parse CIGAR string to get genomic segments
    segments = []
    pos = alignment.reference_start

    for operation, length in alignment.cigartuples:
        if operation == 0:  # Match/mismatch
            segments.append((pos, pos + length - 1))
            pos += length
        elif operation == 3:  # Skip (intron)
            pos += length

    # Create Read object
    return Read(
        bc=bc,
        umi=umi,
        chrom=alignment.reference_name,
        strand="+" if not alignment.is_reverse else "-",
        pos=alignment.reference_start,
        segments=segments,
        is_spliced=len(segments) > 1
    )
                    </div>
                </div>
            </div>

            <!-- Section 4: Feature Mapping -->
            <div class="section" id="feature-mapping">
                <h2>4. Feature Mapping and Overlap Detection</h2>

                <div class="pipeline-step">
                    <div class="step-number">3</div>
                    <h3 style="margin-top: 0; padding-left: 60px;">Genomic Feature Overlap Detection</h3>

                    <div class="svg-container">
                        <svg width="1400" height="500" viewBox="0 0 1400 500">
                            <!-- Background -->
                            <rect width="1400" height="500" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                            <!-- Title -->
                            <text x="700" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Feature Mapping Process</text>

                            <!-- Read segments -->
                            <text x="100" y="70" font-size="12" font-weight="bold" fill="#333">Input: Read Segments</text>
                            <rect x="100" y="80" width="80" height="15" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                            <text x="140" y="92" text-anchor="middle" font-size="8" fill="#1976d2">Segment 1</text>
                            <text x="100" y="110" font-size="9" fill="#666">(1000000, 1000024)</text>

                            <rect x="250" y="80" width="80" height="15" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                            <text x="290" y="92" text-anchor="middle" font-size="8" fill="#1976d2">Segment 2</text>
                            <text x="250" y="110" font-size="9" fill="#666">(1001025, 1001049)</text>

                            <!-- Feature index -->
                            <text x="500" y="70" font-size="12" font-weight="bold" fill="#333">FeatureIndex Lookup</text>
                            <rect x="450" y="80" width="300" height="120" fill="white" stroke="#ccc" stroke-width="1"/>

                            <!-- Sorted features -->
                            <rect x="460" y="90" width="60" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="490" y="105" text-anchor="middle" font-size="8" fill="#155724">Exon A</text>
                            <text x="460" y="125" font-size="7" fill="#666">999950-1000050</text>

                            <rect x="530" y="90" width="60" height="20" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                            <text x="560" y="105" text-anchor="middle" font-size="8" fill="#856404">Intron B</text>
                            <text x="530" y="125" font-size="7" fill="#666">1000051-1001000</text>

                            <rect x="600" y="90" width="60" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="630" y="105" text-anchor="middle" font-size="8" fill="#155724">Exon C</text>
                            <text x="600" y="125" font-size="7" fill="#666">1001001-1001100</text>

                            <rect x="670" y="90" width="60" height="20" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                            <text x="700" y="105" text-anchor="middle" font-size="8" fill="#856404">Intron D</text>
                            <text x="670" y="125" font-size="7" fill="#666">1001101-1002000</text>

                            <!-- Search process -->
                            <text x="600" y="150" text-anchor="middle" font-size="10" fill="#666">Binary search through sorted features</text>
                            <text x="600" y="165" text-anchor="middle" font-size="10" fill="#666">iidx tracks current position</text>
                            <text x="600" y="180" text-anchor="middle" font-size="10" fill="#666">Skip features that end before read starts</text>

                            <!-- Overlap detection -->
                            <text x="100" y="230" font-size="12" font-weight="bold" fill="#333">Overlap Detection Results</text>

                            <!-- SegmentMatch objects -->
                            <rect x="100" y="250" width="250" height="80" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="5"/>
                            <text x="225" y="270" text-anchor="middle" font-size="11" font-weight="bold" fill="#7b1fa2">SegmentMatch 1</text>
                            <text x="110" y="290" font-size="9" fill="#666">segment: (1000000, 1000024)</text>
                            <text x="110" y="305" font-size="9" fill="#666">feature: Exon A</text>
                            <text x="110" y="320" font-size="9" fill="#666">overlap_type: CONTAINS</text>

                            <rect x="400" y="250" width="250" height="80" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="5"/>
                            <text x="525" y="270" text-anchor="middle" font-size="11" font-weight="bold" fill="#7b1fa2">SegmentMatch 2</text>
                            <text x="410" y="290" font-size="9" fill="#666">segment: (1001025, 1001049)</text>
                            <text x="410" y="305" font-size="9" fill="#666">feature: Exon C</text>
                            <text x="410" y="320" font-size="9" fill="#666">overlap_type: CONTAINS</text>

                            <!-- Mapping record -->
                            <text x="900" y="230" font-size="12" font-weight="bold" fill="#333">Mapping Record</text>
                            <rect x="850" y="250" width="300" height="120" fill="#ffebee" stroke="#e91e63" stroke-width="2" rx="5"/>
                            <text x="1000" y="270" text-anchor="middle" font-size="11" font-weight="bold" fill="#c2185b">Dict[TranscriptModel, List[SegmentMatch]]</text>

                            <text x="860" y="295" font-size="9" fill="#666">TranscriptModel_1:</text>
                            <text x="870" y="310" font-size="8" fill="#666">  - SegmentMatch(seg1, ExonA)</text>
                            <text x="870" y="325" font-size="8" fill="#666">  - SegmentMatch(seg2, ExonC)</text>

                            <text x="860" y="345" font-size="9" fill="#666">TranscriptModel_2:</text>
                            <text x="870" y="360" font-size="8" fill="#666">  - SegmentMatch(seg1, ExonA)</text>

                            <!-- Visual representation of overlap -->
                            <text x="700" y="420" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Visual Overlap Example</text>

                            <!-- Genomic coordinate line -->
                            <line x1="200" y1="450" x2="1000" y2="450" stroke="#333" stroke-width="2"/>
                            <text x="600" y="440" text-anchor="middle" font-size="9" fill="#666">Genomic Coordinate →</text>

                            <!-- Features -->
                            <rect x="250" y="440" width="100" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="300" y="455" text-anchor="middle" font-size="8" fill="#155724">Exon A</text>

                            <rect x="400" y="440" width="200" height="20" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                            <text x="500" y="455" text-anchor="middle" font-size="8" fill="#856404">Intron B</text>

                            <rect x="650" y="440" width="100" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="700" y="455" text-anchor="middle" font-size="8" fill="#155724">Exon C</text>

                            <!-- Read segments overlaid -->
                            <rect x="275" y="465" width="50" height="10" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
                            <text x="300" y="475" text-anchor="middle" font-size="7" fill="#1976d2">Seg1</text>

                            <rect x="675" y="465" width="50" height="10" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
                            <text x="700" y="475" text-anchor="middle" font-size="7" fill="#1976d2">Seg2</text>

                            <!-- Overlap indicators -->
                            <path d="M 300 465 L 300 460" stroke="#e74c3c" stroke-width="2"/>
                            <path d="M 700 465 L 700 460" stroke="#e74c3c" stroke-width="2"/>
                            <text x="300" y="490" text-anchor="middle" font-size="8" fill="#e74c3c">OVERLAP</text>
                            <text x="700" y="490" text-anchor="middle" font-size="8" fill="#e74c3c">OVERLAP</text>
                        </svg>
                    </div>

                    <div class="code-box">
# Feature mapping implementation
def find_overlapping_features(read, feature_index):
    """Find all features that overlap with read segments"""

    mapping_record = defaultdict(list)

    # Skip features that end before read starts (optimization)
    while (feature_index.iidx < feature_index.maxiidx and
           feature_index.ivls[feature_index.iidx].ends_upstream_of(read)):
        feature_index.iidx += 1

    # Check each read segment against features
    for segment in read.segments:
        i = feature_index.iidx

        # Local search from current position
        while (i <= feature_index.maxiidx and
               not feature_index.ivls[i].starts_downstream_of(segment)):

            feature = feature_index.ivls[i]

            # Check for meaningful overlap
            if (feature.intersects(segment) and
                (segment[1] - segment[0]) > MIN_FLANK):

                # Create SegmentMatch object
                segment_match = SegmentMatch(segment, feature, read.is_spliced)
                mapping_record[feature.transcript_model].append(segment_match)

            i += 1

    return mapping_record
                    </div>

                    <div class="success-box">
                        <strong>✅ Optimization Benefits:</strong>
                        <ul>
                            <li><strong>Sorted Features:</strong> O(log n) search complexity instead of O(n)</li>
                            <li><strong>State Preservation:</strong> iidx avoids re-scanning previous features</li>
                            <li><strong>Early Termination:</strong> Stop when features start after segment ends</li>
                            <li><strong>Minimum Flank:</strong> Filters out spurious short overlaps</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Section 5: Intron Validation -->
            <div class="section" id="intron-validation">
                <h2>5. Intron Validation</h2>

                <div class="pipeline-step">
                    <div class="step-number">4</div>
                    <h3 style="margin-top: 0; padding-left: 60px;">Spanning Read Detection</h3>

                    <div class="svg-container">
                        <svg width="1400" height="350" viewBox="0 0 1400 350">
                            <!-- Background -->
                            <rect width="1400" height="350" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                            <!-- Title -->
                            <text x="700" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Intron Validation Through Spanning Reads</text>

                            <!-- Gene structure -->
                            <text x="100" y="70" font-size="12" font-weight="bold" fill="#333">Gene Structure</text>
                            <line x1="100" y1="100" x2="800" y2="100" stroke="#333" stroke-width="2"/>

                            <!-- Exons -->
                            <rect x="150" y="90" width="80" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="190" y="105" text-anchor="middle" font-size="9" fill="#155724">Exon 1</text>

                            <rect x="350" y="90" width="80" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="390" y="105" text-anchor="middle" font-size="9" fill="#155724">Exon 2</text>

                            <rect x="550" y="90" width="80" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="590" y="105" text-anchor="middle" font-size="9" fill="#155724">Exon 3</text>

                            <!-- Introns -->
                            <rect x="240" y="115" width="100" height="15" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                            <text x="290" y="127" text-anchor="middle" font-size="8" fill="#856404">Intron 1</text>

                            <rect x="440" y="115" width="100" height="15" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                            <text x="490" y="127" text-anchor="middle" font-size="8" fill="#856404">Intron 2</text>

                            <!-- Spanning reads -->
                            <text x="100" y="170" font-size="12" font-weight="bold" fill="#333">Spanning Reads</text>

                            <!-- Left boundary spanning read -->
                            <rect x="210" y="180" width="60" height="12" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
                            <text x="240" y="190" text-anchor="middle" font-size="8" fill="#1976d2">Spanning Read 1</text>
                            <text x="240" y="205" text-anchor="middle" font-size="7" fill="#666">Exon1 → Intron1</text>

                            <!-- Right boundary spanning read -->
                            <rect x="320" y="180" width="60" height="12" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2"/>
                            <text x="350" y="190" text-anchor="middle" font-size="8" fill="#9c27b0">Spanning Read 2</text>
                            <text x="350" y="205" text-anchor="middle" font-size="7" fill="#666">Intron1 → Exon2</text>

                            <!-- Validation arrows -->
                            <path d="M 240 192 L 290 130" stroke="#e74c3c" stroke-width="2" marker-end="url(#validation-arrow)"/>
                            <path d="M 350 192 L 290 130" stroke="#e74c3c" stroke-width="2" marker-end="url(#validation-arrow)"/>

                            <text x="290" y="145" text-anchor="middle" font-size="10" fill="#e74c3c" font-weight="bold">VALIDATED!</text>

                            <!-- Validation logic -->
                            <text x="900" y="70" font-size="12" font-weight="bold" fill="#333">Validation Logic</text>
                            <rect x="850" y="80" width="400" height="200" fill="white" stroke="#ccc" stroke-width="1"/>

                            <text x="860" y="105" font-size="10" fill="#666" font-weight="bold">Left Boundary Check:</text>
                            <text x="860" y="125" font-size="9" fill="#666">if intron.end_overlaps_with_part_of(segment):</text>
                            <text x="870" y="140" font-size="9" fill="#666">downstream_exon = intron.get_downstream_exon()</text>
                            <text x="870" y="155" font-size="9" fill="#666">if downstream_exon.start_overlaps_with_part_of(segment):</text>
                            <text x="880" y="170" font-size="9" fill="#666">intron.is_validated = True</text>

                            <text x="860" y="195" font-size="10" fill="#666" font-weight="bold">Right Boundary Check:</text>
                            <text x="860" y="215" font-size="9" fill="#666">if intron.start_overlaps_with_part_of(segment):</text>
                            <text x="870" y="230" font-size="9" fill="#666">upstream_exon = intron.get_upstream_exon()</text>
                            <text x="870" y="245" font-size="9" fill="#666">if upstream_exon.end_overlaps_with_part_of(segment):</text>
                            <text x="880" y="260" font-size="9" fill="#666">intron.is_validated = True</text>

                            <!-- Statistics -->
                            <text x="700" y="310" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Validation Statistics</text>
                            <text x="700" y="330" text-anchor="middle" font-size="10" fill="#666">Typical: 60-80% of introns validated by spanning reads</text>

                            <!-- Arrow marker -->
                            <defs>
                                <marker id="validation-arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                    <polygon points="0 0, 8 3, 0 6" fill="#e74c3c"/>
                                </marker>
                            </defs>
                        </svg>
                    </div>

                    <div class="code-box">
# Intron validation implementation
def mark_overlapping_ivls(read, feature_index):
    """Mark introns as validated when reads span exon-intron boundaries"""

    for segment in read.segments:
        i = feature_index.iidx

        while i <= feature_index.maxiidx:
            feature = feature_index.ivls[i]

            # Only check introns for validation
            if feature.kind == ord("i"):  # 'i' = intron

                # Check left boundary (intron end → downstream exon start)
                if feature.end_overlaps_with_part_of(segment):
                    downstream_exon = feature.get_downstream_exon()
                    if downstream_exon and downstream_exon.start_overlaps_with_part_of(segment):
                        feature.is_validated = True
                        logging.debug(f"Validated intron {feature} via left boundary")

                # Check right boundary (upstream exon end → intron start)
                if feature.start_overlaps_with_part_of(segment):
                    upstream_exon = feature.get_upstream_exon()
                    if upstream_exon and upstream_exon.end_overlaps_with_part_of(segment):
                        feature.is_validated = True
                        logging.debug(f"Validated intron {feature} via right boundary")

            i += 1
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ Why Validation Matters:</strong> Intron validation prevents false positives from:
                        <ul>
                            <li><strong>Genomic DNA contamination:</strong> Would show reads in introns without splicing evidence</li>
                            <li><strong>Annotation errors:</strong> Incorrectly annotated introns</li>
                            <li><strong>Pseudogenes:</strong> Processed pseudogenes lacking introns</li>
                            <li><strong>Technical artifacts:</strong> Mapping errors or chimeric reads</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Section 6: Molecule Assembly -->
            <div class="section" id="molecule-assembly">
                <h2>6. Molecule Assembly</h2>

                <div class="pipeline-step">
                    <div class="step-number">5</div>
                    <h3 style="margin-top: 0; padding-left: 60px;">UMI-Based Molecule Grouping</h3>

                    <div class="svg-container">
                        <svg width="1400" height="400" viewBox="0 0 1400 400">
                            <!-- Background -->
                            <rect width="1400" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                            <!-- Title -->
                            <text x="700" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Molecule Assembly from UMI Groups</text>

                            <!-- Input reads -->
                            <text x="100" y="70" font-size="12" font-weight="bold" fill="#333">Input: Reads with Same Cell Barcode</text>

                            <!-- Read 1 -->
                            <rect x="100" y="90" width="200" height="40" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                            <text x="110" y="105" font-size="9" fill="#666">BC: AAACCTGAGCGCTCCA-1</text>
                            <text x="110" y="120" font-size="9" fill="#666">UMI: AGTCAGTCAGTC</text>

                            <!-- Read 2 -->
                            <rect x="100" y="140" width="200" height="40" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                            <text x="110" y="155" font-size="9" fill="#666">BC: AAACCTGAGCGCTCCA-1</text>
                            <text x="110" y="170" font-size="9" fill="#666">UMI: AGTCAGTCAGTC</text>

                            <!-- Read 3 -->
                            <rect x="100" y="190" width="200" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="1"/>
                            <text x="110" y="205" font-size="9" fill="#666">BC: AAACCTGAGCGCTCCA-1</text>
                            <text x="110" y="220" font-size="9" fill="#666">UMI: TGCATGCATGCA</text>

                            <!-- Grouping arrow -->
                            <path d="M 320 150 L 380 150" stroke="#666" stroke-width="3" marker-end="url(#group-arrow)"/>
                            <text x="350" y="140" text-anchor="middle" font-size="10" fill="#666">Group by UMI</text>

                            <!-- Molecule groups -->
                            <text x="450" y="70" font-size="12" font-weight="bold" fill="#333">Molecule Groups</text>

                            <!-- Molecule 1 -->
                            <rect x="400" y="90" width="250" height="80" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="5"/>
                            <text x="525" y="110" text-anchor="middle" font-size="11" font-weight="bold" fill="#7b1fa2">Molecule 1</text>
                            <text x="410" y="130" font-size="9" fill="#666">UMI: AGTCAGTCAGTC</text>
                            <text x="410" y="145" font-size="9" fill="#666">Reads: 2 (PCR duplicates)</text>
                            <text x="410" y="160" font-size="9" fill="#666">Consensus mapping</text>

                            <!-- Molecule 2 -->
                            <rect x="400" y="190" width="250" height="60" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="5"/>
                            <text x="525" y="210" text-anchor="middle" font-size="11" font-weight="bold" fill="#7b1fa2">Molecule 2</text>
                            <text x="410" y="230" font-size="9" fill="#666">UMI: TGCATGCATGCA</text>
                            <text x="410" y="245" font-size="9" fill="#666">Reads: 1</text>

                            <!-- Molitem creation -->
                            <path d="M 670 150 L 730 150" stroke="#666" stroke-width="3" marker-end="url(#group-arrow)"/>
                            <text x="700" y="140" text-anchor="middle" font-size="10" fill="#666">Create Molitem</text>

                            <!-- Molitem objects -->
                            <text x="800" y="70" font-size="12" font-weight="bold" fill="#333">Molitem Objects</text>

                            <!-- Molitem 1 -->
                            <rect x="750" y="90" width="300" height="100" fill="#ffebee" stroke="#e91e63" stroke-width="2" rx="5"/>
                            <text x="900" y="110" text-anchor="middle" font-size="11" font-weight="bold" fill="#c2185b">Molitem 1</text>
                            <text x="760" y="130" font-size="9" fill="#666">mappings_record:</text>
                            <text x="770" y="145" font-size="8" fill="#666">TranscriptModel_A: [SegmentMatch1, SegmentMatch2]</text>
                            <text x="770" y="160" font-size="8" fill="#666">TranscriptModel_B: [SegmentMatch1]</text>
                            <text x="760" y="180" font-size="9" fill="#666">Ready for classification</text>

                            <!-- Molitem 2 -->
                            <rect x="750" y="210" width="300" height="80" fill="#ffebee" stroke="#e91e63" stroke-width="2" rx="5"/>
                            <text x="900" y="230" text-anchor="middle" font-size="11" font-weight="bold" fill="#c2185b">Molitem 2</text>
                            <text x="760" y="250" font-size="9" fill="#666">mappings_record:</text>
                            <text x="770" y="265" font-size="8" fill="#666">TranscriptModel_C: [SegmentMatch1]</text>
                            <text x="760" y="280" font-size="9" fill="#666">Ready for classification</text>

                            <!-- Process summary -->
                            <text x="700" y="330" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Key Benefits</text>
                            <text x="700" y="350" text-anchor="middle" font-size="10" fill="#666">• Eliminates PCR duplicates • Enables true molecule counting • Reduces technical noise</text>
                            <text x="700" y="370" text-anchor="middle" font-size="10" fill="#666">• Improves quantification accuracy • Essential for RNA velocity analysis</text>

                            <!-- Arrow marker -->
                            <defs>
                                <marker id="group-arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                    <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                                </marker>
                            </defs>
                        </svg>
                    </div>

                    <div class="code-box">
# Molecule assembly implementation
def assemble_molecules(reads_by_cell):
    """Group reads by UMI to create molecules"""

    molecules = {}

    for cell_bc, reads in reads_by_cell.items():
        # Group reads by UMI within each cell
        umi_groups = defaultdict(list)

        for read in reads:
            umi_groups[read.umi].append(read)

        # Create Molitem for each UMI group
        for umi, umi_reads in umi_groups.items():
            # Combine mapping records from all reads with same UMI
            combined_mappings = defaultdict(list)

            for read in umi_reads:
                read_mappings = feature_index.find_overlapping_ivls(read)

                # Intersect with existing mappings (consensus)
                if not combined_mappings:
                    combined_mappings = read_mappings
                else:
                    combined_mappings = intersect_mappings(combined_mappings, read_mappings)

            # Create Molitem object
            molitem = Molitem()
            molitem.add_mappings_record(combined_mappings)

            molecules[(cell_bc, umi)] = molitem

    return molecules
                    </div>

                    <div class="success-box">
                        <strong>✅ Molecule Assembly Benefits:</strong>
                        <ul>
                            <li><strong>PCR Duplicate Removal:</strong> Multiple reads with same UMI represent one molecule</li>
                            <li><strong>Consensus Mapping:</strong> Combines evidence from multiple reads</li>
                            <li><strong>Noise Reduction:</strong> Eliminates technical amplification bias</li>
                            <li><strong>True Quantification:</strong> Counts original mRNA molecules, not reads</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Section 7: Classification -->
            <div class="section" id="classification">
                <h2>7. Read Classification</h2>

                <div class="pipeline-step">
                    <div class="step-number">6</div>
                    <h3 style="margin-top: 0; padding-left: 60px;">Logic-Based Molecule Classification</h3>

                    <div class="svg-container">
                        <svg width="1400" height="500" viewBox="0 0 1400 500">
                            <!-- Background -->
                            <rect width="1400" height="500" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                            <!-- Title -->
                            <text x="700" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Classification Decision Tree (Permissive10X Logic)</text>

                            <!-- Input Molitem -->
                            <rect x="600" y="60" width="200" height="60" fill="#ffebee" stroke="#e91e63" stroke-width="2" rx="5"/>
                            <text x="700" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#c2185b">Input: Molitem</text>
                            <text x="700" y="105" text-anchor="middle" font-size="9" fill="#666">mappings_record with SegmentMatches</text>

                            <!-- Decision tree -->
                            <rect x="600" y="150" width="200" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5"/>
                            <text x="700" y="175" text-anchor="middle" font-size="11" fill="#856404">Maps to multiple genes?</text>

                            <!-- No mapping branch -->
                            <rect x="100" y="220" width="150" height="40" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="5"/>
                            <text x="175" y="245" text-anchor="middle" font-size="11" fill="#721c24">NOT COUNTED</text>

                            <!-- Single gene branch -->
                            <rect x="600" y="220" width="200" height="40" fill="#d1ecf1" stroke="#17a2b8" stroke-width="2" rx="5"/>
                            <text x="700" y="245" text-anchor="middle" font-size="11" fill="#0c5460">Single gene mapping</text>

                            <!-- Classification branches -->
                            <rect x="200" y="300" width="150" height="60" fill="#d4edda" stroke="#28a745" stroke-width="2" rx="5"/>
                            <text x="275" y="325" text-anchor="middle" font-size="11" font-weight="bold" fill="#155724">SPLICED</text>
                            <text x="275" y="345" text-anchor="middle" font-size="9" fill="#666">Exon-only reads</text>

                            <rect x="400" y="300" width="150" height="60" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5"/>
                            <text x="475" y="325" text-anchor="middle" font-size="11" font-weight="bold" fill="#856404">UNSPLICED</text>
                            <text x="475" y="345" text-anchor="middle" font-size="9" fill="#666">Intronic reads</text>

                            <rect x="600" y="300" width="150" height="60" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="5"/>
                            <text x="675" y="325" text-anchor="middle" font-size="11" font-weight="bold" fill="#7b1fa2">UNSPLICED</text>
                            <text x="675" y="345" text-anchor="middle" font-size="9" fill="#666">Exon-intron spanning</text>

                            <rect x="800" y="300" width="150" height="60" fill="#e2e3e5" stroke="#6c757d" stroke-width="2" rx="5"/>
                            <text x="875" y="325" text-anchor="middle" font-size="11" font-weight="bold" fill="#495057">AMBIGUOUS</text>
                            <text x="875" y="345" text-anchor="middle" font-size="9" fill="#666">Complex cases</text>

                            <!-- Decision arrows -->
                            <path d="M 700 130 L 700 150" stroke="#666" stroke-width="2" marker-end="url(#decision-arrow)"/>

                            <path d="M 650 190 L 200 220" stroke="#dc3545" stroke-width="2" marker-end="url(#decision-arrow)"/>
                            <text x="400" y="200" text-anchor="middle" font-size="9" fill="#dc3545">Yes / No mapping</text>

                            <path d="M 700 190 L 700 220" stroke="#17a2b8" stroke-width="2" marker-end="url(#decision-arrow)"/>
                            <text x="720" y="205" font-size="9" fill="#17a2b8">No</text>

                            <path d="M 650 260 L 275 300" stroke="#28a745" stroke-width="2" marker-end="url(#decision-arrow)"/>
                            <path d="M 675 260 L 475 300" stroke="#ffc107" stroke-width="2" marker-end="url(#decision-arrow)"/>
                            <path d="M 700 260 L 675 300" stroke="#9c27b0" stroke-width="2" marker-end="url(#decision-arrow)"/>
                            <path d="M 725 260 L 875 300" stroke="#6c757d" stroke-width="2" marker-end="url(#decision-arrow)"/>

                            <!-- Classification rules -->
                            <text x="700" y="410" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Permissive10X Classification Rules</text>

                            <rect x="50" y="420" width="1300" height="60" fill="white" stroke="#ccc" stroke-width="1"/>
                            <text x="70" y="440" font-size="10" fill="#666" font-weight="bold">SPLICED:</text>
                            <text x="70" y="455" font-size="9" fill="#666">• All segments map to exons only</text>
                            <text x="70" y="470" font-size="9" fill="#666">• No intronic or spanning evidence</text>

                            <text x="350" y="440" font-size="10" fill="#666" font-weight="bold">UNSPLICED:</text>
                            <text x="350" y="455" font-size="9" fill="#666">• Contains validated or unvalidated intronic reads</text>
                            <text x="350" y="470" font-size="9" fill="#666">• Contains exon-intron spanning reads</text>

                            <text x="700" y="440" font-size="10" fill="#666" font-weight="bold">AMBIGUOUS:</text>
                            <text x="700" y="455" font-size="9" fill="#666">• Complex mapping patterns</text>
                            <text x="700" y="470" font-size="9" fill="#666">• Conflicting evidence</text>

                            <text x="1000" y="440" font-size="10" fill="#666" font-weight="bold">NOT COUNTED:</text>
                            <text x="1000" y="455" font-size="9" fill="#666">• Maps to multiple genes</text>
                            <text x="1000" y="470" font-size="9" fill="#666">• No valid mappings</text>

                            <!-- Arrow marker -->
                            <defs>
                                <marker id="decision-arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                    <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                                </marker>
                            </defs>
                        </svg>
                    </div>

                    <div class="code-box">
# Classification implementation (Permissive10X logic)
def count(self, molitem, cell_bcidx, dict_layers_columns, geneid2ix):
    """Classify molecule and increment appropriate counter"""

    spliced = dict_layers_columns["spliced"]
    unspliced = dict_layers_columns["unspliced"]
    ambiguous = dict_layers_columns["ambiguous"]

    # No valid mappings
    if len(molitem.mappings_record) == 0:
        return 2  # Not counted

    # Check for multi-gene mapping
    gene_ids = set(tm.geneid for tm in molitem.mappings_record.keys())
    if len(gene_ids) > 1:
        return 2  # Not counted - ambiguous gene assignment

    # Single gene - analyze mapping patterns
    gene_id = list(gene_ids)[0]
    gene_idx = geneid2ix[gene_id]

    has_exonic = False
    has_intronic = False
    has_spanning = False

    for transcript_model, segment_matches in molitem.mappings_record.items():
        for sm in segment_matches:
            if sm.maps_to_exon:
                has_exonic = True
            elif sm.maps_to_intron:
                has_intronic = True
                # Check for exon-intron spanning
                if sm.feature.end_overlaps_with_part_of(sm.segment) or \
                   sm.feature.start_overlaps_with_part_of(sm.segment):
                    has_spanning = True

    # Classification decision
    if has_intronic or has_spanning:
        unspliced[cell_bcidx, gene_idx] += 1
        return 0  # Unspliced
    elif has_exonic:
        spliced[cell_bcidx, gene_idx] += 1
        return 1  # Spliced
    else:
        ambiguous[cell_bcidx, gene_idx] += 1
        return 3  # Ambiguous
                    </div>

                    <div class="info-box">
                        <strong>📊 Classification Strategy:</strong> Permissive10X maximizes information extraction by counting most reads, only excluding clear multi-gene mappings. This approach prioritizes sensitivity over specificity for RNA velocity analysis.
                    </div>
                </div>
            </div>

            <!-- Section 8: Batch Processing -->
            <div class="section" id="batch-processing">
                <h2>8. Batch Processing and Memory Management</h2>

                <div class="pipeline-step">
                    <div class="step-number">7</div>
                    <h3 style="margin-top: 0; padding-left: 60px;">Efficient Large-Scale Processing</h3>

                    <div class="svg-container">
                        <svg width="1400" height="400" viewBox="0 0 1400 400">
                            <!-- Background -->
                            <rect width="1400" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                            <!-- Title -->
                            <text x="700" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Batch Processing Strategy</text>

                            <!-- Memory timeline -->
                            <text x="100" y="70" font-size="12" font-weight="bold" fill="#333">Memory Usage Timeline</text>

                            <!-- Timeline axis -->
                            <line x1="100" y1="100" x2="1200" y2="100" stroke="#333" stroke-width="2"/>
                            <text x="650" y="90" text-anchor="middle" font-size="10" fill="#666">Processing Time →</text>

                            <!-- Batch markers -->
                            <line x1="200" y1="95" x2="200" y2="105" stroke="#666" stroke-width="2"/>
                            <text x="200" y="120" text-anchor="middle" font-size="9" fill="#666">Batch 1</text>

                            <line x1="400" y1="95" x2="400" y2="105" stroke="#666" stroke-width="2"/>
                            <text x="400" y="120" text-anchor="middle" font-size="9" fill="#666">Batch 2</text>

                            <line x1="600" y1="95" x2="600" y2="105" stroke="#666" stroke-width="2"/>
                            <text x="600" y="120" text-anchor="middle" font-size="9" fill="#666">Batch 3</text>

                            <line x1="800" y1="95" x2="800" y2="105" stroke="#666" stroke-width="2"/>
                            <text x="800" y="120" text-anchor="middle" font-size="9" fill="#666">Batch 4</text>

                            <!-- Memory usage curve -->
                            <path d="M 100 150 Q 200 130 300 150 Q 400 130 500 150 Q 600 130 700 150 Q 800 130 900 150"
                                  stroke="#e74c3c" stroke-width="3" fill="none"/>
                            <text x="500" y="140" text-anchor="middle" font-size="10" fill="#e74c3c">Memory Usage</text>

                            <!-- Batch processing details -->
                            <text x="100" y="200" font-size="12" font-weight="bold" fill="#333">Batch Processing Steps</text>

                            <!-- Step boxes -->
                            <rect x="100" y="220" width="250" height="80" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                            <text x="225" y="240" text-anchor="middle" font-size="11" font-weight="bold" fill="#1976d2">1. Accumulate Reads</text>
                            <text x="110" y="260" font-size="9" fill="#666">• Collect reads by cell barcode</text>
                            <text x="110" y="275" font-size="9" fill="#666">• Stop at batch_size (default: 100 cells)</text>
                            <text x="110" y="290" font-size="9" fill="#666">• Maintain cell integrity</text>

                            <rect x="400" y="220" width="250" height="80" fill="#fff3e0" stroke="#f57c00" stroke-width="1"/>
                            <text x="525" y="240" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">2. Process Batch</text>
                            <text x="410" y="260" font-size="9" fill="#666">• Map reads to features</text>
                            <text x="410" y="275" font-size="9" fill="#666">• Assemble molecules</text>
                            <text x="410" y="290" font-size="9" fill="#666">• Classify and count</text>

                            <rect x="700" y="220" width="250" height="80" fill="#e8f5e8" stroke="#4caf50" stroke-width="1"/>
                            <text x="825" y="240" text-anchor="middle" font-size="11" font-weight="bold" fill="#4caf50">3. Update Matrices</text>
                            <text x="710" y="260" font-size="9" fill="#666">• Increment count matrices</text>
                            <text x="710" y="275" font-size="9" fill="#666">• Filter low-count cells</text>
                            <text x="710" y="290" font-size="9" fill="#666">• Log statistics</text>

                            <rect x="1000" y="220" width="250" height="80" fill="#f3e5f5" stroke="#9c27b0" stroke-width="1"/>
                            <text x="1125" y="240" text-anchor="middle" font-size="11" font-weight="bold" fill="#9c27b0">4. Reset State</text>
                            <text x="1010" y="260" font-size="9" fill="#666">• Clear batch data</text>
                            <text x="1010" y="275" font-size="9" fill="#666">• Reset feature indexes</text>
                            <text x="1010" y="290" font-size="9" fill="#666">• Prepare for next batch</text>

                            <!-- Benefits -->
                            <text x="700" y="340" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Memory Management Benefits</text>
                            <text x="700" y="360" text-anchor="middle" font-size="10" fill="#666">• Constant memory usage regardless of dataset size</text>
                            <text x="700" y="380" text-anchor="middle" font-size="10" fill="#666">• Enables processing of datasets with millions of cells</text>
                        </svg>
                    </div>

                    <div class="code-box">
# Batch processing implementation
def count_cell_batch(self):
    """Process current batch of cells"""

    # Group reads by cell barcode and UMI
    molecules_by_cell = defaultdict(dict)

    for read in self.reads_to_count:
        cell_bc = read.bc
        umi = read.umi

        # Create molecule key
        mol_key = (cell_bc, umi)

        if mol_key not in molecules_by_cell[cell_bc]:
            molecules_by_cell[cell_bc][mol_key] = []

        molecules_by_cell[cell_bc][mol_key].append(read)

    # Process each molecule
    for cell_bc, molecules in molecules_by_cell.items():
        if self.filter_mode and cell_bc not in self.valid_bcset:
            continue

        cell_idx = self.cell_bcs_order.index(cell_bc)

        for mol_key, reads in molecules.items():
            # Create Molitem from reads
            molitem = self.create_molitem(reads)

            # Classify molecule
            self.logic.count(molitem, cell_idx, self.dict_list_arrays, self.geneid2ix)

    # Reset for next batch
    self.cell_batch.clear()
    self.reads_to_count.clear()

    # Reset feature indexes
    for feature_index in self.feature_indexes.values():
        feature_index.reset()
                    </div>

                    <div class="success-box">
                        <strong>✅ Batch Processing Advantages:</strong>
                        <ul>
                            <li><strong>Memory Efficiency:</strong> Constant memory usage regardless of dataset size</li>
                            <li><strong>Scalability:</strong> Can process millions of cells with limited RAM</li>
                            <li><strong>Cell Integrity:</strong> Never splits cells across batches</li>
                            <li><strong>Index Optimization:</strong> Reset preserves search efficiency</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Section 9: Output Generation -->
            <div class="section" id="output-generation">
                <h2>9. Output Generation</h2>

                <div class="pipeline-step">
                    <div class="step-number">8</div>
                    <h3 style="margin-top: 0; padding-left: 60px;">Count Matrix Creation and Export</h3>

                    <div class="svg-container">
                        <svg width="1400" height="400" viewBox="0 0 1400 400">
                            <!-- Background -->
                            <rect width="1400" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                            <!-- Title -->
                            <text x="700" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Output Matrix Generation</text>

                            <!-- Count matrices -->
                            <text x="200" y="70" font-size="12" font-weight="bold" fill="#333">Count Matrices</text>

                            <!-- Spliced matrix -->
                            <rect x="100" y="90" width="150" height="100" fill="#d4edda" stroke="#28a745" stroke-width="2" rx="5"/>
                            <text x="175" y="110" text-anchor="middle" font-size="11" font-weight="bold" fill="#155724">Spliced (S)</text>
                            <text x="110" y="130" font-size="9" fill="#666">Cells × Genes</text>
                            <text x="110" y="145" font-size="9" fill="#666">Mature mRNA</text>
                            <text x="110" y="160" font-size="9" fill="#666">Exon-only reads</text>
                            <text x="110" y="175" font-size="9" fill="#666">~60-80% of reads</text>

                            <!-- Unspliced matrix -->
                            <rect x="300" y="90" width="150" height="100" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5"/>
                            <text x="375" y="110" text-anchor="middle" font-size="11" font-weight="bold" fill="#856404">Unspliced (U)</text>
                            <text x="310" y="130" font-size="9" fill="#666">Cells × Genes</text>
                            <text x="310" y="145" font-size="9" fill="#666">Pre-mRNA</text>
                            <text x="310" y="160" font-size="9" fill="#666">Intronic reads</text>
                            <text x="310" y="175" font-size="9" fill="#666">~15-30% of reads</text>

                            <!-- Ambiguous matrix -->
                            <rect x="500" y="90" width="150" height="100" fill="#e2e3e5" stroke="#6c757d" stroke-width="2" rx="5"/>
                            <text x="575" y="110" text-anchor="middle" font-size="11" font-weight="bold" fill="#495057">Ambiguous (A)</text>
                            <text x="510" y="130" font-size="9" fill="#666">Cells × Genes</text>
                            <text x="510" y="145" font-size="9" fill="#666">Complex cases</text>
                            <text x="510" y="160" font-size="9" fill="#666">Multi-mapping</text>
                            <text x="510" y="175" font-size="9" fill="#666">~5-15% of reads</text>

                            <!-- Export formats -->
                            <text x="900" y="70" font-size="12" font-weight="bold" fill="#333">Export Formats</text>

                            <!-- Loom file -->
                            <rect x="800" y="90" width="200" height="60" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
                            <text x="900" y="115" text-anchor="middle" font-size="11" font-weight="bold" fill="#1976d2">Loom File (.loom)</text>
                            <text x="810" y="135" font-size="9" fill="#666">• HDF5-based format</text>
                            <text x="810" y="145" font-size="9" fill="#666">• Includes metadata</text>

                            <!-- H5 file -->
                            <rect x="1050" y="90" width="200" height="60" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                            <text x="1150" y="115" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">H5 File (.h5)</text>
                            <text x="1060" y="135" font-size="9" fill="#666">• Compressed matrices</text>
                            <text x="1060" y="145" font-size="9" fill="#666">• Fast loading</text>

                            <!-- Matrix structure visualization -->
                            <text x="700" y="230" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Matrix Structure Example</text>

                            <!-- Example matrix -->
                            <rect x="400" y="250" width="600" height="120" fill="white" stroke="#ccc" stroke-width="1"/>

                            <!-- Headers -->
                            <text x="420" y="270" font-size="9" fill="#666" font-weight="bold">Cells ↓ / Genes →</text>
                            <text x="520" y="270" font-size="9" fill="#666" font-weight="bold">GAPDH</text>
                            <text x="580" y="270" font-size="9" fill="#666" font-weight="bold">ACTB</text>
                            <text x="640" y="270" font-size="9" fill="#666" font-weight="bold">MYC</text>
                            <text x="700" y="270" font-size="9" fill="#666" font-weight="bold">TP53</text>
                            <text x="760" y="270" font-size="9" fill="#666" font-weight="bold">...</text>

                            <!-- Cell rows -->
                            <text x="420" y="290" font-size="9" fill="#666">Cell_001</text>
                            <text x="520" y="290" font-size="9" fill="#666">45</text>
                            <text x="580" y="290" font-size="9" fill="#666">23</text>
                            <text x="640" y="290" font-size="9" fill="#666">12</text>
                            <text x="700" y="290" font-size="9" fill="#666">8</text>
                            <text x="760" y="290" font-size="9" fill="#666">...</text>

                            <text x="420" y="310" font-size="9" fill="#666">Cell_002</text>
                            <text x="520" y="310" font-size="9" fill="#666">38</text>
                            <text x="580" y="310" font-size="9" fill="#666">19</text>
                            <text x="640" y="310" font-size="9" fill="#666">15</text>
                            <text x="700" y="310" font-size="9" fill="#666">6</text>
                            <text x="760" y="310" font-size="9" fill="#666">...</text>

                            <text x="420" y="330" font-size="9" fill="#666">Cell_003</text>
                            <text x="520" y="330" font-size="9" fill="#666">52</text>
                            <text x="580" y="330" font-size="9" fill="#666">31</text>
                            <text x="640" y="330" font-size="9" fill="#666">9</text>
                            <text x="700" y="330" font-size="9" fill="#666">11</text>
                            <text x="760" y="330" font-size="9" fill="#666">...</text>

                            <text x="420" y="350" font-size="9" fill="#666">...</text>
                            <text x="520" y="350" font-size="9" fill="#666">...</text>
                            <text x="580" y="350" font-size="9" fill="#666">...</text>
                            <text x="640" y="350" font-size="9" fill="#666">...</text>
                            <text x="700" y="350" font-size="9" fill="#666">...</text>
                            <text x="760" y="350" font-size="9" fill="#666">...</text>
                        </svg>
                    </div>

                    <div class="code-box">
# Output generation implementation
def save_count_matrices(self, output_folder):
    """Save count matrices in multiple formats"""

    # Convert sparse matrices to dense for export
    spliced_dense = self.dict_list_arrays["spliced"].toarray()
    unspliced_dense = self.dict_list_arrays["unspliced"].toarray()
    ambiguous_dense = self.dict_list_arrays["ambiguous"].toarray()

    # Create loom file
    loom_path = os.path.join(output_folder, f"{self.sampleid}.loom")

    # Prepare metadata
    cell_attrs = {
        "CellID": self.cell_bcs_order,
        "nUMI_spliced": np.sum(spliced_dense, axis=1),
        "nUMI_unspliced": np.sum(unspliced_dense, axis=1),
        "nUMI_ambiguous": np.sum(ambiguous_dense, axis=1)
    }

    gene_attrs = {
        "Gene": list(self.geneid2ix.keys()),
        "Accession": list(self.geneid2ix.keys())
    }

    # Create loom file with layers
    loompy.create(
        filename=loom_path,
        layers={
            "spliced": spliced_dense.T,      # Loom format: genes × cells
            "unspliced": unspliced_dense.T,
            "ambiguous": ambiguous_dense.T
        },
        row_attrs=gene_attrs,
        col_attrs=cell_attrs
    )

    # Also save as H5 for faster loading
    h5_path = os.path.join(output_folder, f"{self.sampleid}.h5")
    with h5py.File(h5_path, 'w') as f:
        f.create_dataset("spliced", data=spliced_dense, compression="gzip")
        f.create_dataset("unspliced", data=unspliced_dense, compression="gzip")
        f.create_dataset("ambiguous", data=ambiguous_dense, compression="gzip")
        f.create_dataset("cell_bcs", data=self.cell_bcs_order)
        f.create_dataset("gene_ids", data=list(self.geneid2ix.keys()))
                    </div>

                    <div class="info-box">
                        <strong>📊 Output Characteristics:</strong>
                        <ul>
                            <li><strong>Matrix Dimensions:</strong> Cells × Genes (typically 10,000 × 30,000)</li>
                            <li><strong>Sparsity:</strong> ~95-99% zeros (most gene-cell combinations have no reads)</li>
                            <li><strong>File Sizes:</strong> 100MB-1GB depending on dataset size and compression</li>
                            <li><strong>Metadata:</strong> Cell barcodes, gene IDs, UMI counts per layer</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Section 10: Performance -->
            <div class="section" id="performance">
                <h2>10. Performance Optimization</h2>

                <div class="pipeline-step">
                    <div class="step-number">9</div>
                    <h3 style="margin-top: 0; padding-left: 60px;">Scalability and Optimization Strategies</h3>

                    <div class="svg-container">
                        <svg width="1400" height="350" viewBox="0 0 1400 350">
                            <!-- Background -->
                            <rect width="1400" height="350" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                            <!-- Title -->
                            <text x="700" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Performance Optimization Strategies</text>

                            <!-- Optimization categories -->
                            <rect x="50" y="60" width="300" height="120" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
                            <text x="200" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#1976d2">Memory Optimization</text>
                            <text x="70" y="105" font-size="9" fill="#666">• Batch processing (100 cells)</text>
                            <text x="70" y="120" font-size="9" fill="#666">• Sparse matrix storage</text>
                            <text x="70" y="135" font-size="9" fill="#666">• Index state preservation</text>
                            <text x="70" y="150" font-size="9" fill="#666">• Garbage collection between batches</text>
                            <text x="70" y="165" font-size="9" fill="#666">• Memory mapping for large files</text>

                            <rect x="400" y="60" width="300" height="120" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                            <text x="550" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">Computational Optimization</text>
                            <text x="420" y="105" font-size="9" fill="#666">• Sorted feature indexes (O(log n))</text>
                            <text x="420" y="120" font-size="9" fill="#666">• Early termination in searches</text>
                            <text x="420" y="135" font-size="9" fill="#666">• Vectorized operations</text>
                            <text x="420" y="150" font-size="9" fill="#666">• Efficient data structures</text>
                            <text x="420" y="165" font-size="9" fill="#666">• Minimal object creation</text>

                            <rect x="750" y="60" width="300" height="120" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                            <text x="900" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">I/O Optimization</text>
                            <text x="770" y="105" font-size="9" fill="#666">• Sequential BAM file reading</text>
                            <text x="770" y="120" font-size="9" fill="#666">• Compressed output formats</text>
                            <text x="770" y="135" font-size="9" fill="#666">• Buffered file operations</text>
                            <text x="770" y="150" font-size="9" fill="#666">• SSD storage recommended</text>
                            <text x="770" y="165" font-size="9" fill="#666">• Parallel chromosome processing</text>

                            <rect x="1100" y="60" width="250" height="120" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="5"/>
                            <text x="1225" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#9c27b0">Algorithmic Optimization</text>
                            <text x="1120" y="105" font-size="9" fill="#666">• Pluggable logic classes</text>
                            <text x="1120" y="120" font-size="9" fill="#666">• Efficient overlap detection</text>
                            <text x="1120" y="135" font-size="9" fill="#666">• Smart intron validation</text>
                            <text x="1120" y="150" font-size="9" fill="#666">• Optimized classification</text>
                            <text x="1120" y="165" font-size="9" fill="#666">• Minimal redundant work</text>

                            <!-- Performance metrics -->
                            <text x="700" y="220" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Typical Performance Metrics</text>

                            <rect x="100" y="240" width="1200" height="80" fill="white" stroke="#ccc" stroke-width="1"/>

                            <!-- Dataset sizes -->
                            <text x="120" y="260" font-size="10" fill="#666" font-weight="bold">Dataset Size:</text>
                            <text x="120" y="275" font-size="9" fill="#666">Small (1k cells, 10M reads): 15-30 minutes</text>
                            <text x="120" y="290" font-size="9" fill="#666">Medium (10k cells, 100M reads): 2-6 hours</text>
                            <text x="120" y="305" font-size="9" fill="#666">Large (100k cells, 1B reads): 12-24 hours</text>

                            <!-- Memory requirements -->
                            <text x="450" y="260" font-size="10" fill="#666" font-weight="bold">Memory Requirements:</text>
                            <text x="450" y="275" font-size="9" fill="#666">Human genome indexes: 3-6 GB</text>
                            <text x="450" y="290" font-size="9" fill="#666">Batch processing: 2-4 GB</text>
                            <text x="450" y="305" font-size="9" fill="#666">Total recommended: 16-32 GB</text>

                            <!-- Bottlenecks -->
                            <text x="750" y="260" font-size="10" fill="#666" font-weight="bold">Common Bottlenecks:</text>
                            <text x="750" y="275" font-size="9" fill="#666">1. BAM file I/O (use SSD)</text>
                            <text x="750" y="290" font-size="9" fill="#666">2. Feature intersection (optimized)</text>
                            <text x="750" y="305" font-size="9" fill="#666">3. Memory allocation (batching)</text>

                            <!-- Scaling -->
                            <text x="1050" y="260" font-size="10" fill="#666" font-weight="bold">Scaling Factors:</text>
                            <text x="1050" y="275" font-size="9" fill="#666">Linear with read count</text>
                            <text x="1050" y="290" font-size="9" fill="#666">Constant memory usage</text>
                            <text x="1050" y="305" font-size="9" fill="#666">Parallelizable by chromosome</text>
                        </svg>
                    </div>

                    <div class="code-box">
# Performance optimization tips
def optimize_velocyto_run():
    """Best practices for optimal velocyto performance"""

    # 1. Hardware recommendations
    hardware_tips = {
        "RAM": "16-32 GB for human genome",
        "Storage": "SSD for BAM files (10x faster I/O)",
        "CPU": "Multi-core for parallel processing"
    }

    # 2. Parameter tuning
    optimal_params = {
        "cell_batch_size": 100,  # Balance memory vs efficiency
        "min_flank": 5,          # Filter spurious overlaps
        "splice_inaccuracy": 10  # Splice junction tolerance
    }

    # 3. Input preparation
    preprocessing_tips = [
        "Sort BAM files by position",
        "Index BAM files with samtools",
        "Filter low-quality alignments",
        "Use appropriate cell barcode whitelist"
    ]

    # 4. Memory management
    memory_tips = [
        "Process chromosomes separately if memory limited",
        "Use filter_mode to exclude empty droplets",
        "Monitor memory usage during processing",
        "Adjust batch size based on available RAM"
    ]

    return {
        "hardware": hardware_tips,
        "parameters": optimal_params,
        "preprocessing": preprocessing_tips,
        "memory": memory_tips
    }
                    </div>

                    <div class="success-box">
                        <strong>✅ Performance Summary:</strong>
                        <ul>
                            <li><strong>Scalable Architecture:</strong> Handles datasets from thousands to millions of cells</li>
                            <li><strong>Memory Efficient:</strong> Constant memory usage through batch processing</li>
                            <li><strong>Optimized Algorithms:</strong> O(log n) feature lookups with smart caching</li>
                            <li><strong>Production Ready:</strong> Robust error handling and comprehensive logging</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Summary -->
            <div class="section">
                <h2>Pipeline Summary</h2>

                <div class="highlight" style="display: block; text-align: center; padding: 1rem; margin: 2rem 0; font-size: 1.1rem;">
                    🎯 <strong>Complete Pipeline:</strong> From BAM files to count matrices, velocyto implements a sophisticated, memory-efficient pipeline that accurately classifies reads for RNA velocity analysis while maintaining scalability for large single-cell datasets.
                </div>

                <h3>Key Pipeline Stages</h3>
                <div class="data-flow">
                    <div class="flow-item">
                        <strong>1. Preparation</strong><br>
                        <small>Build indexes</small>
                    </div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-item">
                        <strong>2. Read Parsing</strong><br>
                        <small>Extract bc/umi/segments</small>
                    </div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-item">
                        <strong>3. Feature Mapping</strong><br>
                        <small>Find overlaps</small>
                    </div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-item">
                        <strong>4. Validation</strong><br>
                        <small>Mark spanning introns</small>
                    </div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-item">
                        <strong>5. Assembly</strong><br>
                        <small>Group by UMI</small>
                    </div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-item">
                        <strong>6. Classification</strong><br>
                        <small>Apply logic rules</small>
                    </div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-item">
                        <strong>7. Output</strong><br>
                        <small>Generate matrices</small>
                    </div>
                </div>

                <h3>Technical Excellence</h3>
                <div class="info-box">
                    <strong>🏗️ Architecture Highlights:</strong>
                    <ul>
                        <li><strong>Batch Processing:</strong> Memory-efficient processing of large datasets</li>
                        <li><strong>Smart Indexing:</strong> O(log n) genomic feature lookups</li>
                        <li><strong>Quality Control:</strong> Intron validation prevents false positives</li>
                        <li><strong>Flexible Logic:</strong> Pluggable classification algorithms</li>
                        <li><strong>Robust Output:</strong> Multiple export formats with comprehensive metadata</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
