<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MOLITEM in Velocyto</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        header {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            margin: 0;
            font-size: 2.5rem;
        }
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-top: 0.5rem;
        }
        .section {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        }
        .section h2 {
            color: #2575fc;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 0.5rem;
            margin-top: 0;
        }
        .card-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 1.5rem 0;
        }
        .card {
            background: #f1f3f5;
            border-radius: 8px;
            padding: 1.2rem;
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card h3 {
            margin-top: 0;
            color: #495057;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }
        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        th {
            background-color: #e9ecef;
            font-weight: 600;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .visualization {
            text-align: center;
            margin: 2rem 0;
        }
        .conclusion {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            margin: 2rem 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .conclusion h2 {
            margin-top: 0;
        }
        .key-point {
            background: rgba(255,255,255,0.2);
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        pre {
            background: #f1f3f5;
            padding: 1rem;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            font-family: 'Courier New', monospace;
        }
        .highlight {
            background: #fff3cd;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-weight: bold;
        }
        .important {
            background: #ffeaa7;
            padding: 1rem;
            border-left: 4px solid #fdcb6e;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <header>
        <h1>MOLITEM in Velocyto</h1>
        <div class="subtitle">Understanding the Molecule Item and Its Role in RNA Velocity Analysis</div>
    </header>

    <div class="section">
        <h2>What is MOLITEM?</h2>
        <p><strong>MOLITEM</strong> (short for "Molecule Item") is a fundamental data structure in velocyto that represents a molecule in the counting pipeline. It's a crucial component for grouping reads and performing accurate quantification of spliced and unspliced RNA.</p>
        
        <div class="important">
            <p><strong>Key Concept:</strong> A MOLITEM groups all reads that share the same cell barcode (BC) and unique molecular identifier (UMI), representing a single molecule in a single cell.</p>
        </div>
        
        <p>In single-cell RNA sequencing, each MOLITEM represents:</p>
        <ul>
            <li>A single mRNA molecule in a specific cell</li>
            <li>All sequencing reads derived from amplification of that molecule</li>
            <li>The mapping information of those reads to annotated transcript models</li>
        </ul>
    </div>

    <div class="section">
        <h2>Molitem Class Structure</h2>
        
        <pre><code>class Molitem:
    """Object that represents a molecule in the counting pipeline"""
    __slots__ = ["mappings_record"]
    
    def __init__(self) -> None:
        # Dictionary mapping transcript models to lists of segment matches
        self.mappings_record: DefaultDict[TranscriptModel, List[SegmentMatch]] = None
        
    def add_mappings_record(self, mappings_record: DefaultDict[TranscriptModel, List[SegmentMatch]]) -> None:
        """Add mapping information to this molecule"""
        if self.mappings_record is None:
            self.mappings_record = mappings_record
        else:
            # When a read aligns to multiple locations, we take the intersection 
            # of compatible transcript models
            self.mappings_record = dictionary_intersect(self.mappings_record, mappings_record)</code></pre>
        
        <h3>Key Attributes:</h3>
        <table>
            <thead>
                <tr>
                    <th>Attribute</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><code>mappings_record</code></td>
                    <td>A dictionary mapping transcript models to lists of segment matches</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="section">
        <h2>Relationship with Other Components</h2>
        
        <div class="card-container">
            <div class="card">
                <h3>Read</h3>
                <p>Individual sequencing reads with cell barcode, UMI, and alignment information.</p>
                <p>Multiple reads with same BC+UMI are grouped into one MOLITEM.</p>
            </div>
            
            <div class="card">
                <h3>MOLITEM</h3>
                <p>Represents a single molecule in a cell.</p>
                <p>Contains mapping information for all reads of that molecule.</p>
            </div>
            
            <div class="card">
                <h3>TranscriptModel</h3>
                <p>Gene structure with exons and introns.</p>
                <p>MOLITEM maps to one or more transcript models.</p>
            </div>
            
            <div class="card">
                <h3>SegmentMatch</h3>
                <p>Represents alignment of read segments to genomic features.</p>
                <p>Links MOLITEM to specific parts of transcript models.</p>
            </div>
        </div>
    </div>

    <div class="visualization">
        <h2>Visualizing MOLITEM Relationships</h2>
        <svg width="100%" height="700" viewBox="0 0 900 700" xmlns="http://www.w3.org/2000/svg">
            <!-- Background -->
            <rect width="100%" height="100%" fill="#f8f9fa"/>
            
            <!-- Title -->
            <text x="450" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#333">
                MOLITEM Relationships in Velocyto
            </text>
            
            <!-- Cell -->
            <rect x="50" y="70" width="800" height="580" fill="#e9ecef" rx="10"/>
            <text x="450" y="90" text-anchor="middle" font-size="16" font-weight="bold" fill="#495057">Single Cell</text>
            
            <!-- Reads with same BC and UMI -->
            <text x="100" y="130" font-size="14" font-weight="bold" fill="#007bff">Reads with same BC+UMI:</text>
            
            <!-- Read 1 -->
            <rect x="120" y="150" width="200" height="30" fill="#007bff" rx="5"/>
            <text x="220" y="170" text-anchor="middle" font-size="12" fill="white">Read 1: BC=ATCG, UMI=AAAA</text>
            
            <!-- Read 2 -->
            <rect x="120" y="190" width="200" height="30" fill="#007bff" rx="5"/>
            <text x="220" y="210" text-anchor="middle" font-size="12" fill="white">Read 2: BC=ATCG, UMI=AAAA</text>
            
            <!-- Read 3 -->
            <rect x="120" y="230" width="200" height="30" fill="#007bff" rx="5"/>
            <text x="220" y="250" text-anchor="middle" font-size="12" fill="white">Read 3: BC=ATCG, UMI=AAAA</text>
            
            <!-- Grouping arrow -->
            <line x1="330" y1="190" x2="400" y2="190" stroke="#007bff" stroke-width="2" marker-end="url(#arrow)"/>
            
            <!-- MOLITEM -->
            <rect x="400" y="150" width="300" height="100" fill="#28a745" rx="10"/>
            <text x="550" y="170" text-anchor="middle" font-size="16" font-weight="bold" fill="white">MOLITEM</text>
            <text x="550" y="190" text-anchor="middle" font-size="12" fill="white">BC=ATCG, UMI=AAAA</text>
            <text x="550" y="210" text-anchor="middle" font-size="12" fill="white">Represents one mRNA molecule</text>
            <text x="550" y="230" text-anchor="middle" font-size="12" fill="white">in this cell</text>
            
            <!-- Mapping information -->
            <text x="100" y="300" font-size="14" font-weight="bold" fill="#6f42c1">Mapping Information:</text>
            
            <!-- Transcript Model 1 -->
            <rect x="120" y="320" width="250" height="60" fill="#f1f3f5" rx="5" stroke="#6f42c1" stroke-width="2"/>
            <text x="245" y="340" text-anchor="middle" font-size="12" font-weight="bold" fill="#6f42c1">TranscriptModel: GeneA-201</text>
            <text x="245" y="360" text-anchor="middle" font-size="10" fill="#6c757d">[Exon1, Intron1, Exon2]</text>
            
            <!-- Segment Matches for Transcript Model 1 -->
            <rect x="130" y="390" width="230" height="80" fill="#e9ecef" rx="5"/>
            <text x="245" y="405" text-anchor="middle" font-size="10" fill="#495057">Segment Matches:</text>
            
            <!-- Segment Match 1 -->
            <rect x="140" y="420" width="100" height="20" fill="#6c757d" rx="3"/>
            <text x="190" y="435" text-anchor="middle" font-size="8" fill="white">SegmentMatch 1</text>
            <text x="190" y="445" text-anchor="middle" font-size="7" fill="#495057">Maps to Exon1</text>
            
            <!-- Segment Match 2 -->
            <rect x="140" y="450" width="100" height="20" fill="#6c757d" rx="3"/>
            <text x="190" y="465" text-anchor="middle" font-size="8" fill="white">SegmentMatch 2</text>
            <text x="190" y="475" text-anchor="middle" font-size="7" fill="#495057">Maps to Exon2</text>
            
            <!-- Transcript Model 2 -->
            <rect x="450" y="320" width="250" height="60" fill="#f1f3f5" rx="5" stroke="#6f42c1" stroke-width="2"/>
            <text x="575" y="340" text-anchor="middle" font-size="12" font-weight="bold" fill="#6f42c1">TranscriptModel: GeneA-202</text>
            <text x="575" y="360" text-anchor="middle" font-size="10" fill="#6c757d">[Exon1, Intron1, Exon2, Intron2, Exon3]</text>
            
            <!-- Segment Matches for Transcript Model 2 -->
            <rect x="460" y="390" width="230" height="80" fill="#e9ecef" rx="5"/>
            <text x="575" y="405" text-anchor="middle" font-size="10" fill="#495057">Segment Matches:</text>
            
            <!-- Segment Match 3 -->
            <rect x="470" y="420" width="100" height="20" fill="#6c757d" rx="3"/>
            <text x="520" y="435" text-anchor="middle" font-size="8" fill="white">SegmentMatch 3</text>
            <text x="520" y="445" text-anchor="middle" font-size="7" fill="#495057">Maps to Exon1</text>
            
            <!-- Segment Match 4 -->
            <rect x="470" y="450" width="100" height="20" fill="#6c757d" rx="3"/>
            <text x="520" y="465" text-anchor="middle" font-size="8" fill="white">SegmentMatch 4</text>
            <text x="520" y="475" text-anchor="middle" font-size="7" fill="#495057">Maps to Exon2</text>
            
            <!-- Mappings record in MOLITEM -->
            <line x1="550" y1="250" x2="300" y2="320" stroke="#28a745" stroke-width="2" stroke-dasharray="5,5"/>
            <line x1="550" y1="250" x2="575" y2="320" stroke="#28a745" stroke-width="2" stroke-dasharray="5,5"/>
            
            <rect x="350" y="270" width="200" height="40" fill="#20c997" rx="5"/>
            <text x="450" y="290" text-anchor="middle" font-size="12" fill="white">mappings_record Dictionary</text>
            <text x="450" y="305" text-anchor="middle" font-size="10" fill="white">{TranscriptModel: [SegmentMatch]}</text>
            
            <!-- Counting process -->
            <text x="100" y="520" font-size="14" font-weight="bold" fill="#fd7e14">Counting Process:</text>
            
            <rect x="120" y="540" width="650" height="100" fill="#f1f3f5" rx="10" stroke="#fd7e14" stroke-width="2"/>
            <text x="445" y="560" text-anchor="middle" font-size="12" font-weight="bold" fill="#fd7e14">Classification Logic</text>
            <text x="445" y="580" text-anchor="middle" font-size="10" fill="#495057">Analyzes mapping patterns to classify as:</text>
            
            <!-- Classification results -->
            <rect x="150" y="590" width="120" height="30" fill="#28a745" rx="5"/>
            <text x="210" y="610" text-anchor="middle" font-size="10" fill="white">Spliced</text>
            
            <rect x="300" y="590" width="120" height="30" fill="#6c757d" rx="5"/>
            <text x="360" y="610" text-anchor="middle" font-size="10" fill="white">Unspliced</text>
            
            <rect x="450" y="590" width="120" height="30" fill="#ffc107" rx="5"/>
            <text x="510" y="610" text-anchor="middle" font-size="10" fill="#333">Ambiguous</text>
            
            <rect x="600" y="590" width="120" height="30" fill="#e9ecef" rx="5"/>
            <text x="660" y="610" text-anchor="middle" font-size="10" fill="#333">Discarded</text>
            
            <!-- Arrow markers -->
            <defs>
                <marker id="arrow" markerWidth="10" markerHeight="10" refX="6" refY="3" orient="auto" markerUnits="strokeWidth">
                    <path d="M0,0 L0,6 L9,3 z" fill="#007bff"/>
                </marker>
            </defs>
        </svg>
    </div>

    <div class="section">
        <h2>How MOLITEM Is Used in the Counting Pipeline</h2>
        
        <h3>1. Read Grouping</h3>
        <pre><code># In the counting process, reads are grouped by cell barcode and UMI:
bcumi = f"{r.bc}${r.umi}"
molitems[bcumi].add_mappings_record(mappings_record)</code></pre>
        
        <h3>2. Mapping Record Aggregation</h3>
        <pre><code>def add_mappings_record(self, mappings_record: DefaultDict[TranscriptModel, List[SegmentMatch]]) -> None:
    if self.mappings_record is None:
        self.mappings_record = mappings_record
    else:
        # For reads with multiple alignments, take intersection of compatible transcript models
        self.mappings_record = dictionary_intersect(self.mappings_record, mappings_record)</code></pre>
        
        <h3>3. Classification</h3>
        <pre><code># After all reads are processed, each MOLITEM is classified:
for bcumi, molitem in molitems.items():
    # Use the selected logic to classify and count this molecule
    self.logic.count(molitem, bcidx, dict_layers_columns, self.geneid2ix)</code></pre>
    </div>

    <div class="section">
        <h2>Benefits of Using MOLITEMs</h2>
        
        <div class="card-container">
            <div class="card">
                <h3>Reduces Amplification Bias</h3>
                <p>By grouping reads with the same UMI, PCR duplicates are collapsed, providing a more accurate count of original molecules.</p>
            </div>
            
            <div class="card">
                <h3>Improves Quantification Accuracy</h3>
                <p>Combining information from all reads of a molecule allows for more confident mapping and classification.</p>
            </div>
            
            <div class="card">
                <h3>Handles Multi-mapping</h3>
                <p>For reads that map to multiple locations, MOLITEMs can use intersection operations to find compatible transcript models.</p>
            </div>
        </div>
    </div>

    <div class="conclusion">
        <h2>Key Takeaways</h2>
        
        <div class="key-point">
            <h3>1. MOLITEM Represents a Single Molecule</h3>
            <p>A MOLITEM groups all reads sharing the same cell barcode and UMI, representing a single mRNA molecule in a cell.</p>
        </div>
        
        <div class="key-point">
            <h3>2. Central Component in Counting Pipeline</h3>
            <p>MOLITEMs serve as the central data structure connecting reads, transcript models, and the final counting process.</p>
        </div>
        
        <div class="key-point">
            <h3>3. Enables Accurate Quantification</h3>
            <p>By aggregating information from multiple reads of the same molecule, MOLITEMs enable more accurate classification of spliced/unspliced status.</p>
        </div>
        
        <div class="key-point">
            <h3>4. Handles Complex Mapping Scenarios</h3>
            <p>MOLITEMs can handle multi-mapping reads by finding intersections of compatible transcript models.</p>
        </div>
    </div>
</body>
</html>