name: Release
on:
    push:
        tags: [v*]

jobs:
    release:
        runs-on: ubuntu-latest
        permissions:
            id-token: write
        environment: pypi-release

        steps:
            - uses: actions/checkout@v3
            - name: Set up Python 3.10
              uses: actions/setup-python@v4
              with:
                  python-version: "3.10"

            - name: Install dependencies
              run: |
                  python -m pip install --upgrade pip
                  python -m pip install --upgrade build

            - name: Build package
              run: |
                  python -m build --sdist --wheel --outdir dist/

            - name: Publish PyPI package
              uses: pypa/gh-action-pypi-publish@release/v1
              with:
                  skip-existing: true
                  verify-metadata: true
                  verbose: true
