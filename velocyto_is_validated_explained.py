"""
Explanation of is_validated in Velocyto

This file explains the meaning and implementation of the is_validated flag in velocyto,
which is used to distinguish true introns from other genomic regions.
"""

from typing import *
from collections import defaultdict


class Feature:
    """
    A genomic feature (exon or intron) in velocyto.
    """
    def __init__(self, start: int, end: int, kind: int, exin_no: str):
        self.start = start
        self.end = end
        self.kind = kind  # ord("e") for exon, ord("i") for intron
        self.exin_no = int(exin_no)
        self.is_validated = False  # CRITICAL: This flag indicates if the intron is confirmed
        self.transcript_model = None
        
    def end_overlaps_with_part_of(self, segment: Tuple[int, int], minimum_flanking: int = 5) -> bool:
        """
        Check if a read segment overlaps with the end of this feature.
        
        This is one of the key functions for detecting exon-intron spanning reads.
        """
        return (segment[0] + minimum_flanking < self.end) and (segment[-1] - minimum_flanking > self.end)
        
    def start_overlaps_with_part_of(self, segment: Tuple[int, int], minimum_flanking: int = 5) -> bool:
        """
        Check if a read segment overlaps with the start of this feature.
        
        This is one of the key functions for detecting exon-intron spanning reads.
        """
        return (segment[0] + minimum_flanking < self.start) and (segment[-1] - minimum_flanking > self.start)


class SegmentMatch:
    """
    Represents a match between a read segment and a genomic feature.
    """
    def __init__(self, feature, segment, is_spliced):
        self.feature = feature      # The genomic feature (exon/intron)
        self.segment = segment      # The read segment coordinates
        self.is_spliced = is_spliced  # Whether this segment is part of a spliced read
        
    @property
    def maps_to_exon(self) -> bool:
        """Check if this segment maps to an exon."""
        return self.feature.kind == ord("e")
        
    @property
    def maps_to_intron(self) -> bool:
        """Check if this segment maps to an intron."""
        return self.feature.kind == ord("i")


def mark_up_introns_example(reads_to_validate, feature_indexes):
    """
    Example of how intron validation works in velocyto.
    
    The is_validated flag is set to True for introns that have reads spanning
    their exon-intron boundaries. This helps distinguish true introns from
    other genomic regions.
    """
    # Process non-spliced reads to validate introns
    for r in reads_to_validate:
        # Only consider non-spliced reads for intron validation
        # Spliced reads skip introns entirely, so they can't validate them
        if not r.is_spliced:
            # Get the feature index for this chromosome/strand
            ii = feature_indexes[r.chrom + r.strand]
            
            # Find overlapping genomic features
            mappings_record = ii.find_overlapping_ivls(r)
            
            # Check each mapping to see if it validates an intron
            for transcript_model, segments_list in mappings_record.items():
                for segment_match in segments_list:
                    # Only process intron mappings
                    if segment_match.maps_to_intron:
                        # Check if this read spans the exon-intron boundary
                        # This happens when a read overlaps both the intron and its neighboring exon
                        
                        # Check if read overlaps with downstream exon
                        if segment_match.feature.end_overlaps_with_part_of(segment_match.segment):
                            downstream_exon = segment_match.feature.get_downstream_exon()
                            if downstream_exon.start_overlaps_with_part_of(segment_match.segment):
                                # This read spans the intron-exon boundary!
                                # VALIDATE the intron
                                segment_match.feature.is_validated = True
                        
                        # Check if read overlaps with upstream exon
                        if segment_match.feature.start_overlaps_with_part_of(segment_match.segment):
                            upstream_exon = segment_match.feature.get_upstream_exon()
                            if upstream_exon.end_overlaps_with_part_of(segment_match.segment):
                                # This read spans the exon-intron boundary!
                                # VALIDATE the intron
                                segment_match.feature.is_validated = True


class Permissive10X:
    """
    Velocyto's classification logic that uses the is_validated flag.
    """
    
    def count(self, molitem: object, cell_bcidx: int, dict_layers_columns: Dict[str, object], geneid2ix: Dict[str, int]) -> int:
        """
        Count a molecule, using is_validated to distinguish true introns.
        """
        spliced = dict_layers_columns["spliced"]
        unspliced = dict_layers_columns["unspliced"]
        ambiguous = dict_layers_columns["ambiguous"]
        
        if len(molitem.mappings_record) == 0:
            return 2  # No gene mapping
            
        else:
            # Check for multi-gene mapping
            if len(set(i.geneid for i in molitem.mappings_record.keys())) > 1:
                return 1  # Multi-gene mapping → AMBIGUOUS
                
            else:
                # Analyze mapping patterns for single-gene reads
                has_validated_intron = False  # Track if we find validated introns
                has_spanning = False          # Track if we find exon-intron spanning
                has_only_exons = True         # Assume exon-only until proven otherwise
                has_introns = False           # Track if we find any introns
                
                # Analyze each transcript model
                for transcript_model, segments_list in molitem.mappings_record.items():
                    # Analyze each segment
                    for segment_match in segments_list:
                        if segment_match.maps_to_intron:
                            has_introns = True
                            has_only_exons = False
                            
                            # THIS IS WHERE is_validated IS USED!
                            if segment_match.feature.is_validated:
                                # This intron has been confirmed by spanning reads
                                has_validated_intron = True
                                
                                # Check if this specific read spans exon-intron boundaries
                                if (segment_match.feature.end_overlaps_with_part_of(segment_match.segment) or
                                    segment_match.feature.start_overlaps_with_part_of(segment_match.segment)):
                                    has_spanning = True
                                    
                        elif segment_match.maps_to_exon:
                            # This segment maps to an exon
                            pass
                
                # Apply classification rules using is_validated
                
                # Exon-only reads → SPLICED
                if has_only_exons and not has_introns:
                    gene_ix = geneid2ix[transcript_model.geneid]
                    spliced[gene_ix, cell_bcidx] += 1
                    return 0
                    
                # Exon-intron spanning reads → UNSPLICED
                elif has_spanning:
                    gene_ix = geneid2ix[transcript_model.geneid]
                    unspliced[gene_ix, cell_bcidx] += 1
                    return 0
                    
                # Validated introns (confirmed by OTHER reads) → UNSPLICED
                elif has_validated_intron and not has_spanning:
                    gene_ix = geneid2ix[transcript_model.geneid]
                    unspliced[gene_ix, cell_bcidx] += 1
                    return 0
                    
                # Other intron mappings → UNSPLICED (per permissive logic)
                elif has_introns and not has_validated_intron and not has_spanning:
                    gene_ix = geneid2ix[transcript_model.geneid]
                    unspliced[gene_ix, cell_bcidx] += 1
                    return 0
                    
                # Ambiguous cases
                else:
                    gene_ix = geneid2ix[transcript_model.geneid]
                    ambiguous[gene_ix, cell_bcidx] += 1
                    return 0


"""
WHAT DOES is_validated MEAN?

The is_validated flag is a critical quality control mechanism in velocyto:

1. PURPOSE:
   - Distinguishes true introns from other genomic regions that might accidentally align to reads
   - Ensures that only genuine introns are counted in the unspliced category
   - Reduces false positives in RNA velocity analysis

2. HOW IT WORKS:
   - Initially, all introns have is_validated = False
   - During analysis, velocyto looks for reads that span exon-intron boundaries
   - When such reads are found, the corresponding intron's is_validated flag is set to True
   - This validation can come from the same read or from other reads in the dataset

3. VALIDATION CRITERIA:
   - A read spans an exon-intron boundary if it overlaps with BOTH:
     a) An intron feature
     b) A neighboring exon feature
   - This is detected using the overlap functions:
     - end_overlaps_with_part_of()
     - start_overlaps_with_part_of()

4. USAGE IN CLASSIFICATION:
   - Reads mapping to validated introns are confidently classified as unspliced
   - Reads mapping to non-validated introns may be treated differently depending on the logic
   - The Permissive10X logic treats both validated and non-validated introns as unspliced
   - More stringent logics might discard reads mapping to non-validated introns

5. BENEFITS:
   - Improves accuracy of spliced/unspliced quantification
   - Reduces noise from misaligned reads
   - Provides confidence measure for intron annotations
"""


"""
EXAMPLE SCENARIOS:

Scenario 1: Confirmed Intron
Read: ---------********************------------------
      |         |                  |                |
Exon: ---------*                  *------------------
Intron:          ******************
                 |                |
                 Read validates this intron (is_validated = True)

Scenario 2: Unconfirmed Intron
Read: -----------------------******
      |                       |    |
Exon: --------***************    |
Intron:               *********** (is_validated = False)
                      |         |
                      Read does not span boundary

Scenario 3: Spanning Read (Validates Intron)
Read: ----*********************---------------------
      |    |                   |                   |
Exon: ----*                   |                   |
Intron:      *****************|                   |
Exon:                        *---------------------
                             |
                    Read spans both features
                    Intron becomes validated (is_validated = True)
                    This read is classified as UNSPLICED
"""