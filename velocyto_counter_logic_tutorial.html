<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Velocyto Counter Logic Tutorial</title>
    
    <!-- MathJax 3 for LaTeX rendering -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.8rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 1rem 0 0 0;
            font-size: 1.3rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
            border-radius: 0 8px 8px 0;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.8rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        
        .section h3 {
            color: #34495e;
            margin-top: 2rem;
            font-size: 1.4rem;
        }
        
        .code-box {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            font-size: 0.9rem;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .svg-container {
            text-align: center;
            margin: 2rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .info-box {
            background: #d1ecf1;
            border: 2px solid #17a2b8;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .success-box {
            background: #d4edda;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .step-box {
            background: white;
            border: 2px solid #3498db;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            position: relative;
        }
        
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            position: absolute;
            top: -15px;
            left: 20px;
        }
        
        .data-structure {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 1rem;
            margin: 0.5rem 0;
            font-family: monospace;
        }
        
        .flow-diagram {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .flow-step {
            background: #e3f2fd;
            border: 2px solid #1976d2;
            border-radius: 8px;
            padding: 1rem;
            min-width: 200px;
            text-align: center;
            font-weight: bold;
            color: #1976d2;
        }
        
        .flow-arrow {
            font-size: 2rem;
            color: #666;
        }
        
        .batch-visual {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .batch-item {
            background: #fff3e0;
            border: 2px solid #f57c00;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
        }
        
        .memory-efficient {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .performance-tip {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧬 Velocyto Counter Logic</h1>
            <p>Understanding the Core Counting Algorithm in counter.py</p>
        </div>
        
        <div class="content">
            <!-- Overview Section -->
            <div class="section">
                <h2>Overview: The count() Method</h2>
                
                <div class="info-box">
                    <strong>🎯 Purpose:</strong> The <code>count()</code> method is the core engine of velocyto that processes BAM files to count spliced, unspliced, and ambiguous reads for RNA velocity analysis. It implements a memory-efficient batch processing approach to handle large single-cell datasets.
                </div>
                
                <h3>Method Signature</h3>
                <div class="code-box">
def count(self, bamfile: Tuple[str], multimap: bool, 
          cell_batch_size: int=100, molecules_report: bool=False) -> 
          Tuple[Dict[str, List[np.ndarray]], List[str]]:
    """
    Arguments:
    - bamfile: Path to BAM files to process
    - multimap: Whether to include multi-mapping reads
    - cell_batch_size: Number of cells to process in each batch (default 100)
    - molecules_report: Whether to generate detailed molecule reports
    
    Returns:
    - dict_list_arrays: Dictionary with arrays for each layer (spliced/unspliced/ambiguous)
    - cell_bcs_order: List of cell barcodes in processing order
    """
                </div>
                
                <h3>Key Design Principles</h3>
                <div class="batch-visual">
                    <div class="batch-item">
                        <strong>Batch Processing</strong><br>
                        Process cells in batches to manage memory
                    </div>
                    <div class="batch-item">
                        <strong>Feature Indexing</strong><br>
                        Pre-load genomic features for fast lookup
                    </div>
                    <div class="batch-item">
                        <strong>Intron Validation</strong><br>
                        Track validated introns for accurate classification
                    </div>
                    <div class="batch-item">
                        <strong>Memory Efficiency</strong><br>
                        Reset indexes and clear data between batches
                    </div>
                </div>
            </div>

            <!-- Algorithm Flow -->
            <div class="section">
                <h2>Complete Algorithm Flow</h2>

                <div class="svg-container">
                    <svg width="1400" height="1000" viewBox="0 0 1400 1000">
                        <!-- Background -->
                        <rect width="1400" height="1000" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="700" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">Velocyto Counter Algorithm Flow</text>

                        <!-- Phase 1: Initialization -->
                        <rect x="50" y="60" width="300" height="120" fill="#e3f2fd" stroke="#1976d2" stroke-width="3" rx="10"/>
                        <text x="200" y="85" text-anchor="middle" font-size="16" font-weight="bold" fill="#1976d2">Phase 1: Initialization</text>
                        <text x="70" y="110" font-size="11" fill="#666">• Initialize cell_batch (set)</text>
                        <text x="70" y="125" font-size="11" fill="#666">• Initialize reads_to_count (list)</text>
                        <text x="70" y="140" font-size="11" fill="#666">• Build feature_indexes</text>
                        <text x="70" y="155" font-size="11" fill="#666">• Build mask_indexes</text>
                        <text x="70" y="170" font-size="11" fill="#666">• Report intron validation stats</text>

                        <!-- Phase 2: Read Processing Loop -->
                        <rect x="400" y="60" width="300" height="120" fill="#fff3e0" stroke="#f57c00" stroke-width="3" rx="10"/>
                        <text x="550" y="85" text-anchor="middle" font-size="16" font-weight="bold" fill="#f57c00">Phase 2: Read Processing</text>
                        <text x="420" y="110" font-size="11" fill="#666">• Iterate through BAM alignments</text>
                        <text x="420" y="125" font-size="11" fill="#666">• Check batch size conditions</text>
                        <text x="420" y="140" font-size="11" fill="#666">• Accumulate reads by cell barcode</text>
                        <text x="420" y="155" font-size="11" fill="#666">• Trigger batch processing</text>
                        <text x="420" y="170" font-size="11" fill="#666">• Reset for next batch</text>

                        <!-- Phase 3: Batch Counting -->
                        <rect x="750" y="60" width="300" height="120" fill="#e8f5e8" stroke="#4caf50" stroke-width="3" rx="10"/>
                        <text x="900" y="85" text-anchor="middle" font-size="16" font-weight="bold" fill="#4caf50">Phase 3: Batch Counting</text>
                        <text x="770" y="110" font-size="11" fill="#666">• Call count_cell_batch()</text>
                        <text x="770" y="125" font-size="11" fill="#666">• Apply classification logic</text>
                        <text x="770" y="140" font-size="11" fill="#666">• Generate count matrices</text>
                        <text x="770" y="155" font-size="11" fill="#666">• Filter low-count cells</text>
                        <text x="770" y="170" font-size="11" fill="#666">• Accumulate results</text>

                        <!-- Phase 4: Output -->
                        <rect x="1100" y="60" width="250" height="120" fill="#f3e5f5" stroke="#9c27b0" stroke-width="3" rx="10"/>
                        <text x="1225" y="85" text-anchor="middle" font-size="16" font-weight="bold" fill="#9c27b0">Phase 4: Output</text>
                        <text x="1120" y="110" font-size="11" fill="#666">• Concatenate batch results</text>
                        <text x="1120" y="125" font-size="11" fill="#666">• Return count matrices</text>
                        <text x="1120" y="140" font-size="11" fill="#666">• Return cell barcodes</text>
                        <text x="1120" y="155" font-size="11" fill="#666">• Log completion</text>

                        <!-- Detailed Flow -->
                        <text x="700" y="230" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Detailed Processing Flow</text>

                        <!-- Start -->
                        <ellipse cx="700" cy="270" rx="80" ry="25" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
                        <text x="700" y="277" text-anchor="middle" font-size="12" font-weight="bold" fill="#1976d2">START</text>

                        <!-- Initialize -->
                        <rect x="620" y="320" width="160" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="700" y="345" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">Initialize Data Structures</text>

                        <!-- Read Loop -->
                        <rect x="620" y="390" width="160" height="40" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="700" y="415" text-anchor="middle" font-size="11" font-weight="bold" fill="#4caf50">For each read in BAM</text>

                        <!-- Batch Check -->
                        <path d="M 620 460 L 780 460 L 750 500 L 650 500 Z" fill="#fff3cd" stroke="#ffc107" stroke-width="2"/>
                        <text x="700" y="475" text-anchor="middle" font-size="10" font-weight="bold" fill="#856404">Batch full?</text>
                        <text x="700" y="490" text-anchor="middle" font-size="10" font-weight="bold" fill="#856404">OR new cell?</text>

                        <!-- Count Batch -->
                        <rect x="400" y="540" width="160" height="40" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="5"/>
                        <text x="480" y="565" text-anchor="middle" font-size="11" font-weight="bold" fill="#721c24">Count Current Batch</text>

                        <!-- Add Read -->
                        <rect x="840" y="540" width="160" height="40" fill="#d4edda" stroke="#28a745" stroke-width="2" rx="5"/>
                        <text x="920" y="565" text-anchor="middle" font-size="11" font-weight="bold" fill="#155724">Add Read to Batch</text>

                        <!-- Filter Cells -->
                        <rect x="400" y="620" width="160" height="40" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
                        <text x="480" y="645" text-anchor="middle" font-size="11" font-weight="bold" fill="#1976d2">Filter Low-Count Cells</text>

                        <!-- Reset -->
                        <rect x="400" y="700" width="160" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="480" y="725" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">Reset Batch & Indexes</text>

                        <!-- More Reads? -->
                        <path d="M 620 780 L 780 780 L 750 820 L 650 820 Z" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2"/>
                        <text x="700" y="805" text-anchor="middle" font-size="11" font-weight="bold" fill="#9c27b0">More reads?</text>

                        <!-- End -->
                        <ellipse cx="700" cy="870" rx="80" ry="25" fill="#d4edda" stroke="#28a745" stroke-width="2"/>
                        <text x="700" y="877" text-anchor="middle" font-size="12" font-weight="bold" fill="#155724">RETURN RESULTS</text>

                        <!-- Arrows -->
                        <path d="M 700 295 L 700 320" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                        <path d="M 700 360 L 700 390" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                        <path d="M 700 430 L 700 460" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>

                        <path d="M 650 480 L 480 540" stroke="#dc3545" stroke-width="2" marker-end="url(#arrow)"/>
                        <text x="550" y="505" text-anchor="middle" font-size="10" fill="#dc3545">YES</text>

                        <path d="M 750 480 L 920 540" stroke="#28a745" stroke-width="2" marker-end="url(#arrow)"/>
                        <text x="850" y="505" text-anchor="middle" font-size="10" fill="#28a745">NO</text>

                        <path d="M 480 580 L 480 620" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                        <path d="M 480 660 L 480 700" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                        <path d="M 480 740 L 700 780" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>

                        <path d="M 920 580 L 920 410 L 780 410" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>

                        <path d="M 650 800 L 400 410" stroke="#9c27b0" stroke-width="2" marker-end="url(#arrow)"/>
                        <text x="500" y="600" text-anchor="middle" font-size="10" fill="#9c27b0">YES</text>

                        <path d="M 700 820 L 700 870" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                        <text x="720" y="845" font-size="10" fill="#666">NO</text>

                        <!-- Arrow marker definition -->
                        <defs>
                            <marker id="arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>
            </div>

            <!-- Data Structures -->
            <div class="section">
                <h2>Key Data Structures</h2>

                <h3>Core Instance Variables</h3>

                <div class="step-box">
                    <div class="step-number">1</div>
                    <h4>Batch Management</h4>
                    <div class="data-structure">
self.cell_batch: Set[str] = set()
# Tracks unique cell barcodes in current batch
# Used to determine when batch size limit is reached

self.reads_to_count: List[vcy.Read] = []
# Accumulates all reads for current batch
# Cleared after each batch is processed
                    </div>
                </div>

                <div class="step-box">
                    <div class="step-number">2</div>
                    <h4>Feature Indexing</h4>
                    <div class="data-structure">
self.feature_indexes: DefaultDict[str, vcy.FeatureIndex] = defaultdict(vcy.FeatureIndex)
# Key: chromstrand_key (e.g., "chr1_+", "chr2_-")
# Value: FeatureIndex containing sorted genomic features (exons, introns)
# Built from self.annotations_by_chrm_strand

self.mask_indexes: DefaultDict[str, vcy.FeatureIndex] = defaultdict(vcy.FeatureIndex)
# Key: chromstrand_key
# Value: FeatureIndex for masked regions (repeats, etc.)
# Built from self.mask_ivls_by_chromstrand
                    </div>
                </div>

                <div class="step-box">
                    <div class="step-number">3</div>
                    <h4>Output Containers</h4>
                    <div class="data-structure">
cell_bcs_order: List[str] = []
# Ordered list of cell barcodes as they are processed
# Maintains correspondence with count matrix columns

dict_list_arrays: Dict[str, List[np.ndarray]] = {layer_name: [] for layer_name in self.logic.layers}
# Key: layer name ("spliced", "unspliced", "ambiguous")
# Value: List of numpy arrays, one per batch
# Each array is (n_genes, n_cells_in_batch)
                    </div>
                </div>
            </div>

            <!-- Batch Processing Logic -->
            <div class="section">
                <h2>Batch Processing Logic</h2>

                <h3>When to Process a Batch</h3>
                <p>The algorithm processes a batch when either condition is met:</p>

                <div class="code-box">
# Batch processing trigger condition:
if (r is None) or (len(self.cell_batch) == cell_batch_size and r.bc not in self.cell_batch):
    # Process current batch

# Condition breakdown:
# 1. r is None: End of file or file boundary reached
# 2. len(self.cell_batch) == cell_batch_size: Batch size limit reached
# 3. r.bc not in self.cell_batch: New cell would exceed batch size
                </div>

                <div class="warning-box">
                    <strong>⚠️ Important Logic:</strong> The batch is processed when the batch size limit is reached AND a new cell barcode is encountered. This ensures all reads from the same cell stay in the same batch, preventing data fragmentation.
                </div>

                <h3>Batch Processing Steps</h3>

                <div class="flow-diagram">
                    <div class="flow-step">
                        <strong>Step 1:</strong> Call count_cell_batch()
                    </div>
                    <div class="flow-arrow">↓</div>
                    <div class="flow-step">
                        <strong>Step 2:</strong> Apply cell filtering (if enabled)
                    </div>
                    <div class="flow-arrow">↓</div>
                    <div class="flow-step">
                        <strong>Step 3:</strong> Accumulate results
                    </div>
                    <div class="flow-arrow">↓</div>
                    <div class="flow-step">
                        <strong>Step 4:</strong> Reset batch and indexes
                    </div>
                </div>

                <h3>Cell Filtering Logic</h3>
                <div class="code-box">
# Cell filtering (when filter_mode is False)
if not self.filter_mode:
    logging.warning("The barcode selection mode is off, no cell events will be identified by <80 counts")
    tot_mol = dict_layer_columns["spliced"].sum(0) + dict_layer_columns["unspliced"].sum(0)
    cell_bcs_order += list(np.array(list_bcs)[tot_mol > 80])
    for layer_name, layer_columns in dict_layer_columns.items():
        dict_list_arrays[layer_name].append(layer_columns[:, tot_mol > 80])
    logging.warning(f"{np.sum(tot_mol < 80)} of the barcodes where without cell")
else:
    # Normal case - no filtering
    cell_bcs_order += list_bcs
    for layer_name, layer_columns in dict_layer_columns.items():
        dict_list_arrays[layer_name].append(layer_columns)
                </div>

                <div class="info-box">
                    <strong>📊 Cell Filtering:</strong> When <code>filter_mode</code> is disabled, cells with fewer than 80 total molecules (spliced + unspliced) are automatically filtered out to remove empty droplets and low-quality cells.
                </div>
            </div>

            <!-- Memory Management -->
            <div class="section">
                <h2>Memory Management Strategy</h2>

                <h3>Why Batch Processing?</h3>
                <div class="memory-efficient">
                    <strong>🧠 Memory Efficiency Goals:</strong>
                    <ul>
                        <li><strong>Controlled Memory Usage:</strong> Process fixed number of cells at a time</li>
                        <li><strong>Prevent Memory Overflow:</strong> Large datasets (>100k cells) would exceed RAM</li>
                        <li><strong>Enable Streaming:</strong> Process BAM files without loading everything into memory</li>
                        <li><strong>Optimize Cache Usage:</strong> Keep frequently accessed data structures in memory</li>
                    </ul>
                </div>

                <h3>Index Reset Strategy</h3>
                <div class="code-box">
# Reset indexes after each batch to free memory
self.cell_batch = set()
self.reads_to_count = []

# Reset feature indexes to position 0
for chromstrand_key, annotions_ordered_dict in self.annotations_by_chrm_strand.items():
    self.feature_indexes[chromstrand_key].reset()

# Reset mask indexes to position 0
for chromstrand_key, annotions_list in self.mask_ivls_by_chromstrand.items():
    self.mask_indexes[chromstrand_key].reset()
                </div>

                <div class="performance-tip">
                    <strong>⚡ Performance Note:</strong> The comment mentions that "Feature Index swapping is happening often and it is worth to preload everything in memory." This refers to the frequent chromosome/strand changes during BAM iteration, making pre-loaded indexes essential for performance.
                </div>

                <h3>Memory Footprint Analysis</h3>

                <div class="svg-container">
                    <svg width="1000" height="400" viewBox="0 0 1000 400">
                        <!-- Background -->
                        <rect width="1000" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="500" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Memory Usage During Batch Processing</text>

                        <!-- Memory components -->
                        <rect x="50" y="60" width="180" height="80" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
                        <text x="140" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#1976d2">Feature Indexes</text>
                        <text x="140" y="105" text-anchor="middle" font-size="10" fill="#666">Pre-loaded</text>
                        <text x="140" y="120" text-anchor="middle" font-size="10" fill="#666">Persistent</text>

                        <rect x="250" y="60" width="180" height="80" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="340" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">Current Batch</text>
                        <text x="340" y="105" text-anchor="middle" font-size="10" fill="#666">~100 cells</text>
                        <text x="340" y="120" text-anchor="middle" font-size="10" fill="#666">Cleared each batch</text>

                        <rect x="450" y="60" width="180" height="80" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="540" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">Count Matrices</text>
                        <text x="540" y="105" text-anchor="middle" font-size="10" fill="#666">Accumulated</text>
                        <text x="540" y="120" text-anchor="middle" font-size="10" fill="#666">Grows with batches</text>

                        <rect x="650" y="60" width="180" height="80" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="5"/>
                        <text x="740" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#dc3545">Temporary Data</text>
                        <text x="740" y="105" text-anchor="middle" font-size="10" fill="#666">Processing buffers</text>
                        <text x="740" y="120" text-anchor="middle" font-size="10" fill="#666">Freed immediately</text>

                        <!-- Memory timeline -->
                        <text x="500" y="180" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Memory Usage Over Time</text>

                        <!-- Timeline axis -->
                        <line x1="100" y1="350" x2="900" y2="350" stroke="#333" stroke-width="2"/>
                        <text x="500" y="375" text-anchor="middle" font-size="12" fill="#666">Processing Time →</text>

                        <!-- Memory usage curve -->
                        <path d="M 100 320 Q 200 280 300 320 Q 400 280 500 320 Q 600 280 700 320 Q 800 280 900 320"
                              stroke="#e74c3c" stroke-width="3" fill="none"/>

                        <!-- Batch markers -->
                        <line x1="200" y1="200" x2="200" y2="350" stroke="#666" stroke-width="1" stroke-dasharray="5,5"/>
                        <text x="200" y="195" text-anchor="middle" font-size="10" fill="#666">Batch 1</text>

                        <line x1="400" y1="200" x2="400" y2="350" stroke="#666" stroke-width="1" stroke-dasharray="5,5"/>
                        <text x="400" y="195" text-anchor="middle" font-size="10" fill="#666">Batch 2</text>

                        <line x1="600" y1="200" x2="600" y2="350" stroke="#666" stroke-width="1" stroke-dasharray="5,5"/>
                        <text x="600" y="195" text-anchor="middle" font-size="10" fill="#666">Batch 3</text>

                        <line x1="800" y1="200" x2="800" y2="350" stroke="#666" stroke-width="1" stroke-dasharray="5,5"/>
                        <text x="800" y="195" text-anchor="middle" font-size="10" fill="#666">Batch 4</text>

                        <!-- Memory level indicators -->
                        <text x="80" y="325" text-anchor="end" font-size="10" fill="#666">Low</text>
                        <text x="80" y="285" text-anchor="end" font-size="10" fill="#666">High</text>
                        <text x="80" y="245" text-anchor="end" font-size="10" fill="#666">Peak</text>

                        <!-- Legend -->
                        <circle cx="750" cy="250" r="3" fill="#e74c3c"/>
                        <text x="760" y="255" font-size="10" fill="#666">Memory Usage</text>
                    </svg>
                </div>
            </div>

            <!-- Intron Validation Reporting -->
            <div class="section">
                <h2>Intron Validation Reporting</h2>

                <h3>Validation Statistics</h3>
                <p>Before processing reads, the algorithm reports intron validation statistics:</p>

                <div class="code-box">
# Intron validation reporting logic
logging.debug(f"Summarizing the results of intron validation.")
n_is_intron = 0
n_is_intron_valid = 0
unique_valid = set()

for chromstrand_key, feature_index in self.feature_indexes.items():
    for ivl in feature_index.ivls:
        if ivl.kind == ord("i"):  # 'i' = intron
            n_is_intron += 1
        if ivl.is_validated:
            n_is_intron_valid += 1
            unique_valid.add((ivl.start, ivl.end))

logging.debug(f"Validated {n_is_intron_valid} introns (of which unique intervals {len(unique_valid)}) out of {n_is_intron} total possible introns (considering each possible transcript models).")
                </div>

                <div class="info-box">
                    <strong>🔍 Validation Metrics:</strong>
                    <ul>
                        <li><strong>n_is_intron:</strong> Total number of intron features across all transcript models</li>
                        <li><strong>n_is_intron_valid:</strong> Number of introns validated by spanning reads</li>
                        <li><strong>unique_valid:</strong> Number of unique genomic intervals that are validated</li>
                    </ul>
                </div>

                <h3>Why Validation Matters</h3>
                <div class="warning-box">
                    <strong>⚠️ Critical for Accuracy:</strong> Intron validation prevents false positives from:
                    <ul>
                        <li><strong>Genomic DNA contamination:</strong> Reads from genomic DNA would map to introns</li>
                        <li><strong>Annotation errors:</strong> Incorrectly annotated introns</li>
                        <li><strong>Pseudogenes:</strong> Processed pseudogenes lacking introns</li>
                        <li><strong>Technical artifacts:</strong> Mapping errors or chimeric reads</li>
                    </ul>
                </div>
            </div>

            <!-- Performance Considerations -->
            <div class="section">
                <h2>Performance Considerations</h2>

                <h3>Computational Complexity</h3>

                <div class="svg-container">
                    <svg width="1000" height="300" viewBox="0 0 1000 300">
                        <!-- Background -->
                        <rect width="1000" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="500" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Performance Characteristics</text>

                        <!-- Time complexity -->
                        <rect x="50" y="60" width="200" height="100" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
                        <text x="150" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="#1976d2">Time Complexity</text>
                        <text x="70" y="110" font-size="11" fill="#666">• O(n) in number of reads</text>
                        <text x="70" y="125" font-size="11" fill="#666">• O(log m) feature lookup</text>
                        <text x="70" y="140" font-size="11" fill="#666">• O(b) batch processing</text>

                        <!-- Space complexity -->
                        <rect x="270" y="60" width="200" height="100" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="370" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="#f57c00">Space Complexity</text>
                        <text x="290" y="110" font-size="11" fill="#666">• O(g) for feature indexes</text>
                        <text x="290" y="125" font-size="11" fill="#666">• O(c) for current batch</text>
                        <text x="290" y="140" font-size="11" fill="#666">• O(b×g) for results</text>

                        <!-- Bottlenecks -->
                        <rect x="490" y="60" width="200" height="100" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="5"/>
                        <text x="590" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="#dc3545">Bottlenecks</text>
                        <text x="510" y="110" font-size="11" fill="#666">• BAM file I/O</text>
                        <text x="510" y="125" font-size="11" fill="#666">• Feature intersection</text>
                        <text x="510" y="140" font-size="11" fill="#666">• Memory allocation</text>

                        <!-- Optimizations -->
                        <rect x="710" y="60" width="200" height="100" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="810" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="#4caf50">Optimizations</text>
                        <text x="730" y="110" font-size="11" fill="#666">• Pre-loaded indexes</text>
                        <text x="730" y="125" font-size="11" fill="#666">• Batch processing</text>
                        <text x="730" y="140" font-size="11" fill="#666">• Index resets</text>

                        <!-- Legend -->
                        <text x="50" y="200" font-size="12" fill="#666">Where:</text>
                        <text x="50" y="220" font-size="11" fill="#666">n = number of reads, m = number of features, g = number of genes</text>
                        <text x="50" y="235" font-size="11" fill="#666">c = cells per batch, b = number of batches</text>
                    </svg>
                </div>

                <h3>Optimization Strategies</h3>

                <div class="performance-tip">
                    <strong>⚡ Key Optimizations:</strong>
                    <ul>
                        <li><strong>Pre-loaded Feature Indexes:</strong> All genomic features loaded into memory for O(log m) lookup</li>
                        <li><strong>Batch Processing:</strong> Controlled memory usage prevents swapping</li>
                        <li><strong>Index Resets:</strong> Reuse data structures instead of reallocating</li>
                        <li><strong>Sparse Arrays:</strong> Comment suggests using scipy.sparse for further memory reduction</li>
                    </ul>
                </div>

                <h3>Scalability Analysis</h3>
                <div class="code-box">
# Typical performance characteristics:
#
# Dataset size: 10,000 cells, 30,000 genes, 100M reads
# Memory usage: ~8-16 GB RAM
# Processing time: 2-6 hours (depending on hardware)
#
# Batch size impact:
# - Smaller batches: Lower memory, more I/O overhead
# - Larger batches: Higher memory, better cache efficiency
# - Default 100 cells: Good balance for most datasets
                </div>

                <div class="success-box">
                    <strong>✅ Production Recommendations:</strong>
                    <ul>
                        <li><strong>Batch Size:</strong> 50-200 cells depending on available RAM</li>
                        <li><strong>Memory:</strong> 16-32 GB RAM for large datasets (>50k cells)</li>
                        <li><strong>Storage:</strong> Fast SSD for BAM files reduces I/O bottleneck</li>
                        <li><strong>Filtering:</strong> Enable filter_mode for production to remove empty droplets</li>
                    </ul>
                </div>
            </div>

            <!-- Summary -->
            <div class="section">
                <h2>Summary</h2>

                <div class="highlight" style="display: block; text-align: center; padding: 1rem; margin: 2rem 0; font-size: 1.1rem;">
                    🎯 <strong>Key Takeaway:</strong> The velocyto counter implements a sophisticated batch processing algorithm that balances memory efficiency with computational performance, enabling RNA velocity analysis on large single-cell datasets while maintaining accuracy through careful intron validation and feature indexing.
                </div>

                <h3>Algorithm Strengths</h3>
                <div class="success-box">
                    <strong>✅ Design Excellence:</strong>
                    <ul>
                        <li><strong>Memory Efficient:</strong> Batch processing prevents memory overflow on large datasets</li>
                        <li><strong>Scalable:</strong> Linear time complexity with optimized feature lookups</li>
                        <li><strong>Robust:</strong> Intron validation prevents false positives</li>
                        <li><strong>Flexible:</strong> Configurable batch sizes and filtering options</li>
                        <li><strong>Production Ready:</strong> Comprehensive logging and error handling</li>
                    </ul>
                </div>

                <h3>Understanding the Code</h3>
                <div class="info-box">
                    <strong>📚 Learning Points:</strong>
                    <ul>
                        <li><strong>Batch Processing Pattern:</strong> Essential for handling large datasets in limited memory</li>
                        <li><strong>Index Management:</strong> Pre-loading and resetting indexes for performance</li>
                        <li><strong>Memory vs Performance Trade-offs:</strong> Balancing batch size with efficiency</li>
                        <li><strong>Data Structure Design:</strong> Using appropriate containers for different data types</li>
                        <li><strong>Validation Importance:</strong> Quality control through intron validation</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
