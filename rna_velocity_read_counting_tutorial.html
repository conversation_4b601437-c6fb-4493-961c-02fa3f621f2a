<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RNA Velocity Read Counting: Spliced, Unspliced, and Ambiguous</title>
    
    <!-- MathJax 3 for LaTeX rendering -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.8rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 1rem 0 0 0;
            font-size: 1.3rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
            border-radius: 0 8px 8px 0;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.8rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        
        .section h3 {
            color: #34495e;
            margin-top: 2rem;
            font-size: 1.4rem;
        }
        
        .math-box {
            background: #fff;
            border: 2px solid #3498db;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            box-shadow: 0 2px 10px rgba(52, 152, 219, 0.1);
        }
        
        .code-box {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
        }
        
        .workflow-step {
            display: flex;
            align-items: flex-start;
            margin: 1.5rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .svg-container {
            text-align: center;
            margin: 2rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .info-box {
            background: #d1ecf1;
            border: 2px solid #17a2b8;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .success-box {
            background: #d4edda;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .read-type {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            margin: 0.2rem;
            display: inline-block;
        }
        
        .spliced {
            background: #d4edda;
            color: #155724;
            border: 2px solid #28a745;
        }
        
        .unspliced {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffc107;
        }
        
        .ambiguous {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #dc3545;
        }
        
        .toc {
            background: #34495e;
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .toc h3 {
            margin-top: 0;
            color: #ecf0f1;
        }
        
        .toc ul {
            list-style: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 0.5rem 0;
            padding-left: 1rem;
        }
        
        .toc a {
            color: #3498db;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .toc a:hover {
            color: #5dade2;
        }
        
        .decision-tree {
            background: white;
            border: 2px solid #3498db;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .gene-model {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 0.5rem;
            margin: 0.5rem 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧬 RNA Velocity Read Counting</h1>
            <p>Understanding Spliced, Unspliced, and Ambiguous Read Classification</p>
        </div>
        
        <div class="content">
            <!-- Table of Contents -->
            <div class="toc">
                <h3>📚 Table of Contents</h3>
                <ul>
                    <li><a href="#introduction">1. Introduction and Overview</a></li>
                    <li><a href="#biological-foundation">2. Biological Foundation</a></li>
                    <li><a href="#technical-implementation">3. Technical Implementation</a></li>
                    <li><a href="#classification-logic">4. Classification Logic</a></li>
                    <li><a href="#decision-tree">5. Decision Tree Algorithm</a></li>
                    <li><a href="#practical-examples">6. Practical Examples</a></li>
                    <li><a href="#edge-cases">7. Edge Cases and Ambiguous Situations</a></li>
                    <li><a href="#code-implementation">8. Code Implementation</a></li>
                </ul>
            </div>

            <!-- Section 1: Introduction -->
            <div class="section" id="introduction">
                <h2>1. Introduction and Overview</h2>
                
                <p>RNA velocity analysis requires precise classification of sequencing reads into three fundamental categories:</p>
                
                <div class="workflow-step">
                    <div class="read-type spliced">SPLICED</div>
                    <div>
                        <strong>Mature mRNA reads:</strong> Reads mapping to exonic sequences, representing processed transcripts with introns removed
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="read-type unspliced">UNSPLICED</div>
                    <div>
                        <strong>Pre-mRNA reads:</strong> Reads mapping to intronic sequences or spanning exon-intron boundaries, representing nascent transcripts
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="read-type ambiguous">AMBIGUOUS</div>
                    <div>
                        <strong>Unclear classification:</strong> Reads that cannot be confidently assigned to either category due to mapping ambiguity or technical limitations
                    </div>
                </div>
                
                <h3>1.1 Why This Classification Matters</h3>
                <p>The ratio of unspliced to spliced reads provides the foundation for RNA velocity calculations:</p>
                
                <div class="math-box">
                    <p><strong>RNA Velocity Principle:</strong></p>
                    $$\text{RNA Velocity} = \frac{du}{dt} = \alpha - \beta u$$
                    $$\frac{ds}{dt} = \beta u - \gamma s$$
                    
                    <p>Where:</p>
                    <ul>
                        <li>$u$ = unspliced mRNA abundance</li>
                        <li>$s$ = spliced mRNA abundance</li>
                        <li>$\alpha$ = transcription rate</li>
                        <li>$\beta$ = splicing rate</li>
                        <li>$\gamma$ = degradation rate</li>
                    </ul>
                </div>
                
                <div class="info-box">
                    <strong>🎯 Key Insight:</strong> Accurate read classification is crucial because the unspliced/spliced ratio directly determines velocity estimates. Misclassified reads lead to incorrect velocity vectors and erroneous trajectory inference.
                </div>
            </div>

            <!-- Section 2: Biological Foundation -->
            <div class="section" id="biological-foundation">
                <h2>2. Biological Foundation</h2>

                <h3>2.1 From Gene to Mature mRNA</h3>
                <p>Understanding read classification requires knowledge of the splicing process:</p>

                <div class="svg-container">
                    <svg width="1200" height="500" viewBox="0 0 1200 500">
                        <!-- Background -->
                        <rect width="1200" height="500" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="600" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">RNA Processing: From Pre-mRNA to Mature mRNA</text>

                        <!-- Gene Structure -->
                        <text x="50" y="70" font-size="14" font-weight="bold" fill="#2c3e50">Gene Structure (DNA)</text>

                        <!-- Exons and Introns -->
                        <rect x="100" y="80" width="80" height="30" fill="#28a745" stroke="#155724" stroke-width="2" rx="5"/>
                        <text x="140" y="100" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Exon 1</text>

                        <rect x="200" y="80" width="120" height="30" fill="#6c757d" stroke="#495057" stroke-width="2" rx="5"/>
                        <text x="260" y="100" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Intron 1</text>

                        <rect x="340" y="80" width="80" height="30" fill="#28a745" stroke="#155724" stroke-width="2" rx="5"/>
                        <text x="380" y="100" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Exon 2</text>

                        <rect x="440" y="80" width="120" height="30" fill="#6c757d" stroke="#495057" stroke-width="2" rx="5"/>
                        <text x="500" y="100" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Intron 2</text>

                        <rect x="580" y="80" width="80" height="30" fill="#28a745" stroke="#155724" stroke-width="2" rx="5"/>
                        <text x="620" y="100" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Exon 3</text>

                        <!-- Transcription Arrow -->
                        <path d="M 100 140 L 660 140" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead)"/>
                        <text x="380" y="135" text-anchor="middle" font-size="12" fill="#e74c3c" font-weight="bold">Transcription</text>

                        <!-- Pre-mRNA -->
                        <text x="50" y="180" font-size="14" font-weight="bold" fill="#2c3e50">Pre-mRNA (Unspliced)</text>

                        <rect x="100" y="190" width="80" height="25" fill="#28a745" stroke="#155724" stroke-width="1" rx="3"/>
                        <text x="140" y="207" text-anchor="middle" font-size="10" fill="white">Exon 1</text>

                        <rect x="180" y="190" width="120" height="25" fill="#ffc107" stroke="#856404" stroke-width="1" rx="3"/>
                        <text x="240" y="207" text-anchor="middle" font-size="10" fill="#212529">Intron 1</text>

                        <rect x="300" y="190" width="80" height="25" fill="#28a745" stroke="#155724" stroke-width="1" rx="3"/>
                        <text x="340" y="207" text-anchor="middle" font-size="10" fill="white">Exon 2</text>

                        <rect x="380" y="190" width="120" height="25" fill="#ffc107" stroke="#856404" stroke-width="1" rx="3"/>
                        <text x="440" y="207" text-anchor="middle" font-size="10" fill="#212529">Intron 2</text>

                        <rect x="500" y="190" width="80" height="25" fill="#28a745" stroke="#155724" stroke-width="1" rx="3"/>
                        <text x="540" y="207" text-anchor="middle" font-size="10" fill="white">Exon 3</text>

                        <!-- Splicing Process -->
                        <text x="700" y="180" font-size="12" fill="#9c27b0" font-weight="bold">Splicing Process:</text>
                        <text x="700" y="200" font-size="10" fill="#666">• Spliceosome recognition</text>
                        <text x="700" y="215" font-size="10" fill="#666">• Intron removal</text>
                        <text x="700" y="230" font-size="10" fill="#666">• Exon ligation</text>

                        <!-- Splicing Arrow -->
                        <path d="M 300 240 L 300 280" stroke="#9c27b0" stroke-width="3" marker-end="url(#arrowhead2)"/>
                        <text x="320" y="265" font-size="12" fill="#9c27b0" font-weight="bold">Splicing</text>

                        <!-- Mature mRNA -->
                        <text x="50" y="320" font-size="14" font-weight="bold" fill="#2c3e50">Mature mRNA (Spliced)</text>

                        <rect x="100" y="330" width="80" height="25" fill="#28a745" stroke="#155724" stroke-width="1" rx="3"/>
                        <text x="140" y="347" text-anchor="middle" font-size="10" fill="white">Exon 1</text>

                        <rect x="180" y="330" width="80" height="25" fill="#28a745" stroke="#155724" stroke-width="1" rx="3"/>
                        <text x="220" y="347" text-anchor="middle" font-size="10" fill="white">Exon 2</text>

                        <rect x="260" y="330" width="80" height="25" fill="#28a745" stroke="#155724" stroke-width="1" rx="3"/>
                        <text x="300" y="347" text-anchor="middle" font-size="10" fill="white">Exon 3</text>

                        <!-- Read Mapping Examples -->
                        <text x="50" y="400" font-size="14" font-weight="bold" fill="#2c3e50">Sequencing Reads</text>

                        <!-- Spliced Read -->
                        <rect x="100" y="410" width="60" height="15" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                        <text x="130" y="422" text-anchor="middle" font-size="8" fill="#155724">Spliced Read</text>
                        <text x="170" y="422" font-size="10" fill="#28a745">→ Maps to exons only</text>

                        <!-- Unspliced Read -->
                        <rect x="100" y="435" width="60" height="15" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                        <text x="130" y="447" text-anchor="middle" font-size="8" fill="#856404">Unspliced Read</text>
                        <text x="170" y="447" font-size="10" fill="#856404">→ Maps to introns or spans exon-intron boundaries</text>

                        <!-- Spanning Read -->
                        <rect x="400" y="410" width="30" height="15" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                        <rect x="430" y="410" width="40" height="15" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                        <rect x="470" y="410" width="30" height="15" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                        <text x="450" y="435" text-anchor="middle" font-size="10" fill="#666">Exon-Intron Spanning Read</text>

                        <!-- Arrow markers -->
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
                            </marker>
                            <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#9c27b0"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>2.2 Key Biological Concepts</h3>

                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>Exon-Intron Boundaries:</strong> Critical junctions where splicing occurs. Reads spanning these boundaries indicate active splicing and are classified as unspliced.
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>Intron Validation:</strong> Not all intronic reads represent true pre-mRNA. Velocyto validates introns by requiring evidence of exon-intron spanning reads.
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div>
                        <strong>Temporal Dynamics:</strong> Newly transcribed genes show high unspliced/spliced ratios, while genes being downregulated show low ratios.
                    </div>
                </div>

                <div class="math-box">
                    <p><strong>Splicing Kinetics:</strong></p>
                    <p>The relationship between unspliced and spliced mRNA follows predictable kinetics:</p>
                    $$\text{Unspliced} \xrightarrow{\beta} \text{Spliced} \xrightarrow{\gamma} \text{Degraded}$$

                    <p>This creates characteristic patterns in the unspliced vs spliced phase space that velocyto exploits for velocity estimation.</p>
                </div>
            </div>

            <!-- Section 3: Technical Implementation -->
            <div class="section" id="technical-implementation">
                <h2>3. Technical Implementation</h2>

                <h3>3.1 Gene Model Annotation</h3>
                <p>Velocyto requires detailed gene annotations to classify reads accurately:</p>

                <div class="gene-model">
                    <strong>Example Gene Model (GTF format):</strong><br>
                    chr1 &nbsp;&nbsp; gene &nbsp;&nbsp;&nbsp;&nbsp; 1000 &nbsp; 5000 &nbsp; . &nbsp; + &nbsp; . &nbsp; gene_id "GENE001"<br>
                    chr1 &nbsp;&nbsp; exon &nbsp;&nbsp;&nbsp;&nbsp; 1000 &nbsp; 1200 &nbsp; . &nbsp; + &nbsp; . &nbsp; gene_id "GENE001"; exon_number "1"<br>
                    chr1 &nbsp;&nbsp; intron &nbsp;&nbsp; 1201 &nbsp; 2999 &nbsp; . &nbsp; + &nbsp; . &nbsp; gene_id "GENE001"; intron_number "1"<br>
                    chr1 &nbsp;&nbsp; exon &nbsp;&nbsp;&nbsp;&nbsp; 3000 &nbsp; 3500 &nbsp; . &nbsp; + &nbsp; . &nbsp; gene_id "GENE001"; exon_number "2"<br>
                    chr1 &nbsp;&nbsp; intron &nbsp;&nbsp; 3501 &nbsp; 4499 &nbsp; . &nbsp; + &nbsp; . &nbsp; gene_id "GENE001"; intron_number "2"<br>
                    chr1 &nbsp;&nbsp; exon &nbsp;&nbsp;&nbsp;&nbsp; 4500 &nbsp; 5000 &nbsp; . &nbsp; + &nbsp; . &nbsp; gene_id "GENE001"; exon_number "3"
                </div>

                <h3>3.2 Read Mapping and Feature Assignment</h3>
                <p>Each sequencing read is mapped to genomic features through a multi-step process:</p>

                <div class="svg-container">
                    <svg width="1000" height="600" viewBox="0 0 1000 600">
                        <!-- Background -->
                        <rect width="1000" height="600" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="500" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Read Classification Pipeline</text>

                        <!-- Step 1: Read Mapping -->
                        <rect x="50" y="60" width="180" height="80" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
                        <text x="140" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#1976d2">1. Read Mapping</text>
                        <text x="70" y="105" font-size="10" fill="#666">• Align to genome</text>
                        <text x="70" y="120" font-size="10" fill="#666">• Extract cell barcode</text>
                        <text x="70" y="135" font-size="10" fill="#666">• Extract UMI</text>

                        <!-- Arrow 1 -->
                        <path d="M 240 100 L 280 100" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>

                        <!-- Step 2: Feature Overlap -->
                        <rect x="290" y="60" width="180" height="80" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="380" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">2. Feature Overlap</text>
                        <text x="310" y="105" font-size="10" fill="#666">• Check exon overlap</text>
                        <text x="310" y="120" font-size="10" fill="#666">• Check intron overlap</text>
                        <text x="310" y="135" font-size="10" fill="#666">• Identify gene(s)</text>

                        <!-- Arrow 2 -->
                        <path d="M 480 100 L 520 100" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>

                        <!-- Step 3: Validation -->
                        <rect x="530" y="60" width="180" height="80" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="620" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">3. Intron Validation</text>
                        <text x="550" y="105" font-size="10" fill="#666">• Check spanning reads</text>
                        <text x="550" y="120" font-size="10" fill="#666">• Validate introns</text>
                        <text x="550" y="135" font-size="10" fill="#666">• Mark features</text>

                        <!-- Arrow 3 -->
                        <path d="M 620 150 L 620 190" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>

                        <!-- Decision Logic -->
                        <rect x="50" y="200" width="900" height="350" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="500" y="225" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Classification Decision Logic</text>

                        <!-- Classification Tree -->
                        <rect x="100" y="250" width="200" height="60" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="5"/>
                        <text x="200" y="275" text-anchor="middle" font-size="12" font-weight="bold" fill="#721c24">Multi-gene mapping?</text>
                        <text x="200" y="295" text-anchor="middle" font-size="10" fill="#721c24">→ AMBIGUOUS</text>

                        <rect x="350" y="250" width="200" height="60" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="5"/>
                        <text x="450" y="275" text-anchor="middle" font-size="12" font-weight="bold" fill="#721c24">No gene mapping?</text>
                        <text x="450" y="295" text-anchor="middle" font-size="10" fill="#721c24">→ AMBIGUOUS</text>

                        <rect x="600" y="250" width="200" height="60" fill="#d4edda" stroke="#28a745" stroke-width="2" rx="5"/>
                        <text x="700" y="275" text-anchor="middle" font-size="12" font-weight="bold" fill="#155724">Single gene mapping</text>
                        <text x="700" y="295" text-anchor="middle" font-size="10" fill="#155724">→ Proceed to classification</text>

                        <!-- Classification branches -->
                        <rect x="100" y="350" width="150" height="50" fill="#d4edda" stroke="#28a745" stroke-width="1" rx="3"/>
                        <text x="175" y="370" text-anchor="middle" font-size="10" font-weight="bold" fill="#155724">Exon only</text>
                        <text x="175" y="385" text-anchor="middle" font-size="9" fill="#155724">→ SPLICED</text>

                        <rect x="270" y="350" width="150" height="50" fill="#fff3cd" stroke="#ffc107" stroke-width="1" rx="3"/>
                        <text x="345" y="370" text-anchor="middle" font-size="10" font-weight="bold" fill="#856404">Validated intron</text>
                        <text x="345" y="385" text-anchor="middle" font-size="9" fill="#856404">→ UNSPLICED</text>

                        <rect x="440" y="350" width="150" height="50" fill="#fff3cd" stroke="#ffc107" stroke-width="1" rx="3"/>
                        <text x="515" y="370" text-anchor="middle" font-size="10" font-weight="bold" fill="#856404">Exon-intron span</text>
                        <text x="515" y="385" text-anchor="middle" font-size="9" fill="#856404">→ UNSPLICED</text>

                        <rect x="610" y="350" width="150" height="50" fill="#f8d7da" stroke="#dc3545" stroke-width="1" rx="3"/>
                        <text x="685" y="370" text-anchor="middle" font-size="10" font-weight="bold" fill="#721c24">Mixed/Complex</text>
                        <text x="685" y="385" text-anchor="middle" font-size="9" fill="#721c24">→ Context dependent</text>

                        <!-- Feature Types -->
                        <text x="100" y="440" font-size="12" font-weight="bold" fill="#333">Feature Types in Velocyto:</text>

                        <rect x="100" y="450" width="15" height="15" fill="#28a745"/>
                        <text x="125" y="462" font-size="10" fill="#666">Exon (kind = 101, 'e')</text>

                        <rect x="250" y="450" width="15" height="15" fill="#ffc107"/>
                        <text x="275" y="462" font-size="10" fill="#666">Intron (kind = 105, 'i')</text>

                        <rect x="400" y="450" width="15" height="15" fill="#6c757d"/>
                        <text x="425" y="462" font-size="10" fill="#666">Masked repeat (kind = 109, 'm')</text>

                        <text x="100" y="490" font-size="12" font-weight="bold" fill="#333">Validation Process:</text>
                        <text x="100" y="510" font-size="10" fill="#666">• Introns are marked as "validated" when reads span exon-intron boundaries</text>
                        <text x="100" y="525" font-size="10" fill="#666">• Only validated introns are confidently classified as containing unspliced reads</text>
                        <text x="100" y="540" font-size="10" fill="#666">• This reduces false positives from genomic DNA contamination</text>

                        <!-- Arrow marker -->
                        <defs>
                            <marker id="arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>3.3 Key Data Structures</h3>
                <p>Velocyto uses several key data structures for read classification:</p>

                <div class="code-box">
# Core data structures in velocyto

class Feature:
    """Represents an exon, intron, or other genomic feature"""
    start: int          # Start position
    end: int           # End position
    kind: int          # Feature type (101='e', 105='i', 109='m')
    exin_no: int       # Exon/intron number
    is_validated: bool # Whether intron is validated by spanning reads

class Read:
    """Container for sequencing read information"""
    bc: str           # Cell barcode
    umi: str          # UMI sequence
    chrom: str        # Chromosome
    strand: str       # Strand (+ or -)
    segments: List    # List of aligned segments
    is_spliced: bool  # Whether read has splice junctions

class SegmentMatch:
    """Links read segments to genomic features"""
    segment: Tuple[int, int]  # Genomic coordinates
    feature: Feature          # Associated feature
    is_spliced: bool         # Whether segment spans splice junction
                </div>
            </div>

            <!-- Section 4: Classification Logic -->
            <div class="section" id="classification-logic">
                <h2>4. Classification Logic</h2>

                <h3>4.1 The Classification Algorithm</h3>
                <p>Velocyto implements a sophisticated decision tree to classify each read. The algorithm considers multiple factors:</p>

                <div class="decision-tree">
                    <h4>Primary Classification Factors:</h4>
                    <ul>
                        <li><strong>Gene Mapping:</strong> Does the read map to one gene, multiple genes, or no genes?</li>
                        <li><strong>Feature Types:</strong> Does the read overlap exons, introns, or both?</li>
                        <li><strong>Intron Validation:</strong> Are the overlapping introns validated by spanning reads?</li>
                        <li><strong>Spanning Behavior:</strong> Does the read span exon-intron boundaries?</li>
                        <li><strong>Splice Junctions:</strong> Does the read contain splice junctions from the aligner?</li>
                    </ul>
                </div>

                <h3>4.2 Detailed Classification Rules</h3>
                <p>Based on the velocyto source code, here are the specific classification rules:</p>

                <div class="workflow-step">
                    <div class="read-type ambiguous">AMBIGUOUS</div>
                    <div>
                        <strong>Multi-gene mapping:</strong> Read maps to multiple genes → Cannot determine origin
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="read-type ambiguous">AMBIGUOUS</div>
                    <div>
                        <strong>No gene mapping:</strong> Read doesn't map to any annotated gene → Intergenic
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="read-type spliced">SPLICED</div>
                    <div>
                        <strong>Exon-only mapping:</strong> Read maps only to exonic sequences → Mature mRNA
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="read-type unspliced">UNSPLICED</div>
                    <div>
                        <strong>Exon-intron spanning:</strong> Read spans exon-intron boundaries → Active splicing
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="read-type unspliced">UNSPLICED</div>
                    <div>
                        <strong>Validated intron:</strong> Read maps to intron validated by spanning reads → Pre-mRNA
                    </div>
                </div>

                <h3>4.3 Boolean Logic Variables</h3>
                <p>The velocyto algorithm uses several boolean variables to track read properties:</p>

                <div class="code-box">
# Boolean variables used in classification logic

has_onlyexo_model = 0           # Read maps only to exons
has_onlyintron_model = 0        # Read maps only to introns
has_mixed_model = 0             # Read maps to both exons and introns
has_validated_intron = 0        # Read maps to validated intron
has_exin_intron_span = 0        # Read spans exon-intron boundary
has_only_span_exin_model = 0    # All models span exon-intron boundaries
multi_gene = 0                  # Read maps to multiple genes

# Classification logic (simplified)
if multi_gene:
    return "AMBIGUOUS"  # Multi-gene mapping
elif not len(molitem.mappings_record):
    return "AMBIGUOUS"  # No gene mapping
elif has_onlyexo_model and not has_onlyintron_model and not has_mixed_model:
    return "SPLICED"    # Exon-only mapping
elif has_only_span_exin_model:
    return "UNSPLICED"  # Exon-intron spanning
elif has_onlyintron_and_valid_model:
    return "UNSPLICED"  # Validated intron mapping
# ... additional complex cases
                </div>

                <h3>4.4 Edge Cases and Complex Scenarios</h3>

                <div class="warning-box">
                    <strong>⚠️ Complex Classification Scenarios:</strong>
                    <ul>
                        <li><strong>Mixed Models:</strong> Reads mapping to both exons and introns require careful evaluation</li>
                        <li><strong>Non-validated Introns:</strong> Intronic reads without spanning evidence may be genomic contamination</li>
                        <li><strong>Alternative Splicing:</strong> Multiple transcript isoforms can complicate classification</li>
                        <li><strong>Overlapping Genes:</strong> Antisense or overlapping genes create ambiguous mappings</li>
                    </ul>
                </div>

                <div class="math-box">
                    <p><strong>Classification Confidence:</strong></p>
                    <p>Not all classifications are equally confident. The algorithm prioritizes:</p>
                    <ol>
                        <li><strong>High Confidence:</strong> Exon-only reads (spliced) and validated intron reads (unspliced)</li>
                        <li><strong>Medium Confidence:</strong> Exon-intron spanning reads (unspliced)</li>
                        <li><strong>Low Confidence:</strong> Non-validated intronic reads and mixed mappings</li>
                    </ol>

                    <p>This hierarchy ensures that velocity estimates are based on the most reliable evidence.</p>
                </div>
            </div>

            <!-- Section 5: Decision Tree Algorithm -->
            <div class="section" id="decision-tree">
                <h2>5. Decision Tree Algorithm</h2>

                <h3>5.1 Complete Decision Flow</h3>
                <p>Here's the complete decision tree used by velocyto for read classification:</p>

                <div class="svg-container">
                    <svg width="1200" height="800" viewBox="0 0 1200 800">
                        <!-- Background -->
                        <rect width="1200" height="800" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="600" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Velocyto Read Classification Decision Tree</text>

                        <!-- Start Node -->
                        <ellipse cx="600" cy="80" rx="80" ry="30" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
                        <text x="600" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#1976d2">Input Read</text>

                        <!-- First Decision: Multi-gene -->
                        <rect x="520" y="130" width="160" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="600" y="155" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">Multi-gene mapping?</text>

                        <!-- Multi-gene YES -->
                        <rect x="100" y="200" width="120" height="40" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="5"/>
                        <text x="160" y="225" text-anchor="middle" font-size="11" font-weight="bold" fill="#721c24">AMBIGUOUS</text>

                        <!-- Multi-gene NO -->
                        <rect x="520" y="200" width="160" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="600" y="225" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">No gene mapping?</text>

                        <!-- No gene YES -->
                        <rect x="300" y="270" width="120" height="40" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="5"/>
                        <text x="360" y="295" text-anchor="middle" font-size="11" font-weight="bold" fill="#721c24">AMBIGUOUS</text>

                        <!-- No gene NO - Continue classification -->
                        <rect x="520" y="270" width="160" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="600" y="295" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">Exon-only mapping?</text>

                        <!-- Exon-only YES -->
                        <rect x="750" y="340" width="120" height="40" fill="#d4edda" stroke="#28a745" stroke-width="2" rx="5"/>
                        <text x="810" y="365" text-anchor="middle" font-size="11" font-weight="bold" fill="#155724">SPLICED</text>

                        <!-- Exon-only NO -->
                        <rect x="520" y="340" width="160" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="600" y="365" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">Exon-intron span?</text>

                        <!-- Span YES -->
                        <rect x="750" y="410" width="120" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5"/>
                        <text x="810" y="435" text-anchor="middle" font-size="11" font-weight="bold" fill="#856404">UNSPLICED</text>

                        <!-- Span NO -->
                        <rect x="520" y="410" width="160" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="600" y="435" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">Validated intron?</text>

                        <!-- Validated YES -->
                        <rect x="750" y="480" width="120" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5"/>
                        <text x="810" y="505" text-anchor="middle" font-size="11" font-weight="bold" fill="#856404">UNSPLICED</text>

                        <!-- Validated NO -->
                        <rect x="520" y="480" width="160" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="600" y="505" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">Mixed model?</text>

                        <!-- Mixed YES -->
                        <rect x="400" y="550" width="120" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5"/>
                        <text x="460" y="575" text-anchor="middle" font-size="11" font-weight="bold" fill="#856404">UNSPLICED</text>

                        <!-- Mixed NO -->
                        <rect x="600" y="550" width="120" height="40" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="5"/>
                        <text x="660" y="575" text-anchor="middle" font-size="11" font-weight="bold" fill="#721c24">AMBIGUOUS</text>

                        <!-- Decision paths -->
                        <path d="M 600 110 L 600 130" stroke="#666" stroke-width="2" marker-end="url(#arrow3)"/>

                        <path d="M 550 150 L 160 200" stroke="#dc3545" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="300" y="175" text-anchor="middle" font-size="10" fill="#dc3545">YES</text>

                        <path d="M 600 170 L 600 200" stroke="#666" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="620" y="185" font-size="10" fill="#666">NO</text>

                        <path d="M 550 225 L 360 270" stroke="#dc3545" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="430" y="245" text-anchor="middle" font-size="10" fill="#dc3545">YES</text>

                        <path d="M 600 240 L 600 270" stroke="#666" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="620" y="255" font-size="10" fill="#666">NO</text>

                        <path d="M 650 295 L 810 340" stroke="#28a745" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="750" y="315" text-anchor="middle" font-size="10" fill="#28a745">YES</text>

                        <path d="M 600 310 L 600 340" stroke="#666" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="620" y="325" font-size="10" fill="#666">NO</text>

                        <path d="M 650 365 L 810 410" stroke="#ffc107" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="750" y="385" text-anchor="middle" font-size="10" fill="#ffc107">YES</text>

                        <path d="M 600 380 L 600 410" stroke="#666" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="620" y="395" font-size="10" fill="#666">NO</text>

                        <path d="M 650 435 L 810 480" stroke="#ffc107" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="750" y="455" text-anchor="middle" font-size="10" fill="#ffc107">YES</text>

                        <path d="M 600 450 L 600 480" stroke="#666" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="620" y="465" font-size="10" fill="#666">NO</text>

                        <path d="M 570 505 L 460 550" stroke="#ffc107" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="490" y="525" text-anchor="middle" font-size="10" fill="#ffc107">YES</text>

                        <path d="M 630 505 L 660 550" stroke="#dc3545" stroke-width="2" marker-end="url(#arrow3)"/>
                        <text x="670" y="525" text-anchor="middle" font-size="10" fill="#dc3545">NO</text>

                        <!-- Legend -->
                        <rect x="50" y="650" width="300" height="120" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="200" y="675" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Classification Results</text>

                        <rect x="70" y="690" width="15" height="15" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                        <text x="95" y="702" font-size="11" fill="#666">SPLICED - Mature mRNA</text>

                        <rect x="70" y="715" width="15" height="15" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                        <text x="95" y="727" font-size="11" fill="#666">UNSPLICED - Pre-mRNA</text>

                        <rect x="70" y="740" width="15" height="15" fill="#f8d7da" stroke="#dc3545" stroke-width="1"/>
                        <text x="95" y="752" font-size="11" fill="#666">AMBIGUOUS - Cannot classify</text>

                        <!-- Arrow marker -->
                        <defs>
                            <marker id="arrow3" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>5.2 Algorithm Pseudocode</h3>
                <div class="code-box">
def classify_read(molitem, geneid2ix, spliced, unspliced, cell_bcidx):
    """
    Classify a single read based on its mapping to genomic features
    """
    # Initialize classification variables
    multi_gene = check_multi_gene_mapping(molitem)
    has_onlyexo_model = check_exon_only_mapping(molitem)
    has_onlyintron_model = check_intron_only_mapping(molitem)
    has_validated_intron = check_validated_intron_mapping(molitem)
    has_exin_intron_span = check_exon_intron_spanning(molitem)
    has_mixed_model = check_mixed_mapping(molitem)

    # Apply decision tree
    if multi_gene:
        return "AMBIGUOUS"  # Multi-gene mapping

    if not molitem.mappings_record:
        return "AMBIGUOUS"  # No gene mapping

    if has_onlyexo_model and not has_onlyintron_model and not has_mixed_model:
        # Exon-only mapping -> SPLICED
        gene_ix = geneid2ix[transcript_model.geneid]
        spliced[gene_ix, cell_bcidx] += 1
        return "SPLICED"

    if has_only_span_exin_model:
        # Exon-intron spanning -> UNSPLICED
        gene_ix = geneid2ix[transcript_model.geneid]
        unspliced[gene_ix, cell_bcidx] += 1
        return "UNSPLICED"

    if has_onlyintron_and_valid_model:
        # Validated intron mapping -> UNSPLICED
        gene_ix = geneid2ix[transcript_model.geneid]
        unspliced[gene_ix, cell_bcidx] += 1
        return "UNSPLICED"

    # Additional complex cases...
    return "AMBIGUOUS"  # Default fallback
                </div>
            </div>

            <!-- Section 6: Practical Examples -->
            <div class="section" id="practical-examples">
                <h2>6. Practical Examples</h2>

                <h3>6.1 Visual Examples of Read Classification</h3>
                <p>Let's examine specific examples of how different read types are classified:</p>

                <div class="svg-container">
                    <svg width="1200" height="600" viewBox="0 0 1200 600">
                        <!-- Background -->
                        <rect width="1200" height="600" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="600" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Read Classification Examples</text>

                        <!-- Gene Model -->
                        <text x="50" y="70" font-size="14" font-weight="bold" fill="#2c3e50">Gene Model:</text>

                        <!-- Exons -->
                        <rect x="100" y="80" width="80" height="20" fill="#28a745" stroke="#155724" stroke-width="1" rx="3"/>
                        <text x="140" y="95" text-anchor="middle" font-size="10" fill="white">Exon 1</text>

                        <rect x="220" y="80" width="80" height="20" fill="#28a745" stroke="#155724" stroke-width="1" rx="3"/>
                        <text x="260" y="95" text-anchor="middle" font-size="10" fill="white">Exon 2</text>

                        <rect x="340" y="80" width="80" height="20" fill="#28a745" stroke="#155724" stroke-width="1" rx="3"/>
                        <text x="380" y="95" text-anchor="middle" font-size="10" fill="white">Exon 3</text>

                        <!-- Introns -->
                        <line x1="180" y1="90" x2="220" y2="90" stroke="#6c757d" stroke-width="2"/>
                        <text x="200" y="85" text-anchor="middle" font-size="8" fill="#6c757d">Intron 1</text>

                        <line x1="300" y1="90" x2="340" y2="90" stroke="#6c757d" stroke-width="2"/>
                        <text x="320" y="85" text-anchor="middle" font-size="8" fill="#6c757d">Intron 2</text>

                        <!-- Example 1: Spliced Read -->
                        <text x="50" y="150" font-size="12" font-weight="bold" fill="#28a745">Example 1: SPLICED Read</text>

                        <!-- Gene model copy -->
                        <rect x="100" y="160" width="80" height="15" fill="#28a745" stroke="#155724" stroke-width="1" rx="2"/>
                        <rect x="220" y="160" width="80" height="15" fill="#28a745" stroke="#155724" stroke-width="1" rx="2"/>
                        <rect x="340" y="160" width="80" height="15" fill="#28a745" stroke="#155724" stroke-width="1" rx="2"/>
                        <line x1="180" y1="167" x2="220" y2="167" stroke="#6c757d" stroke-width="1"/>
                        <line x1="300" y1="167" x2="340" y2="167" stroke="#6c757d" stroke-width="1"/>

                        <!-- Spliced read -->
                        <rect x="120" y="180" width="40" height="8" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                        <text x="140" y="200" text-anchor="middle" font-size="9" fill="#155724">Read maps only to exon</text>
                        <text x="140" y="215" text-anchor="middle" font-size="9" fill="#155724">→ SPLICED</text>

                        <!-- Example 2: Unspliced Read (Intronic) -->
                        <text x="50" y="260" font-size="12" font-weight="bold" fill="#ffc107">Example 2: UNSPLICED Read (Intronic)</text>

                        <!-- Gene model copy -->
                        <rect x="100" y="270" width="80" height="15" fill="#28a745" stroke="#155724" stroke-width="1" rx="2"/>
                        <rect x="220" y="270" width="80" height="15" fill="#28a745" stroke="#155724" stroke-width="1" rx="2"/>
                        <rect x="340" y="270" width="80" height="15" fill="#28a745" stroke="#155724" stroke-width="1" rx="2"/>
                        <line x1="180" y1="277" x2="220" y2="277" stroke="#6c757d" stroke-width="1"/>
                        <line x1="300" y1="277" x2="340" y2="277" stroke="#6c757d" stroke-width="1"/>

                        <!-- Intronic read -->
                        <rect x="185" y="290" width="30" height="8" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                        <text x="200" y="310" text-anchor="middle" font-size="9" fill="#856404">Read maps to validated intron</text>
                        <text x="200" y="325" text-anchor="middle" font-size="9" fill="#856404">→ UNSPLICED</text>

                        <!-- Example 3: Unspliced Read (Spanning) -->
                        <text x="50" y="370" font-size="12" font-weight="bold" fill="#ffc107">Example 3: UNSPLICED Read (Spanning)</text>

                        <!-- Gene model copy -->
                        <rect x="100" y="380" width="80" height="15" fill="#28a745" stroke="#155724" stroke-width="1" rx="2"/>
                        <rect x="220" y="380" width="80" height="15" fill="#28a745" stroke="#155724" stroke-width="1" rx="2"/>
                        <rect x="340" y="380" width="80" height="15" fill="#28a745" stroke="#155724" stroke-width="1" rx="2"/>
                        <line x1="180" y1="387" x2="220" y2="387" stroke="#6c757d" stroke-width="1"/>
                        <line x1="300" y1="387" x2="340" y2="387" stroke="#6c757d" stroke-width="1"/>

                        <!-- Spanning read -->
                        <rect x="160" y="400" width="20" height="8" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                        <rect x="180" y="400" width="40" height="8" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                        <rect x="220" y="400" width="20" height="8" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                        <text x="200" y="420" text-anchor="middle" font-size="9" fill="#856404">Read spans exon-intron boundary</text>
                        <text x="200" y="435" text-anchor="middle" font-size="9" fill="#856404">→ UNSPLICED</text>

                        <!-- Example 4: Ambiguous Read -->
                        <text x="50" y="480" font-size="12" font-weight="bold" fill="#dc3545">Example 4: AMBIGUOUS Read</text>

                        <!-- Two overlapping genes -->
                        <rect x="100" y="490" width="80" height="15" fill="#28a745" stroke="#155724" stroke-width="1" rx="2"/>
                        <text x="140" y="502" text-anchor="middle" font-size="8" fill="white">Gene A</text>

                        <rect x="120" y="510" width="80" height="15" fill="#17a2b8" stroke="#117a8b" stroke-width="1" rx="2"/>
                        <text x="160" y="522" text-anchor="middle" font-size="8" fill="white">Gene B</text>

                        <!-- Ambiguous read -->
                        <rect x="130" y="530" width="40" height="8" fill="#f8d7da" stroke="#dc3545" stroke-width="1"/>
                        <text x="150" y="550" text-anchor="middle" font-size="9" fill="#721c24">Read maps to multiple genes</text>
                        <text x="150" y="565" text-anchor="middle" font-size="9" fill="#721c24">→ AMBIGUOUS</text>

                        <!-- Right side: Statistics -->
                        <rect x="500" y="120" width="650" height="450" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="825" y="145" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Typical Classification Statistics</text>

                        <!-- Pie chart representation -->
                        <circle cx="650" cy="250" r="80" fill="none" stroke="#ccc" stroke-width="2"/>

                        <!-- Spliced segment (60%) -->
                        <path d="M 650 170 A 80 80 0 1 1 706 314 L 650 250 Z" fill="#d4edda" stroke="#28a745" stroke-width="2"/>
                        <text x="680" y="200" font-size="12" fill="#155724" font-weight="bold">60%</text>
                        <text x="680" y="215" font-size="10" fill="#155724">Spliced</text>

                        <!-- Unspliced segment (25%) -->
                        <path d="M 706 314 A 80 80 0 0 1 650 330 L 650 250 Z" fill="#fff3cd" stroke="#ffc107" stroke-width="2"/>
                        <text x="720" y="300" font-size="12" fill="#856404" font-weight="bold">25%</text>
                        <text x="720" y="315" font-size="10" fill="#856404">Unspliced</text>

                        <!-- Ambiguous segment (15%) -->
                        <path d="M 650 330 A 80 80 0 0 1 650 170 L 650 250 Z" fill="#f8d7da" stroke="#dc3545" stroke-width="2"/>
                        <text x="600" y="300" font-size="12" fill="#721c24" font-weight="bold">15%</text>
                        <text x="600" y="315" font-size="10" fill="#721c24">Ambiguous</text>

                        <!-- Statistics table -->
                        <text x="520" y="380" font-size="12" font-weight="bold" fill="#333">Factors Affecting Classification:</text>

                        <text x="520" y="405" font-size="11" fill="#666">• Gene density: Higher density → More ambiguous reads</text>
                        <text x="520" y="425" font-size="11" fill="#666">• Intron length: Longer introns → More unspliced reads</text>
                        <text x="520" y="445" font-size="11" fill="#666">• Read length: Longer reads → Better classification</text>
                        <text x="520" y="465" font-size="11" fill="#666">• Sequencing depth: Higher depth → More spanning reads</text>
                        <text x="520" y="485" font-size="11" fill="#666">• Alternative splicing: More isoforms → More complexity</text>

                        <text x="520" y="520" font-size="12" font-weight="bold" fill="#333">Quality Control Metrics:</text>
                        <text x="520" y="540" font-size="11" fill="#666">• Spliced/Unspliced ratio should be biologically reasonable</text>
                        <text x="520" y="555" font-size="11" fill="#666">• High ambiguous fraction may indicate annotation issues</text>
                    </svg>
                </div>
            </div>

            <!-- Section 7: Edge Cases -->
            <div class="section" id="edge-cases">
                <h2>7. Edge Cases and Ambiguous Situations</h2>

                <h3>7.1 Common Edge Cases</h3>

                <div class="warning-box">
                    <strong>⚠️ Challenging Scenarios:</strong>
                    <ul>
                        <li><strong>Overlapping Genes:</strong> Antisense or overlapping gene annotations create multi-gene mappings</li>
                        <li><strong>Pseudogenes:</strong> Highly similar sequences can cause ambiguous mappings</li>
                        <li><strong>Repetitive Elements:</strong> Reads mapping to repetitive regions are often excluded</li>
                        <li><strong>Short Exons:</strong> Very short exons may be missed by read aligners</li>
                        <li><strong>Alternative Splicing:</strong> Multiple isoforms complicate feature assignment</li>
                    </ul>
                </div>

                <h3>7.2 Technical Limitations</h3>

                <div class="info-box">
                    <strong>🔧 Technical Considerations:</strong>
                    <ul>
                        <li><strong>Read Length:</strong> Shorter reads are less likely to span exon-intron boundaries</li>
                        <li><strong>Sequencing Errors:</strong> Errors can create false splice junctions</li>
                        <li><strong>Mapping Quality:</strong> Low-quality mappings are typically excluded</li>
                        <li><strong>Annotation Quality:</strong> Incomplete or incorrect annotations affect classification</li>
                        <li><strong>Cell Type Specificity:</strong> Some introns may be cell-type specific</li>
                    </ul>
                </div>

                <h3>7.3 Best Practices</h3>

                <div class="success-box">
                    <strong>✅ Recommendations:</strong>
                    <ul>
                        <li><strong>Use High-Quality Annotations:</strong> GENCODE or Ensembl annotations are recommended</li>
                        <li><strong>Filter Low-Quality Reads:</strong> Remove reads with low mapping quality</li>
                        <li><strong>Validate Results:</strong> Check spliced/unspliced ratios for biological plausibility</li>
                        <li><strong>Consider Cell Type:</strong> Different cell types may have different splicing patterns</li>
                        <li><strong>Monitor Ambiguous Fraction:</strong> High ambiguous fractions may indicate problems</li>
                    </ul>
                </div>
            </div>

            <!-- Section 8: Code Implementation -->
            <div class="section" id="code-implementation">
                <h2>8. Code Implementation</h2>

                <h3>8.1 Running Velocyto</h3>
                <p>Here's how to run the complete velocyto pipeline for read counting:</p>

                <div class="code-box">
# Command line usage
velocyto run10x -m repeat_msk.gtf sample_folder genome.gtf

# Python API usage
import velocyto as vcy

# Load the results
vlm = vcy.VelocytoLoom("sample.loom")

# Access the count matrices
spliced_matrix = vlm.S      # Spliced counts
unspliced_matrix = vlm.U    # Unspliced counts
ambiguous_matrix = vlm.A    # Ambiguous counts

# Basic statistics
print(f"Total spliced reads: {vlm.S.sum()}")
print(f"Total unspliced reads: {vlm.U.sum()}")
print(f"Total ambiguous reads: {vlm.A.sum()}")

# Per-gene statistics
spliced_per_gene = vlm.S.sum(axis=1)
unspliced_per_gene = vlm.U.sum(axis=1)
ratio_per_gene = unspliced_per_gene / (spliced_per_gene + 1e-6)

print(f"Mean unspliced/spliced ratio: {ratio_per_gene.mean():.3f}")
                </div>

                <h3>8.2 Quality Control</h3>
                <div class="code-box">
# Quality control checks
import numpy as np
import matplotlib.pyplot as plt

# Check overall classification rates
total_reads = vlm.S.sum() + vlm.U.sum() + vlm.A.sum()
spliced_fraction = vlm.S.sum() / total_reads
unspliced_fraction = vlm.U.sum() / total_reads
ambiguous_fraction = vlm.A.sum() / total_reads

print(f"Spliced fraction: {spliced_fraction:.3f}")
print(f"Unspliced fraction: {unspliced_fraction:.3f}")
print(f"Ambiguous fraction: {ambiguous_fraction:.3f}")

# Flag potential issues
if ambiguous_fraction > 0.3:
    print("WARNING: High ambiguous fraction - check annotation quality")

if unspliced_fraction < 0.1:
    print("WARNING: Low unspliced fraction - may indicate processing issues")

# Plot distribution of unspliced/spliced ratios
ratios = vlm.U.sum(axis=1) / (vlm.S.sum(axis=1) + 1)
plt.hist(ratios, bins=50, alpha=0.7)
plt.xlabel('Unspliced/Spliced Ratio')
plt.ylabel('Number of Genes')
plt.title('Distribution of Unspliced/Spliced Ratios')
plt.show()
                </div>

                <div class="highlight" style="display: block; text-align: center; padding: 1rem; margin: 2rem 0; font-size: 1.1rem;">
                    🎯 <strong>Key Takeaway:</strong> Accurate read classification is the foundation of RNA velocity analysis. Understanding the classification logic helps interpret results and troubleshoot issues in velocity estimation.
                </div>
            </div>
        </div>
    </div>
</body>
</html>
