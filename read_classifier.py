#!/usr/bin/env python3
"""
BAM/GTF Read Classifier for Spliced/Unspliced Identification

This module provides functionality to process BAM and GTF files to identify
and tag reads as spliced, unspliced, or ambiguous based on their alignment
patterns relative to gene annotations.

Author: AI Assistant
Date: 2025-08-03
"""

import os
import sys
import logging
import pysam
import numpy as np
from typing import Dict, List, Tuple, Set, Optional, Union
from collections import defaultdict, OrderedDict
from itertools import chain

# Import velocyto components
import velocyto as vcy


class ReadClassifier:
    """
    Main class for classifying reads from BAM files as spliced/unspliced
    based on GTF gene annotations.
    """
    
    def __init__(self, gtf_file: str, stranded: bool = True, verbose: bool = False):
        """
        Initialize the ReadClassifier.
        
        Parameters:
        -----------
        gtf_file : str
            Path to the GTF annotation file
        stranded : bool, default=True
            Whether to consider strand information
        verbose : bool, default=False
            Enable verbose logging
        """
        self.gtf_file = gtf_file
        self.stranded = stranded
        self.verbose = verbose
        
        # Initialize logging
        log_level = logging.DEBUG if verbose else logging.INFO
        logging.basicConfig(level=log_level, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)
        
        # Initialize data structures
        self.annotations_by_chrm_strand: Dict[str, Dict[str, vcy.TranscriptModel]] = {}
        self.feature_indexes: Dict[str, vcy.FeatureIndex] = {}
        self.geneid2ix: Dict[str, int] = {}
        self.genes: Dict[str, vcy.GeneInfo] = {}
        
        # Load and process GTF file
        self._load_gtf_annotations()
        self._build_feature_indexes()
        
        self.logger.info(f"ReadClassifier initialized with {len(self.genes)} genes")
    
    def _load_gtf_annotations(self):
        """Load and parse GTF annotations using velocyto's infrastructure."""
        self.logger.info(f"Loading GTF annotations from {self.gtf_file}")
        
        # Use velocyto's GTF parsing functionality
        # This is similar to how ExInCounter loads annotations
        from velocyto.counter import ExInCounter
        
        # Create a temporary counter to use its GTF parsing methods
        temp_counter = ExInCounter("temp", vcy.logic.Permissive10X)
        
        # Parse the GTF file
        self.annotations_by_chrm_strand = temp_counter.read_transcriptmodels(self.gtf_file)
        
        # Build gene index
        gene_counter = 0
        for chromstrand_key, transcript_models in self.annotations_by_chrm_strand.items():
            for transcript_id, transcript_model in transcript_models.items():
                gene_id = transcript_model.geneid
                if gene_id not in self.geneid2ix:
                    self.geneid2ix[gene_id] = gene_counter
                    self.genes[gene_id] = vcy.GeneInfo(gene_id, transcript_model.genename, transcript_model.chrom)
                    gene_counter += 1
        
        self.logger.info(f"Loaded {len(self.annotations_by_chrm_strand)} chromosome/strand combinations")
        self.logger.info(f"Found {len(self.genes)} unique genes")
    
    def _build_feature_indexes(self):
        """Build feature indexes for efficient overlap detection."""
        self.logger.info("Building feature indexes for overlap detection")
        
        for chromstrand_key, transcript_models in self.annotations_by_chrm_strand.items():
            # Collect all features (exons and introns) for this chromosome/strand
            all_features = list(chain.from_iterable(transcript_models.values()))
            
            # Create sorted feature index
            self.feature_indexes[chromstrand_key] = vcy.FeatureIndex(sorted(all_features))
        
        self.logger.info(f"Built feature indexes for {len(self.feature_indexes)} chromosome/strand combinations")

    def _alignment_to_read(self, alignment: pysam.AlignedSegment) -> Optional[vcy.Read]:
        """
        Convert a pysam alignment to a velocyto Read object.

        Parameters:
        -----------
        alignment : pysam.AlignedSegment
            The alignment from the BAM file

        Returns:
        --------
        vcy.Read or None
            The velocyto Read object, or None if the alignment should be skipped
        """
        # Skip unmapped reads
        if alignment.is_unmapped:
            return None

        # Extract cell barcode and UMI if available
        bc = None
        umi = None

        try:
            if alignment.has_tag("CB"):
                bc = alignment.get_tag("CB")
            if alignment.has_tag("UB"):
                umi = alignment.get_tag("UB")
        except:
            pass

        # Use read name as barcode if no CB tag
        if bc is None:
            bc = alignment.query_name

        # Use a dummy UMI if no UB tag
        if umi is None:
            umi = "DUMMY_UMI"

        # Determine strand
        strand = "-" if alignment.is_reverse else "+"

        # Parse CIGAR to get genomic segments
        segments = []
        pos = alignment.reference_start
        ref_skipped = False

        if alignment.cigartuples:
            for operation, length in alignment.cigartuples:
                if operation == 0:  # Match/mismatch (M)
                    segments.append((pos, pos + length - 1))
                    pos += length
                elif operation == 3:  # Skip/intron (N)
                    pos += length
                    ref_skipped = True
                elif operation == 2:  # Deletion (D)
                    pos += length
                elif operation == 1:  # Insertion (I)
                    pass  # Don't advance reference position
                elif operation == 4:  # Soft clipping (S)
                    pass  # Don't advance reference position
                elif operation == 5:  # Hard clipping (H)
                    pass  # Don't advance reference position

        # Skip reads with no valid segments
        if not segments:
            return None

        # Create Read object
        return vcy.Read(
            bc=bc,
            umi=umi,
            chrom=alignment.reference_name,
            strand=strand,
            pos=alignment.reference_start,
            segments=segments,
            clip5=0,  # Simplified - not calculating exact clipping
            clip3=0,  # Simplified - not calculating exact clipping
            ref_skipped=ref_skipped
        )

    def _classify_read(self, read: vcy.Read) -> Tuple[str, str, List[str]]:
        """
        Classify a read as spliced, unspliced, or ambiguous.

        Parameters:
        -----------
        read : vcy.Read
            The read to classify

        Returns:
        --------
        Tuple[str, str, List[str]]
            (classification, gene_id, details)
            classification: 'spliced', 'unspliced', 'ambiguous'
            gene_id: the gene ID if mapped to a single gene, else 'multi_gene' or 'no_gene'
            details: list of detailed classification information
        """
        # Get the appropriate feature index
        if self.stranded:
            chromstrand_key = read.chrom + read.strand
        else:
            # For unstranded, try both strands
            chromstrand_key = read.chrom + "+"

        if chromstrand_key not in self.feature_indexes:
            if not self.stranded:
                chromstrand_key = read.chrom + "-"
                if chromstrand_key not in self.feature_indexes:
                    return "ambiguous", "no_gene", ["No annotations for chromosome"]
            else:
                return "ambiguous", "no_gene", ["No annotations for chromosome/strand"]

        feature_index = self.feature_indexes[chromstrand_key]

        # Find overlapping features
        overlapping_features = []
        feature_index.reset()

        # Check each segment of the read against features
        for segment_start, segment_end in read.segments:
            # Create a temporary read-like object for overlap detection
            temp_read = vcy.Read(
                bc=read.bc, umi=read.umi, chrom=read.chrom, strand=read.strand,
                pos=segment_start, segments=[(segment_start, segment_end)],
                clip5=0, clip3=0, ref_skipped=False
            )

            # Find overlapping intervals
            feature_index.mark_overlapping_ivls(temp_read)

            # Collect the overlapping features
            for feature in feature_index.ivls:
                if (feature.start <= segment_end and feature.end >= segment_start):
                    overlapping_features.append(feature)

        return self._apply_classification_logic(read, overlapping_features)

    def _apply_classification_logic(self, read: vcy.Read, overlapping_features: List[vcy.Feature]) -> Tuple[str, str, List[str]]:
        """
        Apply classification logic based on overlapping features.

        Parameters:
        -----------
        read : vcy.Read
            The read being classified
        overlapping_features : List[vcy.Feature]
            List of features that overlap with the read

        Returns:
        --------
        Tuple[str, str, List[str]]
            (classification, gene_id, details)
        """
        if not overlapping_features:
            return "ambiguous", "no_gene", ["No overlapping features"]

        # Group features by gene
        genes_found = set()
        exon_features = []
        intron_features = []
        details = []

        for feature in overlapping_features:
            genes_found.add(feature.transcript_model.geneid)

            if feature.kind == ord("e"):  # Exon
                exon_features.append(feature)
                details.append(f"Overlaps exon: {feature.transcript_model.geneid}")
            elif feature.kind == ord("i"):  # Intron
                intron_features.append(feature)
                details.append(f"Overlaps intron: {feature.transcript_model.geneid}")

        # Check for multi-gene mapping
        if len(genes_found) > 1:
            return "ambiguous", "multi_gene", details + ["Maps to multiple genes"]

        gene_id = list(genes_found)[0] if genes_found else "no_gene"

        # Apply simplified classification rules
        has_exons = len(exon_features) > 0
        has_introns = len(intron_features) > 0
        is_spliced_read = read.is_spliced  # Read spans multiple segments (has introns in alignment)

        # Classification logic:
        # 1. If read is spliced (spans introns in alignment) and maps to exons -> SPLICED
        # 2. If read maps to introns or spans exon-intron boundaries -> UNSPLICED
        # 3. If read maps only to exons and is not spliced -> SPLICED
        # 4. Otherwise -> AMBIGUOUS

        if is_spliced_read and has_exons and not has_introns:
            # Spliced read mapping only to exons = mature mRNA
            return "spliced", gene_id, details + ["Spliced read in exons"]
        elif has_introns:
            # Any mapping to introns = pre-mRNA/unspliced
            return "unspliced", gene_id, details + ["Maps to introns"]
        elif has_exons and not is_spliced_read:
            # Unspliced read mapping only to exons = mature mRNA
            return "spliced", gene_id, details + ["Exon-only mapping"]
        elif has_exons and is_spliced_read:
            # Spliced read in exons (already covered above, but for completeness)
            return "spliced", gene_id, details + ["Spliced read in exons"]
        else:
            return "ambiguous", gene_id, details + ["Unclear classification"]

    def process_bam_file(self, bam_file: str, output_file: str = None, max_reads: int = None) -> List[Dict]:
        """
        Process a BAM file and classify all reads.

        Parameters:
        -----------
        bam_file : str
            Path to the BAM file
        output_file : str, optional
            Path to output TSV file. If None, no file is written.
        max_reads : int, optional
            Maximum number of reads to process (for testing)

        Returns:
        --------
        List[Dict]
            List of dictionaries containing read classification results
        """
        self.logger.info(f"Processing BAM file: {bam_file}")

        results = []
        read_count = 0

        # Open BAM file
        with pysam.AlignmentFile(bam_file, "rb") as bamfile:
            for alignment in bamfile:
                # Convert to velocyto Read object
                read = self._alignment_to_read(alignment)
                if read is None:
                    continue

                # Classify the read
                classification, gene_id, details = self._classify_read(read)

                # Store result
                result = {
                    'read_name': alignment.query_name,
                    'cell_barcode': read.bc,
                    'umi': read.umi,
                    'chromosome': read.chrom,
                    'strand': read.strand,
                    'start': read.start,
                    'end': read.end,
                    'is_spliced': read.is_spliced,
                    'classification': classification,
                    'gene_id': gene_id,
                    'details': '; '.join(details)
                }
                results.append(result)

                read_count += 1
                if read_count % 10000 == 0:
                    self.logger.info(f"Processed {read_count} reads")

                # Stop if max_reads reached
                if max_reads and read_count >= max_reads:
                    break

        self.logger.info(f"Finished processing {read_count} reads")

        # Write output file if specified
        if output_file:
            self._write_results_to_file(results, output_file)

        return results

    def _write_results_to_file(self, results: List[Dict], output_file: str):
        """Write classification results to a TSV file."""
        self.logger.info(f"Writing results to {output_file}")

        with open(output_file, 'w') as f:
            # Write header
            header = ['read_name', 'cell_barcode', 'umi', 'chromosome', 'strand',
                     'start', 'end', 'is_spliced', 'classification', 'gene_id', 'details']
            f.write('\t'.join(header) + '\n')

            # Write data
            for result in results:
                row = [str(result[col]) for col in header]
                f.write('\t'.join(row) + '\n')

        self.logger.info(f"Results written to {output_file}")

    def get_classification_summary(self, results: List[Dict]) -> Dict[str, int]:
        """
        Get summary statistics of read classifications.

        Parameters:
        -----------
        results : List[Dict]
            Results from process_bam_file

        Returns:
        --------
        Dict[str, int]
            Summary counts for each classification type
        """
        summary = {
            'total_reads': len(results),
            'spliced': 0,
            'unspliced': 0,
            'ambiguous': 0,
            'multi_gene': 0,
            'no_gene': 0
        }

        for result in results:
            classification = result['classification']
            summary[classification] += 1

            if result['gene_id'] == 'multi_gene':
                summary['multi_gene'] += 1
            elif result['gene_id'] == 'no_gene':
                summary['no_gene'] += 1

        return summary

    def print_summary(self, results: List[Dict]):
        """Print a summary of classification results."""
        summary = self.get_classification_summary(results)

        print("\n" + "="*50)
        print("READ CLASSIFICATION SUMMARY")
        print("="*50)
        print(f"Total reads processed: {summary['total_reads']:,}")
        print(f"Spliced reads:         {summary['spliced']:,} ({summary['spliced']/summary['total_reads']*100:.1f}%)")
        print(f"Unspliced reads:       {summary['unspliced']:,} ({summary['unspliced']/summary['total_reads']*100:.1f}%)")
        print(f"Ambiguous reads:       {summary['ambiguous']:,} ({summary['ambiguous']/summary['total_reads']*100:.1f}%)")
        print(f"Multi-gene mappings:   {summary['multi_gene']:,} ({summary['multi_gene']/summary['total_reads']*100:.1f}%)")
        print(f"No gene mappings:      {summary['no_gene']:,} ({summary['no_gene']/summary['total_reads']*100:.1f}%)")
        print("="*50)


def main():
    """Example usage of the ReadClassifier."""
    import argparse

    parser = argparse.ArgumentParser(description='Classify BAM reads as spliced/unspliced based on GTF annotations')
    parser.add_argument('bam_file', help='Input BAM file')
    parser.add_argument('gtf_file', help='Input GTF annotation file')
    parser.add_argument('-o', '--output', help='Output TSV file (optional)')
    parser.add_argument('--max-reads', type=int, help='Maximum reads to process (for testing)')
    parser.add_argument('--unstranded', action='store_true', help='Treat data as unstranded')
    parser.add_argument('-v', '--verbose', action='store_true', help='Verbose logging')

    args = parser.parse_args()

    # Initialize classifier
    classifier = ReadClassifier(
        gtf_file=args.gtf_file,
        stranded=not args.unstranded,
        verbose=args.verbose
    )

    # Process BAM file
    results = classifier.process_bam_file(
        bam_file=args.bam_file,
        output_file=args.output,
        max_reads=args.max_reads
    )

    # Print summary
    classifier.print_summary(results)


if __name__ == "__main__":
    main()
