<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FeatureIndex Class Tutorial</title>
    
    <!-- MathJax 3 for LaTeX rendering -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.8rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 1rem 0 0 0;
            font-size: 1.3rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
            border-radius: 0 8px 8px 0;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.8rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        
        .section h3 {
            color: #34495e;
            margin-top: 2rem;
            font-size: 1.4rem;
        }
        
        .code-box {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            font-size: 0.9rem;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .svg-container {
            text-align: center;
            margin: 2rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .info-box {
            background: #d1ecf1;
            border: 2px solid #17a2b8;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .success-box {
            background: #d4edda;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .method-box {
            background: white;
            border: 2px solid #3498db;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            position: relative;
        }
        
        .method-name {
            background: #3498db;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            position: absolute;
            top: -15px;
            left: 20px;
        }
        
        .step-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 1rem;
            margin: 0.5rem 0;
            border-left: 4px solid #28a745;
        }
        
        .step-number {
            background: #28a745;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
            font-size: 0.9rem;
        }
        
        .feature-type {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-weight: bold;
            margin: 0.2rem;
            display: inline-block;
        }
        
        .exon {
            background: #d4edda;
            color: #155724;
            border: 1px solid #28a745;
        }
        
        .intron {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffc107;
        }
        
        .masked {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #dc3545;
        }
        
        .toc {
            background: #34495e;
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .toc h3 {
            margin-top: 0;
            color: #ecf0f1;
        }
        
        .toc ul {
            list-style: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 0.5rem 0;
            padding-left: 1rem;
        }
        
        .toc a {
            color: #3498db;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .toc a:hover {
            color: #5dade2;
        }
        
        .math-box {
            background: #fff;
            border: 2px solid #3498db;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            box-shadow: 0 2px 10px rgba(52, 152, 219, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧬 FeatureIndex Class Tutorial</h1>
            <p>Understanding Velocyto's Genomic Feature Search Engine</p>
        </div>
        
        <div class="content">
            <!-- Table of Contents -->
            <div class="toc">
                <h3>📚 Table of Contents</h3>
                <ul>
                    <li><a href="#introduction">1. Introduction and Overview</a></li>
                    <li><a href="#class-structure">2. Class Structure and Attributes</a></li>
                    <li><a href="#initialization">3. Initialization and Sorting</a></li>
                    <li><a href="#core-methods">4. Core Methods</a></li>
                    <li><a href="#overlap-detection">5. Overlap Detection Algorithm</a></li>
                    <li><a href="#intron-validation">6. Intron Validation Process</a></li>
                    <li><a href="#mapping-records">7. Mapping Records Generation</a></li>
                    <li><a href="#performance">8. Performance and Optimization</a></li>
                </ul>
            </div>

            <!-- Section 1: Introduction -->
            <div class="section" id="introduction">
                <h2>1. Introduction and Overview</h2>
                
                <div class="info-box">
                    <strong>🎯 Purpose:</strong> The <code>FeatureIndex</code> class is velocyto's core search engine for efficiently finding overlaps between sequencing reads and genomic features (exons, introns, masked regions). It serves as the foundation for accurate read classification in RNA velocity analysis.
                </div>
                
                <h3>1.1 What is FeatureIndex?</h3>
                <p>FeatureIndex is a <span class="highlight">"Search help class used to find the intervals that a read is spanning"</span> according to the source code. It implements an efficient algorithm for genomic interval overlap detection.</p>
                
                <div class="step-box">
                    <div class="step-number">1</div>
                    <strong>Efficient Search:</strong> Provides O(log n) lookup of genomic features that overlap with read segments
                </div>
                
                <div class="step-box">
                    <div class="step-number">2</div>
                    <strong>Intron Validation:</strong> Marks introns as validated when reads span exon-intron boundaries
                </div>
                
                <div class="step-box">
                    <div class="step-number">3</div>
                    <strong>Mapping Records:</strong> Generates detailed mapping records linking reads to transcript models
                </div>
                
                <h3>1.2 The Overlap Problem</h3>
                <div class="math-box">
                    <p><strong>Computational Challenge:</strong></p>
                    <p>Given a read with segments $R = \{s_1, s_2, ..., s_n\}$ and genomic features $F = \{f_1, f_2, ..., f_m\}$, efficiently find:</p>
                    
                    $$\text{overlaps}(R, F) = \{(s_i, f_j) : \text{overlap}(s_i, f_j) = \text{True}\}$$
                    
                    <p>Where overlap is defined as:</p>
                    $$\text{overlap}(s_i, f_j) = (\text{start}_i \leq \text{end}_j) \land (\text{end}_i \geq \text{start}_j)$$
                    
                    <p><strong>Naive approach:</strong> O(n×m) - check every read segment against every feature</p>
                    <p><strong>FeatureIndex approach:</strong> O(n×log m) - sorted features with efficient search</p>
                </div>
            </div>

            <!-- Section 2: Class Structure -->
            <div class="section" id="class-structure">
                <h2>2. Class Structure and Attributes</h2>

                <h3>2.1 Class Definition</h3>
                <div class="code-box">
class FeatureIndex:
    """Search help class used to find the intervals that a read is spanning"""

    def __init__(self, ivls: List[vcy.Feature]=[]) -> None:
        self.ivls = ivls              # List of Feature objects
        self.ivls.sort()              # Features sorted by genomic position
        self.iidx = 0                 # Current feature index during iteration
        self.maxiidx = len(ivls) - 1  # Maximum valid index
                </div>

                <h3>2.2 Core Attributes</h3>

                <div class="method-box">
                    <div class="method-name">ivls: List[Feature]</div>
                    <p><strong>Purpose:</strong> Stores the list of genomic features (exons, introns, masked regions)</p>
                    <p><strong>Sorting:</strong> Automatically sorted by genomic coordinates for efficient search</p>
                    <p><strong>Content:</strong> Each Feature has start, end, kind, exin_no, is_validated, transcript_model</p>
                </div>

                <div class="method-box">
                    <div class="method-name">iidx: int</div>
                    <p><strong>Purpose:</strong> Current position in the feature list during iteration</p>
                    <p><strong>Optimization:</strong> Maintains state between reads to avoid re-scanning from beginning</p>
                    <p><strong>Reset:</strong> Can be reset to 0 using the reset() method</p>
                </div>

                <div class="method-box">
                    <div class="method-name">maxiidx: int</div>
                    <p><strong>Purpose:</strong> Maximum valid index (len(ivls) - 1)</p>
                    <p><strong>Boundary Check:</strong> Used to prevent index out of bounds errors</p>
                    <p><strong>Loop Control:</strong> Controls iteration termination in search loops</p>
                </div>

                <h3>2.3 Feature Types</h3>
                <p>FeatureIndex works with three types of genomic features:</p>

                <div class="feature-type exon">Exon (kind = ord("e") = 101)</div>
                <div class="feature-type intron">Intron (kind = ord("i") = 105)</div>
                <div class="feature-type masked">Masked Repeat (kind = ord("m") = 109)</div>

                <div class="warning-box">
                    <strong>⚠️ Important Note:</strong> The comment in the code mentions "maybe I am sorting twice" - this suggests features might already be sorted when passed to FeatureIndex, but the class ensures sorting for safety.
                </div>
            </div>

            <!-- Section 3: Initialization -->
            <div class="section" id="initialization">
                <h2>3. Initialization and Sorting</h2>

                <h3>3.1 Constructor Logic</h3>
                <div class="code-box">
def __init__(self, ivls: List[vcy.Feature]=[]) -> None:
    self.ivls = ivls
    self.ivls.sort()  # Crucial for efficient search
    self.iidx = 0     # Start at beginning
    self.maxiidx = len(ivls) - 1
                </div>

                <h3>3.2 Feature Sorting</h3>
                <p>Features are sorted using the Feature class's comparison methods:</p>

                <div class="code-box">
# Feature class sorting logic (from feature.py)
def __lt__(self, other: Any) -> bool:
    if self.start == other.start:
        return self.end < other.end  # If same start, sort by end
    return self.start < other.start  # Primary sort by start position

def __gt__(self, other: Any) -> bool:
    if self.start == other.start:
        return self.end > other.end
    return self.start > other.start
                </div>

                <div class="info-box">
                    <strong>📊 Sorting Strategy:</strong>
                    <ul>
                        <li><strong>Primary Key:</strong> Start position (ascending)</li>
                        <li><strong>Secondary Key:</strong> End position (ascending)</li>
                        <li><strong>Result:</strong> Features ordered left-to-right on genome</li>
                        <li><strong>Benefit:</strong> Enables efficient left-to-right scanning</li>
                    </ul>
                </div>

                <h3>3.3 Index Management</h3>

                <div class="svg-container">
                    <svg width="1000" height="300" viewBox="0 0 1000 300">
                        <!-- Background -->
                        <rect width="1000" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="500" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">FeatureIndex State Management</text>

                        <!-- Genome representation -->
                        <line x1="100" y1="100" x2="900" y2="100" stroke="#333" stroke-width="3"/>
                        <text x="500" y="90" text-anchor="middle" font-size="12" fill="#666">Genomic Coordinate →</text>

                        <!-- Features -->
                        <rect x="150" y="80" width="60" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                        <text x="180" y="95" text-anchor="middle" font-size="10" fill="#155724">Exon 1</text>

                        <rect x="250" y="80" width="80" height="20" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                        <text x="290" y="95" text-anchor="middle" font-size="10" fill="#856404">Intron 1</text>

                        <rect x="370" y="80" width="60" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                        <text x="400" y="95" text-anchor="middle" font-size="10" fill="#155724">Exon 2</text>

                        <rect x="470" y="80" width="80" height="20" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                        <text x="510" y="95" text-anchor="middle" font-size="10" fill="#856404">Intron 2</text>

                        <rect x="590" y="80" width="60" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                        <text x="620" y="95" text-anchor="middle" font-size="10" fill="#155724">Exon 3</text>

                        <!-- Index pointer -->
                        <path d="M 290 130 L 290 110" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrow)"/>
                        <text x="290" y="145" text-anchor="middle" font-size="12" fill="#e74c3c" font-weight="bold">iidx = 1</text>

                        <!-- Index array representation -->
                        <rect x="100" y="180" width="800" height="80" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="500" y="205" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Internal Feature Array (sorted)</text>

                        <rect x="120" y="220" width="80" height="30" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <text x="160" y="240" text-anchor="middle" font-size="10" fill="#1976d2">ivls[0]<br/>Exon 1</text>

                        <rect x="220" y="220" width="80" height="30" fill="#fff3e0" stroke="#f57c00" stroke-width="2"/>
                        <text x="260" y="240" text-anchor="middle" font-size="10" fill="#f57c00">ivls[1]<br/>Intron 1</text>

                        <rect x="320" y="220" width="80" height="30" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <text x="360" y="240" text-anchor="middle" font-size="10" fill="#1976d2">ivls[2]<br/>Exon 2</text>

                        <rect x="420" y="220" width="80" height="30" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <text x="460" y="240" text-anchor="middle" font-size="10" fill="#1976d2">ivls[3]<br/>Intron 2</text>

                        <rect x="520" y="220" width="80" height="30" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <text x="560" y="240" text-anchor="middle" font-size="10" fill="#1976d2">ivls[4]<br/>Exon 3</text>

                        <!-- Current index indicator -->
                        <text x="260" y="270" text-anchor="middle" font-size="12" fill="#f57c00" font-weight="bold">Current Position</text>

                        <!-- Arrow marker definition -->
                        <defs>
                            <marker id="arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#e74c3c"/>
                            </marker>
                        </defs>
                    </svg>
                </div>
            </div>

            <!-- Section 4: Core Methods -->
            <div class="section" id="core-methods">
                <h2>4. Core Methods Overview</h2>

                <h3>4.1 Method Summary</h3>
                <p>FeatureIndex provides four main methods for different types of overlap analysis:</p>

                <div class="method-box">
                    <div class="method-name">reset()</div>
                    <p><strong>Purpose:</strong> Reset the current feature index to the beginning</p>
                    <div class="code-box">
def reset(self) -> None:
    """It set the current feature to the first feature"""
    self.iidx = 0
                    </div>
                    <p><strong>Usage:</strong> Called between batches to restart scanning from the beginning</p>
                </div>

                <div class="method-box">
                    <div class="method-name">last_interval_not_reached</div>
                    <p><strong>Purpose:</strong> Property to check if there are more features to examine</p>
                    <div class="code-box">
@property
def last_interval_not_reached(self) -> bool:
    return self.iidx < self.maxiidx
                    </div>
                    <p><strong>Usage:</strong> Loop control in search algorithms</p>
                </div>

                <div class="method-box">
                    <div class="method-name">has_ivls_enclosing(read)</div>
                    <p><strong>Purpose:</strong> Check if ALL read segments are fully contained within features</p>
                    <p><strong>Returns:</strong> Boolean - True if all segments are enclosed</p>
                    <p><strong>Use Case:</strong> Detecting reads in masked/repeat regions</p>
                </div>

                <div class="method-box">
                    <div class="method-name">mark_overlapping_ivls(read)</div>
                    <p><strong>Purpose:</strong> Mark introns as validated when reads span exon-intron boundaries</p>
                    <p><strong>Returns:</strong> None (modifies Feature objects in place)</p>
                    <p><strong>Use Case:</strong> Intron validation for accurate unspliced read classification</p>
                </div>

                <div class="method-box">
                    <div class="method-name">find_overlapping_ivls(read)</div>
                    <p><strong>Purpose:</strong> Generate complete mapping records for read classification</p>
                    <p><strong>Returns:</strong> Dict[TranscriptModel, List[SegmentMatch]]</p>
                    <p><strong>Use Case:</strong> Primary method for read-to-feature mapping</p>
                </div>

                <h3>4.2 Common Algorithm Pattern</h3>
                <p>All search methods follow a similar optimization pattern:</p>

                <div class="step-box">
                    <div class="step-number">1</div>
                    <strong>Skip Past Features:</strong> Move iidx forward until finding features that could overlap with the read
                </div>

                <div class="step-box">
                    <div class="step-number">2</div>
                    <strong>Local Search:</strong> For each read segment, search forward from current position
                </div>

                <div class="step-box">
                    <div class="step-number">3</div>
                    <strong>Overlap Detection:</strong> Check various types of overlaps (contains, partial, spanning)
                </div>

                <div class="step-box">
                    <div class="step-number">4</div>
                    <strong>State Maintenance:</strong> Keep iidx at optimal position for next read
                </div>

                <div class="code-box">
# Common pattern in all search methods:
feature = self.ivls[self.iidx]  # Current feature

# Skip features that end before the read starts
while self.last_interval_not_reached and feature.ends_upstream_of(read):
    self.iidx += 1
    feature = self.ivls[self.iidx]

# Local search for each segment
for segment in read.segments:
    i = self.iidx
    feature = self.ivls[i]
    while i < self.maxiidx and feature.doesnt_start_after(segment):
        # Check overlaps here
        i += 1
        feature = self.ivls[i]
                </div>
            </div>

            <!-- Section 5: Overlap Detection -->
            <div class="section" id="overlap-detection">
                <h2>5. Overlap Detection Algorithm</h2>

                <h3>5.1 Types of Overlaps</h3>
                <p>FeatureIndex detects several types of overlaps between read segments and genomic features:</p>

                <div class="svg-container">
                    <svg width="1200" height="500" viewBox="0 0 1200 500">
                        <!-- Background -->
                        <rect width="1200" height="500" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="600" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Types of Read-Feature Overlaps</text>

                        <!-- Feature representation -->
                        <rect x="200" y="70" width="200" height="30" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
                        <text x="300" y="90" text-anchor="middle" font-size="12" fill="#1976d2" font-weight="bold">Genomic Feature</text>

                        <!-- Type 1: Contains -->
                        <text x="50" y="140" font-size="14" font-weight="bold" fill="#28a745">1. CONTAINS</text>
                        <rect x="200" y="150" width="200" height="20" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <rect x="250" y="155" width="100" height="10" fill="#d4edda" stroke="#28a745" stroke-width="2"/>
                        <text x="300" y="190" text-anchor="middle" font-size="10" fill="#666">Read segment fully inside feature</text>

                        <!-- Type 2: Start Overlap -->
                        <text x="50" y="220" font-size="14" font-weight="bold" fill="#f57c00">2. START OVERLAP</text>
                        <rect x="200" y="230" width="200" height="20" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <rect x="150" y="235" width="100" height="10" fill="#fff3cd" stroke="#f57c00" stroke-width="2"/>
                        <text x="300" y="270" text-anchor="middle" font-size="10" fill="#666">Read overlaps feature start</text>

                        <!-- Type 3: End Overlap -->
                        <text x="50" y="300" font-size="14" font-weight="bold" fill="#dc3545">3. END OVERLAP</text>
                        <rect x="200" y="310" width="200" height="20" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <rect x="350" y="315" width="100" height="10" fill="#f8d7da" stroke="#dc3545" stroke-width="2"/>
                        <text x="300" y="350" text-anchor="middle" font-size="10" fill="#666">Read overlaps feature end</text>

                        <!-- Type 4: Spanning -->
                        <text x="50" y="380" font-size="14" font-weight="bold" fill="#9c27b0">4. SPANNING</text>
                        <rect x="200" y="390" width="200" height="20" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <rect x="150" y="395" width="300" height="10" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2"/>
                        <text x="300" y="430" text-anchor="middle" font-size="10" fill="#666">Read spans entire feature</text>

                        <!-- Match type constants -->
                        <rect x="600" y="120" width="500" height="300" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="850" y="145" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Match Type Constants</text>

                        <text x="620" y="180" font-size="12" fill="#28a745" font-weight="bold">MATCH_INSIDE</text>
                        <text x="620" y="200" font-size="11" fill="#666">• Segment fully contained within feature</text>
                        <text x="620" y="215" font-size="11" fill="#666">• Used for exonic reads and intronic reads</text>

                        <text x="620" y="250" font-size="12" fill="#f57c00" font-weight="bold">MATCH_OVER5END</text>
                        <text x="620" y="270" font-size="11" fill="#666">• Segment overlaps feature start (5' end)</text>
                        <text x="620" y="285" font-size="11" fill="#666">• Indicates potential exon-intron spanning</text>

                        <text x="620" y="320" font-size="12" fill="#dc3545" font-weight="bold">MATCH_OVER3END</text>
                        <text x="620" y="340" font-size="11" fill="#666">• Segment overlaps feature end (3' end)</text>
                        <text x="620" y="355" font-size="11" fill="#666">• Indicates potential intron-exon spanning</text>

                        <text x="620" y="390" font-size="12" fill="#666" font-weight="bold">Bitwise Combination:</text>
                        <text x="620" y="410" font-size="11" fill="#666">• Multiple match types can be combined</text>
                        <text x="620" y="425" font-size="11" fill="#666">• Uses bitwise OR (|) operations</text>
                    </svg>
                </div>

                <h3>5.2 Overlap Detection Code</h3>
                <div class="code-box">
# From has_ivls_enclosing method - overlap detection logic
for segment in read.segments:
    segment_matchtype = 0
    i = self.iidx
    ivl = self.ivls[self.iidx]

    while i < self.maxiidx and ivl.doesnt_start_after(segment):
        matchtype = 0  # No match initially

        if ivl.contains(segment):
            matchtype = vcy.MATCH_INSIDE

        if ivl.start_overlaps_with_part_of(segment):
            matchtype |= vcy.MATCH_OVER5END

        if ivl.end_overlaps_with_part_of(segment):
            matchtype |= vcy.MATCH_OVER3END

        segment_matchtype |= matchtype
        i += 1
        ivl = self.ivls[i]
                </div>
            </div>

            <!-- Section 6: Intron Validation -->
            <div class="section" id="intron-validation">
                <h2>6. Intron Validation Process</h2>

                <h3>6.1 Why Intron Validation Matters</h3>
                <div class="warning-box">
                    <strong>⚠️ Critical for Accuracy:</strong> Not all reads mapping to intronic regions represent true pre-mRNA. Intron validation ensures that only introns with evidence of active splicing are used for unspliced read classification.
                </div>

                <h3>6.2 Validation Criteria</h3>
                <p>An intron is marked as validated when reads span exon-intron boundaries:</p>

                <div class="svg-container">
                    <svg width="1000" height="400" viewBox="0 0 1000 400">
                        <!-- Background -->
                        <rect width="1000" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="500" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Intron Validation Through Spanning Reads</text>

                        <!-- Gene structure -->
                        <rect x="100" y="80" width="80" height="25" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                        <text x="140" y="97" text-anchor="middle" font-size="10" fill="#155724">Exon 1</text>

                        <rect x="200" y="80" width="120" height="25" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                        <text x="260" y="97" text-anchor="middle" font-size="10" fill="#856404">Intron 1</text>

                        <rect x="340" y="80" width="80" height="25" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                        <text x="380" y="97" text-anchor="middle" font-size="10" fill="#155724">Exon 2</text>

                        <!-- Spanning read 1 (left boundary) -->
                        <rect x="160" y="130" width="60" height="15" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
                        <text x="190" y="142" text-anchor="middle" font-size="9" fill="#1976d2">Spanning Read 1</text>
                        <text x="190" y="160" text-anchor="middle" font-size="8" fill="#666">Exon1 → Intron1</text>

                        <!-- Spanning read 2 (right boundary) -->
                        <rect x="300" y="130" width="60" height="15" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2"/>
                        <text x="330" y="142" text-anchor="middle" font-size="9" fill="#9c27b0">Spanning Read 2</text>
                        <text x="330" y="160" text-anchor="middle" font-size="8" fill="#666">Intron1 → Exon2</text>

                        <!-- Validation arrows -->
                        <path d="M 190 150 L 260 105" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrow2)"/>
                        <path d="M 330 150 L 260 105" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrow2)"/>

                        <text x="260" y="125" text-anchor="middle" font-size="12" fill="#e74c3c" font-weight="bold">VALIDATED!</text>

                        <!-- Code representation -->
                        <rect x="100" y="200" width="800" height="150" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="500" y="225" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Validation Algorithm</text>

                        <text x="120" y="250" font-size="11" fill="#666" font-weight="bold">Left Boundary Check:</text>
                        <text x="120" y="270" font-size="10" fill="#666">if feature.end_overlaps_with_part_of(segment):</text>
                        <text x="140" y="285" font-size="10" fill="#666">downstream_exon = feature.get_downstream_exon()</text>
                        <text x="140" y="300" font-size="10" fill="#666">if downstream_exon.start_overlaps_with_part_of(segment):</text>
                        <text x="160" y="315" font-size="10" fill="#666">feature.is_validated = True</text>

                        <text x="500" y="250" font-size="11" fill="#666" font-weight="bold">Right Boundary Check:</text>
                        <text x="500" y="270" font-size="10" fill="#666">if feature.start_overlaps_with_part_of(segment):</text>
                        <text x="520" y="285" font-size="10" fill="#666">upstream_exon = feature.get_upstream_exon()</text>
                        <text x="520" y="300" font-size="10" fill="#666">if upstream_exon.end_overlaps_with_part_of(segment):</text>
                        <text x="540" y="315" font-size="10" fill="#666">feature.is_validated = True</text>

                        <!-- Arrow marker definition -->
                        <defs>
                            <marker id="arrow2" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#e74c3c"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>6.3 Validation Implementation</h3>
                <div class="code-box">
def mark_overlapping_ivls(self, read: vcy.Read) -> None:
    """Mark intronic features if spanned by exon-intron boundary reads"""

    for n_seg, segment in enumerate(read.segments):
        i = self.iidx
        feature = self.ivls[i]

        while i < self.maxiidx and feature.doesnt_start_after(segment):
            if feature.kind == ord("i"):  # Only check introns

                # Check left boundary (intron end → downstream exon start)
                if feature.end_overlaps_with_part_of(segment):
                    downstream_exon = feature.get_downstream_exon()
                    if downstream_exon.start_overlaps_with_part_of(segment):
                        feature.is_validated = True

                # Check right boundary (upstream exon end → intron start)
                if feature.start_overlaps_with_part_of(segment):
                    upstream_exon = feature.get_upstream_exon()
                    if upstream_exon.end_overlaps_with_part_of(segment):
                        feature.is_validated = True

            i += 1
            feature = self.ivls[i]
                </div>

                <div class="success-box">
                    <strong>✅ Validation Benefits:</strong>
                    <ul>
                        <li><strong>Prevents False Positives:</strong> Eliminates genomic DNA contamination</li>
                        <li><strong>Confirms Active Splicing:</strong> Only counts introns with splicing evidence</li>
                        <li><strong>Improves Accuracy:</strong> Reduces noise in unspliced read counts</li>
                        <li><strong>Quality Control:</strong> Provides metrics on annotation quality</li>
                    </ul>
                </div>
            </div>

            <!-- Section 7: Mapping Records -->
            <div class="section" id="mapping-records">
                <h2>7. Mapping Records Generation</h2>

                <h3>7.1 The find_overlapping_ivls Method</h3>
                <p>This is the primary method for generating detailed read-to-feature mappings:</p>

                <div class="code-box">
def find_overlapping_ivls(self, read: vcy.Read) -> Dict[vcy.TranscriptModel, List[vcy.SegmentMatch]]:
    """Generate mapping records linking reads to transcript models"""

    mapping_record: Dict[vcy.TranscriptModel, List[vcy.SegmentMatch]] = defaultdict(list)

    for seg_n, segment in enumerate(read.segments):
        i = self.iidx
        feature = self.ivls[i]

        while i < self.maxiidx and feature.doesnt_start_after(segment):
            # Check for meaningful overlap (minimum flank size)
            if feature.intersects(segment) and (segment[-1] - segment[0]) > vcy.MIN_FLANK:
                mapping_record[feature.transcript_model].append(
                    vcy.SegmentMatch(segment, feature, read.is_spliced)
                )
            i += 1
            feature = self.ivls[i]

    # Quality control and filtering steps...
    return mapping_record
                </div>

                <h3>7.2 Quality Control Steps</h3>
                <p>The method applies several quality control filters:</p>

                <div class="step-box">
                    <div class="step-number">1</div>
                    <strong>Minimum Flank Filter:</strong> Segments must be longer than MIN_FLANK to avoid spurious matches
                </div>

                <div class="step-box">
                    <div class="step-number">2</div>
                    <strong>Optimal Match Selection:</strong> Remove transcript models with fewer segment matches
                </div>

                <div class="step-box">
                    <div class="step-number">3</div>
                    <strong>Skip Validation:</strong> Ensure splice junctions make biological sense
                </div>

                <div class="code-box">
# Quality control implementation
if len(mapping_record) != 0:
    # Step 1: Remove suboptimal transcript models
    max_n_segments = len(max(mapping_record.values(), key=len))
    for tm, segmatch_list in list(mapping_record.items()):
        if len(segmatch_list) < max_n_segments:
            del mapping_record[tm]

# Step 2: Validate splice junctions
if len(mapping_record) != 0:
    for tm, segmatch_list in list(mapping_record.items()):
        for sm in segmatch_list:
            if not sm.skip_makes_sense:
                del mapping_record[tm]
                break
                </div>

                <h3>7.3 SegmentMatch Objects</h3>
                <p>Each mapping creates a SegmentMatch object containing:</p>

                <div class="info-box">
                    <strong>📊 SegmentMatch Contents:</strong>
                    <ul>
                        <li><strong>segment:</strong> Genomic coordinates of the read segment</li>
                        <li><strong>feature:</strong> Reference to the overlapping Feature object</li>
                        <li><strong>is_spliced:</strong> Whether the read contains splice junctions</li>
                        <li><strong>skip_makes_sense:</strong> Whether splice patterns are biologically valid</li>
                    </ul>
                </div>
            </div>

            <!-- Section 8: Performance -->
            <div class="section" id="performance">
                <h2>8. Performance and Optimization</h2>

                <h3>8.1 Computational Complexity</h3>

                <div class="svg-container">
                    <svg width="1000" height="400" viewBox="0 0 1000 400">
                        <!-- Background -->
                        <rect width="1000" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="500" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">FeatureIndex Performance Analysis</text>

                        <!-- Naive approach -->
                        <rect x="50" y="60" width="200" height="120" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="5"/>
                        <text x="150" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="#721c24">Naive Approach</text>
                        <text x="70" y="110" font-size="11" fill="#666">• Check every read vs every feature</text>
                        <text x="70" y="125" font-size="11" fill="#666">• Time: O(n × m)</text>
                        <text x="70" y="140" font-size="11" fill="#666">• Space: O(1)</text>
                        <text x="70" y="155" font-size="11" fill="#666">• No optimization</text>

                        <!-- FeatureIndex approach -->
                        <rect x="300" y="60" width="200" height="120" fill="#d4edda" stroke="#28a745" stroke-width="2" rx="5"/>
                        <text x="400" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="#155724">FeatureIndex</text>
                        <text x="320" y="110" font-size="11" fill="#666">• Sorted features + smart scanning</text>
                        <text x="320" y="125" font-size="11" fill="#666">• Time: O(n × log m)</text>
                        <text x="320" y="140" font-size="11" fill="#666">• Space: O(m)</text>
                        <text x="320" y="155" font-size="11" fill="#666">• State preservation</text>

                        <!-- Ideal approach -->
                        <rect x="550" y="60" width="200" height="120" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
                        <text x="650" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="#1976d2">Interval Tree</text>
                        <text x="570" y="110" font-size="11" fill="#666">• Specialized data structure</text>
                        <text x="570" y="125" font-size="11" fill="#666">• Time: O(log m + k)</text>
                        <text x="570" y="140" font-size="11" fill="#666">• Space: O(m)</text>
                        <text x="570" y="155" font-size="11" fill="#666">• Complex implementation</text>

                        <!-- Performance comparison chart -->
                        <rect x="50" y="220" width="700" height="150" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="400" y="245" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Performance Comparison</text>

                        <!-- Y-axis -->
                        <line x1="100" y1="260" x2="100" y2="350" stroke="#333" stroke-width="2"/>
                        <text x="80" y="305" text-anchor="middle" font-size="10" fill="#666" transform="rotate(-90 80 305)">Time (log scale)</text>

                        <!-- X-axis -->
                        <line x1="100" y1="350" x2="700" y2="350" stroke="#333" stroke-width="2"/>
                        <text x="400" y="370" text-anchor="middle" font-size="10" fill="#666">Number of Features (m)</text>

                        <!-- Performance curves -->
                        <path d="M 150 320 Q 300 300 450 280 Q 550 260 650 240" stroke="#dc3545" stroke-width="3" fill="none"/>
                        <text x="500" y="270" font-size="10" fill="#dc3545">O(n×m) - Naive</text>

                        <path d="M 150 340 Q 300 330 450 325 Q 550 320 650 315" stroke="#28a745" stroke-width="3" fill="none"/>
                        <text x="500" y="310" font-size="10" fill="#28a745">O(n×log m) - FeatureIndex</text>

                        <path d="M 150 345 Q 300 342 450 340 Q 550 338 650 335" stroke="#1976d2" stroke-width="3" fill="none"/>
                        <text x="500" y="330" font-size="10" fill="#1976d2">O(log m + k) - Interval Tree</text>

                        <!-- Optimization strategies -->
                        <rect x="800" y="60" width="150" height="300" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="875" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">Optimizations</text>

                        <text x="820" y="110" font-size="10" fill="#666" font-weight="bold">1. Sorting:</text>
                        <text x="820" y="125" font-size="9" fill="#666">Features sorted by position</text>

                        <text x="820" y="150" font-size="10" fill="#666" font-weight="bold">2. State Preservation:</text>
                        <text x="820" y="165" font-size="9" fill="#666">iidx tracks current position</text>

                        <text x="820" y="190" font-size="10" fill="#666" font-weight="bold">3. Early Termination:</text>
                        <text x="820" y="205" font-size="9" fill="#666">Stop when no more overlaps</text>

                        <text x="820" y="230" font-size="10" fill="#666" font-weight="bold">4. Skip Ahead:</text>
                        <text x="820" y="245" font-size="9" fill="#666">Jump past irrelevant features</text>

                        <text x="820" y="270" font-size="10" fill="#666" font-weight="bold">5. Local Search:</text>
                        <text x="820" y="285" font-size="9" fill="#666">Search from current position</text>

                        <text x="820" y="310" font-size="10" fill="#666" font-weight="bold">6. Batch Reset:</text>
                        <text x="820" y="325" font-size="9" fill="#666">Reset between cell batches</text>
                    </svg>
                </div>

                <h3>8.2 Key Optimizations</h3>

                <div class="method-box">
                    <div class="method-name">Sorted Features</div>
                    <p><strong>Benefit:</strong> Enables left-to-right scanning without backtracking</p>
                    <p><strong>Implementation:</strong> Features sorted by start position, then end position</p>
                    <p><strong>Impact:</strong> Reduces search space from O(m) to O(log m) per segment</p>
                </div>

                <div class="method-box">
                    <div class="method-name">State Preservation</div>
                    <p><strong>Benefit:</strong> Avoids re-scanning features for consecutive reads</p>
                    <p><strong>Implementation:</strong> iidx maintains current position in feature list</p>
                    <p><strong>Impact:</strong> Amortized performance improvement for sorted reads</p>
                </div>

                <div class="method-box">
                    <div class="method-name">Early Termination</div>
                    <p><strong>Benefit:</strong> Stops searching when no more overlaps are possible</p>
                    <p><strong>Implementation:</strong> Check if feature.start > segment.end</p>
                    <p><strong>Impact:</strong> Reduces average case complexity significantly</p>
                </div>

                <h3>8.3 Memory Usage</h3>
                <div class="code-box">
# Memory footprint analysis:
#
# For a typical human genome annotation:
# - ~200,000 exons
# - ~200,000 introns
# - ~50,000 genes
# - Total: ~450,000 features per chromosome
#
# Memory per feature: ~100-200 bytes
# Total memory: ~90-180 MB per chromosome
# Full genome: ~3-6 GB for all chromosomes
#
# This is acceptable for modern systems and enables
# fast in-memory lookups throughout the analysis.
                </div>

                <h3>8.4 Performance Recommendations</h3>
                <div class="success-box">
                    <strong>✅ Best Practices:</strong>
                    <ul>
                        <li><strong>Pre-sort Reads:</strong> Sort BAM files by position for better cache performance</li>
                        <li><strong>Batch Processing:</strong> Reset indexes between batches to maintain optimal state</li>
                        <li><strong>Memory Management:</strong> Use sufficient RAM to keep all feature indexes in memory</li>
                        <li><strong>Chromosome Splitting:</strong> Process chromosomes separately to reduce memory pressure</li>
                    </ul>
                </div>
            </div>

            <!-- Summary -->
            <div class="section">
                <h2>Summary</h2>

                <div class="highlight" style="display: block; text-align: center; padding: 1rem; margin: 2rem 0; font-size: 1.1rem;">
                    🎯 <strong>Key Takeaway:</strong> FeatureIndex is a sophisticated genomic search engine that balances simplicity with performance, providing the foundation for accurate and efficient read classification in RNA velocity analysis.
                </div>

                <h3>Class Strengths</h3>
                <div class="success-box">
                    <strong>✅ Design Excellence:</strong>
                    <ul>
                        <li><strong>Efficient Search:</strong> O(n×log m) complexity with smart optimizations</li>
                        <li><strong>Intron Validation:</strong> Prevents false positives through spanning read evidence</li>
                        <li><strong>Quality Control:</strong> Multiple filtering steps ensure accurate mappings</li>
                        <li><strong>Memory Efficient:</strong> Reasonable memory usage for genome-scale data</li>
                        <li><strong>State Management:</strong> Preserves search state for optimal performance</li>
                    </ul>
                </div>

                <h3>Learning Points</h3>
                <div class="info-box">
                    <strong>📚 Key Concepts:</strong>
                    <ul>
                        <li><strong>Genomic Interval Overlap:</strong> Fundamental problem in bioinformatics</li>
                        <li><strong>Sorted Data Structures:</strong> Enable efficient search algorithms</li>
                        <li><strong>State Preservation:</strong> Amortized performance through smart caching</li>
                        <li><strong>Quality Control:</strong> Multiple validation steps ensure data integrity</li>
                        <li><strong>Biological Validation:</strong> Intron validation reflects biological reality</li>
                    </ul>
                </div>

                <h3>Usage in Velocyto Pipeline</h3>
                <div class="warning-box">
                    <strong>🔄 Integration:</strong> FeatureIndex is used throughout the velocyto pipeline:
                    <ul>
                        <li><strong>Markup Phase:</strong> Initial intron validation</li>
                        <li><strong>Counting Phase:</strong> Read classification and mapping</li>
                        <li><strong>Batch Processing:</strong> Efficient processing of large datasets</li>
                        <li><strong>Quality Control:</strong> Validation statistics and filtering</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
