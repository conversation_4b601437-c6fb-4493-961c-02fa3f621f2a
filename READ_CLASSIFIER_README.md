# BAM/GTF Read Classifier for Spliced/Unspliced Identification

This project provides a comprehensive solution for processing BAM and GTF files to identify and tag reads as spliced, unspliced, or ambiguous based on their alignment patterns relative to gene annotations.

## Overview

The ReadClassifier leverages the robust infrastructure from the velocyto.py package to:
- Parse GTF annotation files and build efficient feature indexes
- Process BAM files read-by-read
- Classify each read based on its overlap with exons and introns
- Output detailed tagging information for downstream analysis

## Features

- **Comprehensive Classification**: Identifies spliced, unspliced, and ambiguous reads
- **Flexible Input**: Works with any BAM file (with or without cell barcodes/UMIs)
- **Stranded/Unstranded**: Supports both stranded and unstranded analysis
- **Efficient Processing**: Uses velocyto's optimized feature indexing for fast overlap detection
- **Detailed Output**: Provides comprehensive tagging and classification details
- **Batch Processing**: Can process multiple BAM files efficiently

## Installation

### Prerequisites

1. **Python 3.6+**
2. **velocyto.py**: `pip install velocyto`
3. **pysam**: `pip install pysam`
4. **numpy**: `pip install numpy`

### Setup

1. Clone or download the classifier files:
   ```bash
   # Download the main files
   # read_classifier.py
   # example_usage.py
   # test_classifier.py
   ```

2. Test the installation:
   ```bash
   python test_classifier.py
   ```

## Quick Start

### Command Line Usage

```bash
# Basic usage
python read_classifier.py input.bam annotations.gtf -o output.tsv

# With options
python read_classifier.py input.bam annotations.gtf \
    --output results.tsv \
    --max-reads 10000 \
    --unstranded \
    --verbose
```

### Python API Usage

```python
from read_classifier import ReadClassifier

# Initialize classifier
classifier = ReadClassifier(
    gtf_file="annotations.gtf",
    stranded=True,
    verbose=True
)

# Process BAM file
results = classifier.process_bam_file(
    bam_file="input.bam",
    output_file="output.tsv",
    max_reads=None  # Process all reads
)

# Print summary
classifier.print_summary(results)
```

## Classification Logic

The classifier uses the following rules to categorize reads:

### Spliced Reads
- Reads that span multiple exons (detected via CIGAR 'N' operations) AND map only to exonic sequences
- Unspliced reads that map only to exonic sequences

### Unspliced Reads  
- Reads that map to intronic sequences
- Reads that span exon-intron boundaries

### Ambiguous Reads
- Reads that map to multiple genes
- Reads with no gene mappings
- Reads with unclear classification patterns

## Output Format

The output TSV file contains the following columns:

| Column | Description |
|--------|-------------|
| read_name | Read identifier from BAM file |
| cell_barcode | Cell barcode (CB tag) or read name if not available |
| umi | UMI sequence (UB tag) or "DUMMY_UMI" if not available |
| chromosome | Chromosome name |
| strand | Strand (+ or -) |
| start | Read start position |
| end | Read end position |
| is_spliced | Whether read spans multiple segments (boolean) |
| classification | spliced, unspliced, or ambiguous |
| gene_id | Gene identifier or multi_gene/no_gene |
| details | Detailed classification information |

## Examples

### Example 1: Basic Processing

```python
from read_classifier import ReadClassifier

classifier = ReadClassifier("annotations.gtf")
results = classifier.process_bam_file("sample.bam", "output.tsv")
classifier.print_summary(results)
```

### Example 2: Unstranded Analysis

```python
classifier = ReadClassifier("annotations.gtf", stranded=False)
results = classifier.process_bam_file("sample.bam")
```

### Example 3: Custom Analysis

```python
classifier = ReadClassifier("annotations.gtf")
results = classifier.process_bam_file("sample.bam")

# Analyze results
gene_counts = {}
for result in results:
    gene_id = result['gene_id']
    if gene_id not in ['multi_gene', 'no_gene']:
        if gene_id not in gene_counts:
            gene_counts[gene_id] = {'spliced': 0, 'unspliced': 0}
        gene_counts[gene_id][result['classification']] += 1

# Calculate splicing ratios
for gene_id, counts in gene_counts.items():
    total = counts['spliced'] + counts['unspliced']
    if total > 0:
        ratio = counts['spliced'] / total
        print(f"{gene_id}: {ratio:.2f}")
```

## Performance Considerations

- **Memory Usage**: The classifier loads all annotations into memory for fast access
- **Processing Speed**: ~10,000-50,000 reads per minute depending on annotation complexity
- **Large Files**: Use `max_reads` parameter for testing with large BAM files
- **Batch Processing**: Process multiple files with the same classifier instance to reuse annotations

## Troubleshooting

### Common Issues

1. **Import Errors**: Make sure velocyto and pysam are installed
2. **GTF Format**: Ensure GTF file follows standard format with gene_id attributes
3. **BAM Index**: Some operations may require indexed BAM files
4. **Memory**: Large annotation files may require significant RAM

### Testing

Run the test suite to verify installation:

```bash
python test_classifier.py
```

## Advanced Usage

See `example_usage.py` for comprehensive examples including:
- Batch processing multiple files
- Custom analysis workflows
- Unstranded data processing
- Performance optimization tips

## Comparison with velocyto

This classifier differs from velocyto in several ways:

| Feature | ReadClassifier | velocyto |
|---------|----------------|----------|
| **Focus** | Read-level classification | Molecule-level counting |
| **Output** | Individual read tags | Count matrices |
| **Complexity** | Simplified logic | Complex UMI/barcode handling |
| **Use Case** | Read analysis | RNA velocity analysis |
| **Performance** | Faster for read tagging | Optimized for counting |

## License

This code builds upon the velocyto.py package and follows similar licensing terms.

## Contributing

Feel free to submit issues and enhancement requests!
