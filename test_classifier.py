#!/usr/bin/env python3
"""
Test script for the ReadClassifier to verify installation and basic functionality.

This script tests the ReadClassifier without requiring actual BAM/GTF files.

Author: AI Assistant
Date: 2025-08-03
"""

import sys
import os

def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")
    
    try:
        import velocyto as vcy
        print("✓ velocyto imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import velocyto: {e}")
        return False
    
    try:
        import pysam
        print("✓ pysam imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import pysam: {e}")
        return False
    
    try:
        import numpy as np
        print("✓ numpy imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import numpy: {e}")
        return False
    
    try:
        from read_classifier import ReadClassifier
        print("✓ ReadClassifier imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import ReadClassifier: {e}")
        return False
    
    return True


def test_velocyto_components():
    """Test that velocyto components are accessible."""
    print("\nTesting velocyto components...")
    
    try:
        import velocyto as vcy
        
        # Test Read class
        read = vcy.Read(
            bc="TEST_BC",
            umi="TEST_UMI", 
            chrom="chr1",
            strand="+",
            pos=1000,
            segments=[(1000, 1100)],
            clip5=0,
            clip3=0,
            ref_skipped=False
        )
        print("✓ vcy.Read class works")
        
        # Test FeatureIndex
        feature_index = vcy.FeatureIndex([])
        print("✓ vcy.FeatureIndex class works")
        
        # Test logic classes
        logic = vcy.logic.Permissive10X()
        print("✓ vcy.logic.Permissive10X class works")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing velocyto components: {e}")
        return False


def test_classifier_initialization():
    """Test ReadClassifier initialization with a dummy GTF."""
    print("\nTesting ReadClassifier initialization...")
    
    # Create a minimal dummy GTF file for testing
    dummy_gtf = "test_dummy.gtf"
    gtf_content = """chr1	test	gene	1000	2000	.	+	.	gene_id "TEST_GENE"; gene_name "TEST_GENE";
chr1	test	transcript	1000	2000	.	+	.	gene_id "TEST_GENE"; transcript_id "TEST_TRANSCRIPT";
chr1	test	exon	1000	1200	.	+	.	gene_id "TEST_GENE"; transcript_id "TEST_TRANSCRIPT";
chr1	test	exon	1800	2000	.	+	.	gene_id "TEST_GENE"; transcript_id "TEST_TRANSCRIPT";
"""
    
    try:
        # Write dummy GTF
        with open(dummy_gtf, 'w') as f:
            f.write(gtf_content)
        
        # Test classifier initialization
        from read_classifier import ReadClassifier
        
        classifier = ReadClassifier(
            gtf_file=dummy_gtf,
            stranded=True,
            verbose=False
        )
        
        print("✓ ReadClassifier initialized successfully")
        print(f"  - Loaded {len(classifier.genes)} genes")
        print(f"  - Built {len(classifier.feature_indexes)} feature indexes")
        
        # Clean up
        os.remove(dummy_gtf)
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing ReadClassifier: {e}")
        # Clean up on error
        if os.path.exists(dummy_gtf):
            os.remove(dummy_gtf)
        return False


def test_read_conversion():
    """Test alignment to read conversion."""
    print("\nTesting read conversion...")
    
    try:
        from read_classifier import ReadClassifier
        import pysam
        
        # Create a minimal dummy GTF
        dummy_gtf = "test_dummy2.gtf"
        with open(dummy_gtf, 'w') as f:
            f.write('chr1\ttest\tgene\t1000\t2000\t.\t+\t.\tgene_id "TEST_GENE";\n')
        
        classifier = ReadClassifier(dummy_gtf, verbose=False)
        
        # Create a mock alignment object
        class MockAlignment:
            def __init__(self):
                self.is_unmapped = False
                self.query_name = "test_read"
                self.reference_name = "chr1"
                self.reference_start = 1000
                self.is_reverse = False
                self.cigartuples = [(0, 100)]  # 100M
                
            def has_tag(self, tag):
                return False
                
            def get_tag(self, tag):
                return None
        
        mock_alignment = MockAlignment()
        read = classifier._alignment_to_read(mock_alignment)
        
        if read:
            print("✓ Alignment to Read conversion works")
            print(f"  - Read: {read.chrom}:{read.start}-{read.end} ({read.strand})")
        else:
            print("✗ Failed to convert alignment to read")
            return False
        
        # Clean up
        os.remove(dummy_gtf)
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing read conversion: {e}")
        if os.path.exists("test_dummy2.gtf"):
            os.remove("test_dummy2.gtf")
        return False


def main():
    """Run all tests."""
    print("ReadClassifier Test Suite")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_velocyto_components,
        test_classifier_initialization,
        test_read_conversion
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! ReadClassifier is ready to use.")
        print("\nNext steps:")
        print("1. Run: python read_classifier.py your_file.bam your_annotations.gtf")
        print("2. Or see example_usage.py for more detailed examples")
    else:
        print("✗ Some tests failed. Please check your installation.")
        print("\nTroubleshooting:")
        print("1. Make sure velocyto is installed: pip install velocyto")
        print("2. Make sure pysam is installed: pip install pysam")
        print("3. Check that you're in the correct directory")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
