<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Velocyto Class Relationships</title>
    
    <!-- MathJax 3 for LaTeX rendering -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.8rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 1rem 0 0 0;
            font-size: 1.3rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
            border-radius: 0 8px 8px 0;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.8rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        
        .section h3 {
            color: #34495e;
            margin-top: 2rem;
            font-size: 1.4rem;
        }
        
        .code-box {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            font-size: 0.9rem;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .svg-container {
            text-align: center;
            margin: 2rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-x: auto;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 2px solid #17a2b8;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .success-box {
            background: #d4edda;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .class-box {
            background: white;
            border: 2px solid #3498db;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            position: relative;
        }
        
        .class-name {
            background: #3498db;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            position: absolute;
            top: -15px;
            left: 20px;
        }
        
        .inheritance-box {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .composition-box {
            background: #fff3e0;
            border: 2px solid #ff9800;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .aggregation-box {
            background: #f3e5f5;
            border: 2px solid #9c27b0;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .toc {
            background: #34495e;
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .toc h3 {
            margin-top: 0;
            color: #ecf0f1;
        }
        
        .toc ul {
            list-style: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 0.5rem 0;
            padding-left: 1rem;
        }
        
        .toc a {
            color: #3498db;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .toc a:hover {
            color: #5dade2;
        }
        
        .relationship-legend {
            display: flex;
            justify-content: space-around;
            margin: 1rem 0;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 0.5rem;
        }
        
        .legend-line {
            width: 30px;
            height: 3px;
            margin-right: 0.5rem;
        }
        
        .inheritance { background: #4caf50; }
        .composition { background: #ff9800; }
        .aggregation { background: #9c27b0; }
        .association { background: #2196f3; }
        .dependency { background: #f44336; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧬 Velocyto Class Relationships</h1>
            <p>Complete Architecture and Interaction Patterns</p>
        </div>
        
        <div class="content">
            <!-- Table of Contents -->
            <div class="toc">
                <h3>📚 Table of Contents</h3>
                <ul>
                    <li><a href="#overview">1. Architecture Overview</a></li>
                    <li><a href="#core-classes">2. Core Data Classes</a></li>
                    <li><a href="#index-classes">3. Index and Search Classes</a></li>
                    <li><a href="#logic-hierarchy">4. Logic Class Hierarchy</a></li>
                    <li><a href="#counter-orchestration">5. Counter and Orchestration</a></li>
                    <li><a href="#relationships">6. Class Relationships</a></li>
                    <li><a href="#data-flow">7. Data Flow Patterns</a></li>
                    <li><a href="#interaction-examples">8. Interaction Examples</a></li>
                </ul>
            </div>

            <!-- Section 1: Overview -->
            <div class="section" id="overview">
                <h2>1. Architecture Overview</h2>
                
                <div class="info-box">
                    <strong>🎯 Velocyto Architecture:</strong> Velocyto follows a layered architecture with clear separation of concerns:
                    <ul>
                        <li><strong>Data Layer:</strong> Core data structures (Read, Feature, TranscriptModel)</li>
                        <li><strong>Index Layer:</strong> Efficient search structures (FeatureIndex, TranscriptsIndex)</li>
                        <li><strong>Logic Layer:</strong> Classification algorithms (Logic hierarchy)</li>
                        <li><strong>Orchestration Layer:</strong> Main processing engine (ExInCounter)</li>
                    </ul>
                </div>
                
                <h3>1.1 Relationship Types</h3>
                <div class="relationship-legend">
                    <div class="legend-item">
                        <div class="legend-line inheritance"></div>
                        <span>Inheritance (IS-A)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-line composition"></div>
                        <span>Composition (PART-OF)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-line aggregation"></div>
                        <span>Aggregation (HAS-A)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-line association"></div>
                        <span>Association (USES)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-line dependency"></div>
                        <span>Dependency (DEPENDS-ON)</span>
                    </div>
                </div>
            </div>

            <!-- Main Class Diagram -->
            <div class="section">
                <h2>Complete Class Relationship Diagram</h2>

                <div class="svg-container">
                    <svg width="1600" height="1200" viewBox="0 0 1600 1200">
                        <!-- Background -->
                        <rect width="1600" height="1200" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="800" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">Velocyto Class Architecture</text>

                        <!-- Data Layer -->
                        <rect x="50" y="60" width="1500" height="200" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="10"/>
                        <text x="800" y="85" text-anchor="middle" font-size="16" font-weight="bold" fill="#1976d2">Data Layer - Core Data Structures</text>

                        <!-- Read Class -->
                        <rect x="100" y="110" width="180" height="120" fill="white" stroke="#2196f3" stroke-width="2" rx="5"/>
                        <text x="190" y="130" text-anchor="middle" font-size="12" font-weight="bold" fill="#1976d2">Read</text>
                        <text x="110" y="150" font-size="9" fill="#666">bc: str</text>
                        <text x="110" y="165" font-size="9" fill="#666">umi: str</text>
                        <text x="110" y="180" font-size="9" fill="#666">chrom: str</text>
                        <text x="110" y="195" font-size="9" fill="#666">segments: List</text>
                        <text x="110" y="210" font-size="9" fill="#666">is_spliced: bool</text>

                        <!-- Feature Class -->
                        <rect x="320" y="110" width="180" height="120" fill="white" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="410" y="130" text-anchor="middle" font-size="12" font-weight="bold" fill="#2e7d32">Feature</text>
                        <text x="330" y="150" font-size="9" fill="#666">start: int</text>
                        <text x="330" y="165" font-size="9" fill="#666">end: int</text>
                        <text x="330" y="180" font-size="9" fill="#666">kind: int</text>
                        <text x="330" y="195" font-size="9" fill="#666">is_validated: bool</text>
                        <text x="330" y="210" font-size="9" fill="#666">transcript_model: ref</text>

                        <!-- TranscriptModel Class -->
                        <rect x="540" y="110" width="180" height="120" fill="white" stroke="#ff9800" stroke-width="2" rx="5"/>
                        <text x="630" y="130" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">TranscriptModel</text>
                        <text x="550" y="150" font-size="9" fill="#666">geneid: str</text>
                        <text x="550" y="165" font-size="9" fill="#666">genename: str</text>
                        <text x="550" y="180" font-size="9" fill="#666">chromstrand: str</text>
                        <text x="550" y="195" font-size="9" fill="#666">list_features: List</text>

                        <!-- SegmentMatch Class -->
                        <rect x="760" y="110" width="180" height="120" fill="white" stroke="#9c27b0" stroke-width="2" rx="5"/>
                        <text x="850" y="130" text-anchor="middle" font-size="12" font-weight="bold" fill="#7b1fa2">SegmentMatch</text>
                        <text x="770" y="150" font-size="9" fill="#666">segment: Tuple</text>
                        <text x="770" y="165" font-size="9" fill="#666">feature: Feature</text>
                        <text x="770" y="180" font-size="9" fill="#666">is_spliced: bool</text>
                        <text x="770" y="195" font-size="9" fill="#666">maps_to_intron: bool</text>
                        <text x="770" y="210" font-size="9" fill="#666">maps_to_exon: bool</text>

                        <!-- Molitem Class -->
                        <rect x="980" y="110" width="200" height="120" fill="white" stroke="#e91e63" stroke-width="2" rx="5"/>
                        <text x="1080" y="130" text-anchor="middle" font-size="12" font-weight="bold" fill="#c2185b">Molitem</text>
                        <text x="990" y="150" font-size="9" fill="#666">mappings_record:</text>
                        <text x="990" y="165" font-size="9" fill="#666">Dict[TranscriptModel,</text>
                        <text x="990" y="180" font-size="9" fill="#666">     List[SegmentMatch]]</text>

                        <!-- Index Layer -->
                        <rect x="50" y="300" width="1500" height="200" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="10"/>
                        <text x="800" y="325" text-anchor="middle" font-size="16" font-weight="bold" fill="#f57c00">Index Layer - Search Structures</text>

                        <!-- FeatureIndex Class -->
                        <rect x="200" y="350" width="200" height="120" fill="white" stroke="#ff9800" stroke-width="2" rx="5"/>
                        <text x="300" y="370" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">FeatureIndex</text>
                        <text x="210" y="390" font-size="9" fill="#666">ivls: List[Feature]</text>
                        <text x="210" y="405" font-size="9" fill="#666">iidx: int</text>
                        <text x="210" y="420" font-size="9" fill="#666">maxiidx: int</text>
                        <text x="210" y="440" font-size="9" fill="#666">find_overlapping_ivls()</text>
                        <text x="210" y="455" font-size="9" fill="#666">mark_overlapping_ivls()</text>

                        <!-- TranscriptsIndex Class -->
                        <rect x="450" y="350" width="200" height="120" fill="white" stroke="#ff9800" stroke-width="2" rx="5"/>
                        <text x="550" y="370" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">TranscriptsIndex</text>
                        <text x="460" y="390" font-size="9" fill="#666">transcript_models:</text>
                        <text x="460" y="405" font-size="9" fill="#666">List[TranscriptModel]</text>
                        <text x="460" y="420" font-size="9" fill="#666">tidx: int</text>
                        <text x="460" y="440" font-size="9" fill="#666">find_overlapping_</text>
                        <text x="460" y="455" font-size="9" fill="#666">transcript_models()</text>

                        <!-- Logic Layer -->
                        <rect x="50" y="540" width="1500" height="250" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10"/>
                        <text x="800" y="565" text-anchor="middle" font-size="16" font-weight="bold" fill="#4caf50">Logic Layer - Classification Algorithms</text>

                        <!-- Logic Base Class -->
                        <rect x="300" y="590" width="180" height="100" fill="white" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="390" y="610" text-anchor="middle" font-size="12" font-weight="bold" fill="#2e7d32">Logic (ABC)</text>
                        <text x="310" y="630" font-size="9" fill="#666">name: str</text>
                        <text x="310" y="645" font-size="9" fill="#666">layers: List[str]</text>
                        <text x="310" y="660" font-size="9" fill="#666">count() [abstract]</text>
                        <text x="310" y="675" font-size="9" fill="#666">stranded: bool</text>

                        <!-- Permissive10X Class -->
                        <rect x="550" y="590" width="180" height="100" fill="white" stroke="#66bb6a" stroke-width="2" rx="5"/>
                        <text x="640" y="610" text-anchor="middle" font-size="12" font-weight="bold" fill="#2e7d32">Permissive10X</text>
                        <text x="560" y="630" font-size="9" fill="#666">layers: ["spliced",</text>
                        <text x="560" y="645" font-size="9" fill="#666">        "unspliced",</text>
                        <text x="560" y="660" font-size="9" fill="#666">        "ambiguous"]</text>
                        <text x="560" y="675" font-size="9" fill="#666">count() [implemented]</text>

                        <!-- Other Logic Classes -->
                        <rect x="800" y="590" width="150" height="100" fill="white" stroke="#66bb6a" stroke-width="2" rx="5"/>
                        <text x="875" y="610" text-anchor="middle" font-size="11" font-weight="bold" fill="#2e7d32">Strict10X</text>
                        <text x="810" y="630" font-size="9" fill="#666">More stringent</text>
                        <text x="810" y="645" font-size="9" fill="#666">classification</text>

                        <rect x="1000" y="590" width="150" height="100" fill="white" stroke="#66bb6a" stroke-width="2" rx="5"/>
                        <text x="1075" y="610" text-anchor="middle" font-size="11" font-weight="bold" fill="#2e7d32">SmartSeq2</text>
                        <text x="1010" y="630" font-size="9" fill="#666">Full-length</text>
                        <text x="1010" y="645" font-size="9" fill="#666">transcript logic</text>

                        <!-- Orchestration Layer -->
                        <rect x="50" y="830" width="1500" height="150" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="10"/>
                        <text x="800" y="855" text-anchor="middle" font-size="16" font-weight="bold" fill="#9c27b0">Orchestration Layer - Main Processing Engine</text>

                        <!-- ExInCounter Class -->
                        <rect x="600" y="880" width="400" height="80" fill="white" stroke="#9c27b0" stroke-width="2" rx="5"/>
                        <text x="800" y="900" text-anchor="middle" font-size="12" font-weight="bold" fill="#7b1fa2">ExInCounter</text>
                        <text x="610" y="920" font-size="9" fill="#666">logic: Logic, feature_indexes: Dict[str, FeatureIndex]</text>
                        <text x="610" y="935" font-size="9" fill="#666">count(), count_cell_batch(), iter_alignments()</text>
                        <text x="610" y="950" font-size="9" fill="#666">Orchestrates the entire counting pipeline</text>

                        <!-- Relationships - Composition arrows -->
                        <path d="M 500 170 L 540 170" stroke="#ff9800" stroke-width="3" marker-end="url(#composition)"/>
                        <text x="520" y="165" text-anchor="middle" font-size="8" fill="#ff9800">contains</text>

                        <path d="M 720 170 L 760 170" stroke="#ff9800" stroke-width="3" marker-end="url(#composition)"/>
                        <text x="740" y="165" text-anchor="middle" font-size="8" fill="#ff9800">contains</text>

                        <path d="M 940 170 L 980 170" stroke="#ff9800" stroke-width="3" marker-end="url(#composition)"/>
                        <text x="960" y="165" text-anchor="middle" font-size="8" fill="#ff9800">contains</text>

                        <!-- Index relationships -->
                        <path d="M 300 350 L 410 250" stroke="#2196f3" stroke-width="2" marker-end="url(#association)"/>
                        <text x="340" y="290" text-anchor="middle" font-size="8" fill="#2196f3">indexes</text>

                        <path d="M 550 350 L 630 250" stroke="#2196f3" stroke-width="2" marker-end="url(#association)"/>
                        <text x="580" y="290" text-anchor="middle" font-size="8" fill="#2196f3">indexes</text>

                        <!-- Inheritance arrows -->
                        <path d="M 640 590 L 390 590" stroke="#4caf50" stroke-width="3" marker-end="url(#inheritance)"/>
                        <path d="M 875 590 L 390 590" stroke="#4caf50" stroke-width="3" marker-end="url(#inheritance)"/>
                        <path d="M 1075 590 L 390 590" stroke="#4caf50" stroke-width="3" marker-end="url(#inheritance)"/>

                        <!-- ExInCounter relationships -->
                        <path d="M 700 880 L 640 700" stroke="#9c27b0" stroke-width="3" marker-end="url(#aggregation)"/>
                        <text x="650" y="790" text-anchor="middle" font-size="8" fill="#9c27b0">uses</text>

                        <path d="M 750 880 L 300 480" stroke="#9c27b0" stroke-width="2" marker-end="url(#aggregation)"/>
                        <text x="500" y="680" text-anchor="middle" font-size="8" fill="#9c27b0">uses</text>

                        <!-- Arrow markers -->
                        <defs>
                            <marker id="inheritance" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
                                <polygon points="0 0, 12 4, 0 8" fill="none" stroke="#4caf50" stroke-width="2"/>
                            </marker>
                            <marker id="composition" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
                                <polygon points="0 0, 12 4, 0 8, 6 4" fill="#ff9800"/>
                            </marker>
                            <marker id="aggregation" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
                                <polygon points="0 0, 12 4, 0 8, 6 4" fill="none" stroke="#9c27b0" stroke-width="2"/>
                            </marker>
                            <marker id="association" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#2196f3"/>
                            </marker>
                        </defs>
                    </svg>
                </div>
            </div>

            <!-- Section 2: Core Data Classes -->
            <div class="section" id="core-classes">
                <h2>2. Core Data Classes</h2>

                <h3>2.1 Read Class</h3>
                <div class="class-box">
                    <div class="class-name">Read</div>
                    <p><strong>Purpose:</strong> Represents a single sequencing read with its metadata and genomic segments</p>

                    <div class="code-box">
class Read:
    """Container for sequencing read information"""
    bc: str           # Cell barcode (10X: 16bp)
    umi: str          # UMI sequence (10X: 12bp)
    chrom: str        # Chromosome name
    strand: str       # Strand (+ or -)
    segments: List[Tuple[int, int]]  # Genomic coordinates
    is_spliced: bool  # Whether read has splice junctions
                    </div>

                    <div class="info-box">
                        <strong>📊 Key Features:</strong>
                        <ul>
                            <li><strong>Cell Identity:</strong> Barcode links read to specific cell</li>
                            <li><strong>Molecule Identity:</strong> UMI enables molecule counting</li>
                            <li><strong>Genomic Position:</strong> Segments define where read maps</li>
                            <li><strong>Splice Detection:</strong> is_spliced indicates mature mRNA</li>
                        </ul>
                    </div>
                </div>

                <h3>2.2 Feature Class</h3>
                <div class="class-box">
                    <div class="class-name">Feature</div>
                    <p><strong>Purpose:</strong> Represents genomic features (exons, introns) with validation state</p>

                    <div class="code-box">
class Feature:
    """Genomic feature with overlap detection methods"""
    start: int                    # Start coordinate
    end: int                     # End coordinate
    kind: int                    # Feature type (101='e', 105='i', 109='m')
    exin_no: int                # Exon/intron number
    is_validated: bool          # Whether intron is validated
    transcript_model: TranscriptModel  # Parent transcript

    # Overlap detection methods
    def contains(self, segment) -> bool
    def intersects(self, segment) -> bool
    def start_overlaps_with_part_of(self, segment) -> bool
    def end_overlaps_with_part_of(self, segment) -> bool
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ Validation Critical:</strong> The <code>is_validated</code> flag is crucial for introns. Only introns with spanning reads are marked as validated, preventing false positives from genomic DNA contamination.
                    </div>
                </div>

                <h3>2.3 TranscriptModel Class</h3>
                <div class="class-box">
                    <div class="class-name">TranscriptModel</div>
                    <p><strong>Purpose:</strong> Represents a transcript isoform with its constituent features</p>

                    <div class="code-box">
class TranscriptModel:
    """Transcript isoform with features"""
    geneid: str              # Gene identifier
    genename: str           # Gene symbol
    chromstrand: str        # Chromosome and strand
    list_features: List[Feature]  # Ordered exons and introns

    # Navigation methods
    def get_downstream_exon(self, intron: Feature) -> Feature
    def get_upstream_exon(self, intron: Feature) -> Feature
                    </div>

                    <div class="composition-box">
                        <strong>🔗 Composition Relationship:</strong> TranscriptModel contains multiple Feature objects. Each Feature has a back-reference to its parent TranscriptModel, creating a bidirectional relationship.
                    </div>
                </div>

                <h3>2.4 SegmentMatch Class</h3>
                <div class="class-box">
                    <div class="class-name">SegmentMatch</div>
                    <p><strong>Purpose:</strong> Links read segments to genomic features with overlap metadata</p>

                    <div class="code-box">
class SegmentMatch:
    """Links read segments to features"""
    segment: Tuple[int, int]  # Read segment coordinates
    feature: Feature          # Overlapping feature
    is_spliced: bool         # Whether segment has splice junction

    @property
    def maps_to_intron(self) -> bool:
        return self.feature.kind == 105  # ord("i")

    @property
    def maps_to_exon(self) -> bool:
        return self.feature.kind == 101  # ord("e")

    @property
    def skip_makes_sense(self) -> bool:
        """Validates splice junction positions"""
                    </div>

                    <div class="success-box">
                        <strong>✅ Quality Control:</strong> SegmentMatch includes validation logic to ensure splice junctions align with feature boundaries, preventing spurious mappings.
                    </div>
                </div>

                <h3>2.5 Molitem Class</h3>
                <div class="class-box">
                    <div class="class-name">Molitem</div>
                    <p><strong>Purpose:</strong> Aggregates all segment matches for a single molecule (UMI)</p>

                    <div class="code-box">
class Molitem:
    """Molecule-level mapping record"""
    mappings_record: Dict[TranscriptModel, List[SegmentMatch]]

    # The mappings_record structure:
    # Key: TranscriptModel that the read maps to
    # Value: List of SegmentMatch objects for that transcript

    # This enables:
    # - Multi-transcript compatibility analysis
    # - Optimal transcript selection
    # - Ambiguity detection
                    </div>

                    <div class="aggregation-box">
                        <strong>🔄 Aggregation Pattern:</strong> Molitem aggregates SegmentMatch objects grouped by TranscriptModel. This structure enables the Logic classes to analyze mapping patterns and make classification decisions.
                    </div>
                </div>
            </div>

            <!-- Section 3: Index Classes -->
            <div class="section" id="index-classes">
                <h2>3. Index and Search Classes</h2>

                <h3>3.1 FeatureIndex Class</h3>
                <div class="class-box">
                    <div class="class-name">FeatureIndex</div>
                    <p><strong>Purpose:</strong> Efficient search engine for finding feature overlaps with read segments</p>

                    <div class="code-box">
class FeatureIndex:
    """Search help class for genomic interval overlaps"""
    ivls: List[Feature]  # Sorted list of features
    iidx: int           # Current search position
    maxiidx: int        # Maximum valid index

    # Core search methods
    def find_overlapping_ivls(self, read: Read) -> Dict[TranscriptModel, List[SegmentMatch]]
    def mark_overlapping_ivls(self, read: Read) -> None  # Intron validation
    def has_ivls_enclosing(self, read: Read) -> bool     # Masking check
    def reset(self) -> None  # Reset search position
                    </div>

                    <div class="info-box">
                        <strong>🔍 Search Optimization:</strong>
                        <ul>
                            <li><strong>Sorted Features:</strong> O(log n) search complexity</li>
                            <li><strong>State Preservation:</strong> iidx maintains position between reads</li>
                            <li><strong>Early Termination:</strong> Stops when no more overlaps possible</li>
                            <li><strong>Intron Validation:</strong> Marks introns with spanning evidence</li>
                        </ul>
                    </div>
                </div>

                <h3>3.2 TranscriptsIndex Class</h3>
                <div class="class-box">
                    <div class="class-name">TranscriptsIndex</div>
                    <p><strong>Purpose:</strong> Manages transcript models for efficient lookup and overlap detection</p>

                    <div class="code-box">
class TranscriptsIndex:
    """Index for transcript model lookup"""
    transcript_models: List[TranscriptModel]  # Sorted transcript models
    tidx: int                                # Current transcript position

    def find_overlapping_transcript_models(self, read: Read) -> List[TranscriptModel]
    def reset(self) -> None
                    </div>

                    <div class="composition-box">
                        <strong>🏗️ Index Architecture:</strong> Both FeatureIndex and TranscriptsIndex follow the same pattern:
                        <ul>
                            <li><strong>Sorted Collections:</strong> Enable efficient binary-like search</li>
                            <li><strong>Position Tracking:</strong> Maintain state for sequential access</li>
                            <li><strong>Reset Capability:</strong> Allow reuse across batches</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Section 4: Logic Hierarchy -->
            <div class="section" id="logic-hierarchy">
                <h2>4. Logic Class Hierarchy</h2>

                <h3>4.1 Abstract Base Class: Logic</h3>
                <div class="class-box">
                    <div class="class-name">Logic (ABC)</div>
                    <p><strong>Purpose:</strong> Defines the interface for read classification algorithms</p>

                    <div class="code-box">
from abc import ABC, abstractmethod

class Logic(ABC):
    """Abstract base class for classification logic"""
    name: str

    @property
    @abstractmethod
    def layers(self) -> List[str]:
        """Define output layers (e.g., spliced, unspliced, ambiguous)"""
        pass

    @property
    @abstractmethod
    def stranded(self) -> bool:
        """Whether the protocol is strand-specific"""
        pass

    @abstractmethod
    def count(self, molitem: Molitem, cell_bcidx: int,
              dict_layers_columns: Dict[str, np.ndarray],
              geneid2ix: Dict[str, int]) -> int:
        """Core classification logic - must be implemented by subclasses"""
        pass
                    </div>

                    <div class="inheritance-box">
                        <strong>🧬 Template Method Pattern:</strong> The Logic class defines the interface while concrete subclasses implement specific classification algorithms for different sequencing protocols.
                    </div>
                </div>

                <h3>4.2 Concrete Implementation: Permissive10X</h3>
                <div class="class-box">
                    <div class="class-name">Permissive10X</div>
                    <p><strong>Purpose:</strong> Most permissive classification logic for 10X Genomics data</p>

                    <div class="code-box">
class Permissive10X(Logic):
    """Permissive logic for 10X Genomics chemistry"""

    @property
    def layers(self) -> List[str]:
        return ["spliced", "unspliced", "ambiguous"]

    @property
    def stranded(self) -> bool:
        return True

    def count(self, molitem: Molitem, cell_bcidx: int,
              dict_layers_columns: Dict[str, np.ndarray],
              geneid2ix: Dict[str, int]) -> int:
        """
        Classification rules:
        - Exon-only reads → SPLICED
        - Intronic reads (validated or not) → UNSPLICED
        - Exon-intron spanning reads → UNSPLICED
        - Multi-gene mappings → Not counted
        - Ambiguous cases → AMBIGUOUS
        """
        # Complex decision tree implementation...
                    </div>

                    <div class="success-box">
                        <strong>✅ Permissive Strategy:</strong> Counts most reads to maximize information extraction, only excluding clear multi-gene mappings and unmappable reads.
                    </div>
                </div>

                <h3>4.3 Other Logic Implementations</h3>

                <div class="class-box">
                    <div class="class-name">Strict10X</div>
                    <p><strong>Purpose:</strong> More stringent classification for higher precision</p>
                    <p><strong>Differences:</strong> Only counts validated introns, stricter ambiguity handling</p>
                </div>

                <div class="class-box">
                    <div class="class-name">SmartSeq2</div>
                    <p><strong>Purpose:</strong> Logic optimized for full-length transcript protocols</p>
                    <p><strong>Differences:</strong> Different handling of splice junctions and transcript coverage</p>
                </div>
            </div>

            <!-- Section 5: Counter Orchestration -->
            <div class="section" id="counter-orchestration">
                <h2>5. Counter and Orchestration</h2>

                <h3>5.1 ExInCounter Class</h3>
                <div class="class-box">
                    <div class="class-name">ExInCounter</div>
                    <p><strong>Purpose:</strong> Main orchestration class that coordinates the entire counting pipeline</p>

                    <div class="code-box">
class ExInCounter:
    """Main counting engine"""
    logic: Logic                                    # Classification algorithm
    feature_indexes: Dict[str, FeatureIndex]      # Per-chromosome indexes
    mask_indexes: Dict[str, FeatureIndex]         # Repeat masking indexes
    annotations_by_chrm_strand: Dict              # Raw annotations

    # Core pipeline methods
    def count(self, bamfile: str, multimap: bool, cell_batch_size: int) -> Tuple[Dict, List[str]]
    def count_cell_batch(self) -> Tuple[Dict[str, np.ndarray], List[str]]
    def iter_alignments(self, bamfile: str) -> Iterator[Read]

    # State management
    cell_batch: Set[str]           # Current batch of cell barcodes
    reads_to_count: List[Read]     # Accumulated reads for current batch
                    </div>

                    <div class="aggregation-box">
                        <strong>🎭 Orchestration Pattern:</strong> ExInCounter aggregates and coordinates multiple components:
                        <ul>
                            <li><strong>Logic:</strong> Pluggable classification algorithm</li>
                            <li><strong>Indexes:</strong> Efficient search structures</li>
                            <li><strong>Batch Management:</strong> Memory-efficient processing</li>
                            <li><strong>I/O Handling:</strong> BAM file reading and parsing</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Section 6: Relationships -->
            <div class="section" id="relationships">
                <h2>6. Class Relationships Summary</h2>

                <h3>6.1 Inheritance Relationships</h3>
                <div class="inheritance-box">
                    <strong>🧬 IS-A Relationships:</strong>
                    <ul>
                        <li><strong>Permissive10X IS-A Logic:</strong> Implements abstract classification interface</li>
                        <li><strong>Strict10X IS-A Logic:</strong> Alternative classification strategy</li>
                        <li><strong>SmartSeq2 IS-A Logic:</strong> Protocol-specific implementation</li>
                    </ul>
                </div>

                <h3>6.2 Composition Relationships</h3>
                <div class="composition-box">
                    <strong>🏗️ PART-OF Relationships:</strong>
                    <ul>
                        <li><strong>Feature PART-OF TranscriptModel:</strong> Features belong to specific transcripts</li>
                        <li><strong>SegmentMatch PART-OF Molitem:</strong> Segment matches grouped by molecule</li>
                        <li><strong>Read segments PART-OF Read:</strong> Genomic coordinates within read</li>
                    </ul>
                </div>

                <h3>6.3 Aggregation Relationships</h3>
                <div class="aggregation-box">
                    <strong>🔄 HAS-A Relationships:</strong>
                    <ul>
                        <li><strong>ExInCounter HAS-A Logic:</strong> Pluggable classification algorithm</li>
                        <li><strong>ExInCounter HAS-A FeatureIndex:</strong> Multiple indexes per chromosome</li>
                        <li><strong>FeatureIndex HAS-A List[Feature]:</strong> Collection of genomic features</li>
                        <li><strong>Molitem HAS-A Dict[TranscriptModel, List[SegmentMatch]]:</strong> Mapping records</li>
                    </ul>
                </div>

                <h3>6.4 Association Relationships</h3>
                <div class="info-box">
                    <strong>🔗 USES Relationships:</strong>
                    <ul>
                        <li><strong>FeatureIndex USES Feature:</strong> For overlap detection and validation</li>
                        <li><strong>Logic USES Molitem:</strong> For classification decisions</li>
                        <li><strong>SegmentMatch USES Feature:</strong> References overlapping feature</li>
                        <li><strong>ExInCounter USES Read:</strong> Processes individual reads</li>
                    </ul>
                </div>
            </div>

            <!-- Section 7: Data Flow -->
            <div class="section" id="data-flow">
                <h2>7. Data Flow Patterns</h2>

                <h3>7.1 Processing Pipeline</h3>
                <div class="svg-container">
                    <svg width="1400" height="600" viewBox="0 0 1400 600">
                        <!-- Background -->
                        <rect width="1400" height="600" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="700" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Data Flow Through Class Hierarchy</text>

                        <!-- Input -->
                        <rect x="50" y="80" width="120" height="60" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
                        <text x="110" y="105" text-anchor="middle" font-size="11" font-weight="bold" fill="#1976d2">BAM File</text>
                        <text x="110" y="125" text-anchor="middle" font-size="9" fill="#666">Raw reads</text>

                        <!-- Read Creation -->
                        <rect x="220" y="80" width="120" height="60" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="280" y="105" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">Read Objects</text>
                        <text x="280" y="125" text-anchor="middle" font-size="9" fill="#666">bc, umi, segments</text>

                        <!-- Feature Lookup -->
                        <rect x="390" y="80" width="120" height="60" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="450" y="105" text-anchor="middle" font-size="11" font-weight="bold" fill="#4caf50">FeatureIndex</text>
                        <text x="450" y="125" text-anchor="middle" font-size="9" fill="#666">find_overlapping_ivls</text>

                        <!-- SegmentMatch Creation -->
                        <rect x="560" y="80" width="120" height="60" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="5"/>
                        <text x="620" y="105" text-anchor="middle" font-size="11" font-weight="bold" fill="#9c27b0">SegmentMatch</text>
                        <text x="620" y="125" text-anchor="middle" font-size="9" fill="#666">segment + feature</text>

                        <!-- Molitem Aggregation -->
                        <rect x="730" y="80" width="120" height="60" fill="#ffebee" stroke="#e91e63" stroke-width="2" rx="5"/>
                        <text x="790" y="105" text-anchor="middle" font-size="11" font-weight="bold" fill="#e91e63">Molitem</text>
                        <text x="790" y="125" text-anchor="middle" font-size="9" fill="#666">mappings_record</text>

                        <!-- Logic Classification -->
                        <rect x="900" y="80" width="120" height="60" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="960" y="105" text-anchor="middle" font-size="11" font-weight="bold" fill="#4caf50">Logic.count()</text>
                        <text x="960" y="125" text-anchor="middle" font-size="9" fill="#666">classification</text>

                        <!-- Output -->
                        <rect x="1070" y="80" width="120" height="60" fill="#e1f5fe" stroke="#00bcd4" stroke-width="2" rx="5"/>
                        <text x="1130" y="105" text-anchor="middle" font-size="11" font-weight="bold" fill="#00838f">Count Matrices</text>
                        <text x="1130" y="125" text-anchor="middle" font-size="9" fill="#666">S, U, A</text>

                        <!-- Flow arrows -->
                        <path d="M 170 110 L 220 110" stroke="#666" stroke-width="3" marker-end="url(#flow-arrow)"/>
                        <path d="M 340 110 L 390 110" stroke="#666" stroke-width="3" marker-end="url(#flow-arrow)"/>
                        <path d="M 510 110 L 560 110" stroke="#666" stroke-width="3" marker-end="url(#flow-arrow)"/>
                        <path d="M 680 110 L 730 110" stroke="#666" stroke-width="3" marker-end="url(#flow-arrow)"/>
                        <path d="M 850 110 L 900 110" stroke="#666" stroke-width="3" marker-end="url(#flow-arrow)"/>
                        <path d="M 1020 110 L 1070 110" stroke="#666" stroke-width="3" marker-end="url(#flow-arrow)"/>

                        <!-- Detailed steps -->
                        <text x="700" y="200" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Detailed Processing Steps</text>

                        <!-- Step boxes -->
                        <rect x="50" y="230" width="250" height="100" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="175" y="250" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">1. Read Parsing</text>
                        <text x="70" y="270" font-size="10" fill="#666">• Extract cell barcode</text>
                        <text x="70" y="285" font-size="10" fill="#666">• Extract UMI</text>
                        <text x="70" y="300" font-size="10" fill="#666">• Parse CIGAR string</text>
                        <text x="70" y="315" font-size="10" fill="#666">• Create Read object</text>

                        <rect x="350" y="230" width="250" height="100" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="475" y="250" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">2. Feature Mapping</text>
                        <text x="370" y="270" font-size="10" fill="#666">• Search sorted features</text>
                        <text x="370" y="285" font-size="10" fill="#666">• Detect overlaps</text>
                        <text x="370" y="300" font-size="10" fill="#666">• Validate introns</text>
                        <text x="370" y="315" font-size="10" fill="#666">• Create SegmentMatch</text>

                        <rect x="650" y="230" width="250" height="100" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="775" y="250" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">3. Molecule Assembly</text>
                        <text x="670" y="270" font-size="10" fill="#666">• Group by UMI</text>
                        <text x="670" y="285" font-size="10" fill="#666">• Aggregate segments</text>
                        <text x="670" y="300" font-size="10" fill="#666">• Build mappings_record</text>
                        <text x="670" y="315" font-size="10" fill="#666">• Create Molitem</text>

                        <rect x="950" y="230" width="250" height="100" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="1075" y="250" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">4. Classification</text>
                        <text x="970" y="270" font-size="10" fill="#666">• Apply Logic rules</text>
                        <text x="970" y="285" font-size="10" fill="#666">• Check gene mapping</text>
                        <text x="970" y="300" font-size="10" fill="#666">• Analyze feature types</text>
                        <text x="970" y="315" font-size="10" fill="#666">• Increment counters</text>

                        <!-- Batch processing -->
                        <rect x="200" y="380" width="800" height="150" fill="#f8f9fa" stroke="#666" stroke-width="2" rx="10"/>
                        <text x="600" y="405" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Batch Processing (ExInCounter)</text>

                        <text x="220" y="430" font-size="11" fill="#666" font-weight="bold">Memory Management:</text>
                        <text x="220" y="450" font-size="10" fill="#666">• Process 100 cells at a time</text>
                        <text x="220" y="465" font-size="10" fill="#666">• Reset indexes between batches</text>
                        <text x="220" y="480" font-size="10" fill="#666">• Accumulate count matrices</text>
                        <text x="220" y="495" font-size="10" fill="#666">• Filter low-count cells</text>

                        <text x="550" y="430" font-size="11" fill="#666" font-weight="bold">State Preservation:</text>
                        <text x="550" y="450" font-size="10" fill="#666">• Maintain feature index positions</text>
                        <text x="550" y="465" font-size="10" fill="#666">• Track validated introns</text>
                        <text x="550" y="480" font-size="10" fill="#666">• Preserve cell barcode order</text>
                        <text x="550" y="495" font-size="10" fill="#666">• Log processing statistics</text>

                        <!-- Arrow marker -->
                        <defs>
                            <marker id="flow-arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>
            </div>

            <!-- Summary -->
            <div class="section">
                <h2>Summary</h2>

                <div class="highlight" style="display: block; text-align: center; padding: 1rem; margin: 2rem 0; font-size: 1.1rem;">
                    🎯 <strong>Key Takeaway:</strong> Velocyto's class architecture demonstrates excellent software design principles with clear separation of concerns, pluggable algorithms, and efficient data structures that enable scalable RNA velocity analysis.
                </div>

                <h3>Design Principles</h3>
                <div class="success-box">
                    <strong>✅ Architectural Excellence:</strong>
                    <ul>
                        <li><strong>Separation of Concerns:</strong> Data, indexing, logic, and orchestration layers</li>
                        <li><strong>Strategy Pattern:</strong> Pluggable Logic implementations for different protocols</li>
                        <li><strong>Template Method:</strong> Abstract Logic class defines interface</li>
                        <li><strong>Composition over Inheritance:</strong> Flexible aggregation relationships</li>
                        <li><strong>Single Responsibility:</strong> Each class has a focused purpose</li>
                    </ul>
                </div>

                <h3>Key Relationships</h3>
                <div class="info-box">
                    <strong>📊 Relationship Summary:</strong>
                    <ul>
                        <li><strong>Data Flow:</strong> Read → SegmentMatch → Molitem → Classification → Counts</li>
                        <li><strong>Search Optimization:</strong> FeatureIndex enables O(log n) genomic lookups</li>
                        <li><strong>Pluggable Logic:</strong> Different protocols use different Logic implementations</li>
                        <li><strong>Batch Processing:</strong> ExInCounter orchestrates memory-efficient processing</li>
                        <li><strong>Quality Control:</strong> Multiple validation steps ensure data integrity</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
