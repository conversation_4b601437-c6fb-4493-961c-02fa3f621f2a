<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   width="1254.9822"
   height="1003.7711"
   viewBox="0 0 1254.9822 1003.7711"
   version="1.1"
   id="svg76"
   sodipodi:docname="data_type.svg"
   inkscape:version="1.4.2 (f4327f4, 2025-05-13)"
   inkscape:export-filename="data_type.png"
   inkscape:export-xdpi="300"
   inkscape:export-ydpi="300"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg">
  <sodipodi:namedview
     id="namedview76"
     pagecolor="#ffffff"
     bordercolor="#000000"
     borderopacity="0.25"
     inkscape:showpageshadow="2"
     inkscape:pageopacity="0.0"
     inkscape:pagecheckerboard="0"
     inkscape:deskcolor="#d1d1d1"
     inkscape:zoom="0.65583333"
     inkscape:cx="739.51715"
     inkscape:cy="441.42313"
     inkscape:window-width="1920"
     inkscape:window-height="1001"
     inkscape:window-x="-9"
     inkscape:window-y="-9"
     inkscape:window-maximized="1"
     inkscape:current-layer="svg76"
     inkscape:export-bgcolor="#ffffffff" />
  <!-- Background -->
  <!-- Title -->
  <text
     x="585.44202"
     y="41.103252"
     text-anchor="middle"
     font-size="20px"
     font-weight="bold"
     fill="#2c3e50"
     id="text1">Velocyto Class Architecture</text>
  <!-- Data Layer -->
  <rect
     x="50"
     y="60"
     width="1144"
     height="200"
     fill="#e3f2fd"
     stroke="#1976d2"
     stroke-width="1.74662"
     rx="7.6266665"
     id="rect2" />
  <text
     x="620"
     y="85"
     text-anchor="middle"
     font-size="16px"
     font-weight="bold"
     fill="#1976d2"
     id="text2">Data Layer - Core Data Structures</text>
  <!-- Read Class -->
  <rect
     x="100"
     y="110"
     width="180"
     height="120"
     fill="#ffffff"
     stroke="#2196f3"
     stroke-width="2"
     rx="5"
     id="rect3" />
  <text
     x="190"
     y="130"
     text-anchor="middle"
     font-size="12px"
     font-weight="bold"
     fill="#1976d2"
     id="text3">Read</text>
  <text
     x="110"
     y="150"
     font-size="9px"
     fill="#666666"
     id="text4">bc: str</text>
  <text
     x="110"
     y="165"
     font-size="9px"
     fill="#666666"
     id="text5">umi: str</text>
  <text
     x="110"
     y="180"
     font-size="9px"
     fill="#666666"
     id="text6">chrom: str</text>
  <text
     x="110"
     y="195"
     font-size="9px"
     fill="#666666"
     id="text7">segments: List</text>
  <text
     x="110"
     y="210"
     font-size="9px"
     fill="#666666"
     id="text8">is_spliced: bool</text>
  <!-- Feature Class -->
  <rect
     x="320"
     y="110"
     width="180"
     height="120"
     fill="#ffffff"
     stroke="#4caf50"
     stroke-width="2"
     rx="5"
     id="rect8" />
  <text
     x="410"
     y="130"
     text-anchor="middle"
     font-size="12px"
     font-weight="bold"
     fill="#2e7d32"
     id="text9">Feature</text>
  <text
     x="330"
     y="150"
     font-size="9px"
     fill="#666666"
     id="text10">start: int</text>
  <text
     x="330"
     y="165"
     font-size="9px"
     fill="#666666"
     id="text11">end: int</text>
  <text
     x="330"
     y="180"
     font-size="9px"
     fill="#666666"
     id="text12">kind: int</text>
  <text
     x="330"
     y="195"
     font-size="9px"
     fill="#666666"
     id="text13">is_validated: bool</text>
  <text
     x="330"
     y="210"
     font-size="9px"
     fill="#666666"
     id="text14">transcript_model: ref</text>
  <!-- TranscriptModel Class -->
  <rect
     x="540"
     y="110"
     width="180"
     height="120"
     fill="#ffffff"
     stroke="#ff9800"
     stroke-width="2"
     rx="5"
     id="rect14" />
  <text
     x="630"
     y="130"
     text-anchor="middle"
     font-size="12px"
     font-weight="bold"
     fill="#f57c00"
     id="text15">TranscriptModel</text>
  <text
     x="550"
     y="150"
     font-size="9px"
     fill="#666666"
     id="text16">geneid: str</text>
  <text
     x="550"
     y="165"
     font-size="9px"
     fill="#666666"
     id="text17">genename: str</text>
  <text
     x="550"
     y="180"
     font-size="9px"
     fill="#666666"
     id="text18">chromstrand: str</text>
  <text
     x="550"
     y="195"
     font-size="9px"
     fill="#666666"
     id="text19">list_features: List</text>
  <!-- SegmentMatch Class -->
  <rect
     x="760"
     y="110"
     width="180"
     height="120"
     fill="#ffffff"
     stroke="#9c27b0"
     stroke-width="2"
     rx="5"
     id="rect19" />
  <text
     x="850"
     y="130"
     text-anchor="middle"
     font-size="12px"
     font-weight="bold"
     fill="#7b1fa2"
     id="text20">SegmentMatch</text>
  <text
     x="770"
     y="150"
     font-size="9px"
     fill="#666666"
     id="text21">segment: Tuple</text>
  <text
     x="770"
     y="165"
     font-size="9px"
     fill="#666666"
     id="text22">feature: Feature</text>
  <text
     x="770"
     y="180"
     font-size="9px"
     fill="#666666"
     id="text23">is_spliced: bool</text>
  <text
     x="770"
     y="195"
     font-size="9px"
     fill="#666666"
     id="text24">maps_to_intron: bool</text>
  <text
     x="770"
     y="210"
     font-size="9px"
     fill="#666666"
     id="text25">maps_to_exon: bool</text>
  <!-- Molitem Class -->
  <rect
     x="980"
     y="110"
     width="200"
     height="120"
     fill="#ffffff"
     stroke="#e91e63"
     stroke-width="2"
     rx="5"
     id="rect25" />
  <text
     x="1080"
     y="130"
     text-anchor="middle"
     font-size="12px"
     font-weight="bold"
     fill="#c2185b"
     id="text26">Molitem</text>
  <text
     x="990"
     y="150"
     font-size="9px"
     fill="#666666"
     id="text27">mappings_record:</text>
  <text
     x="990"
     y="165"
     font-size="9px"
     fill="#666666"
     id="text28">Dict[TranscriptModel,</text>
  <text
     x="990"
     y="180"
     font-size="9px"
     fill="#666666"
     id="text29">List[SegmentMatch]]</text>
  <!-- Index Layer -->
  <rect
     x="50.025246"
     y="300.02524"
     width="1145.5149"
     height="199.94951"
     fill="#fff3e0"
     stroke="#f57c00"
     stroke-width="1.74756"
     rx="7.636766"
     id="rect29" />
  <text
     x="620"
     y="325"
     text-anchor="middle"
     font-size="16px"
     font-weight="bold"
     fill="#f57c00"
     id="text30">Index Layer - Search Structures</text>
  <!-- FeatureIndex Class -->
  <rect
     x="499.21609"
     y="353.39426"
     width="200"
     height="120"
     fill="#ffffff"
     stroke="#ff9800"
     stroke-width="2"
     rx="5"
     id="rect30" />
  <text
     x="599.21606"
     y="373.39426"
     text-anchor="middle"
     font-size="12px"
     font-weight="bold"
     fill="#f57c00"
     id="text31">FeatureIndex</text>
  <text
     x="509.21609"
     y="393.39426"
     font-size="9px"
     fill="#666666"
     id="text32">ivls: List[Feature]</text>
  <text
     x="509.21609"
     y="408.39426"
     font-size="9px"
     fill="#666666"
     id="text33">iidx: int</text>
  <text
     x="509.21609"
     y="423.39426"
     font-size="9px"
     fill="#666666"
     id="text34">maxiidx: int</text>
  <text
     x="509.21609"
     y="443.39426"
     font-size="9px"
     fill="#666666"
     id="text35">find_overlapping_ivls()</text>
  <text
     x="509.21609"
     y="458.39426"
     font-size="9px"
     fill="#666666"
     id="text36">mark_overlapping_ivls()</text>
  <!-- TranscriptsIndex Class -->
  <!-- Logic Layer -->
  <rect
     x="49.989964"
     y="539.98999"
     width="1155.1724"
     height="250.02007"
     fill="#e8f5e8"
     stroke="#4caf50"
     stroke-width="1.75519"
     rx="7.7011495"
     id="rect42" />
  <text
     x="620"
     y="565"
     text-anchor="middle"
     font-size="16px"
     font-weight="bold"
     fill="#4caf50"
     id="text43">Logic Layer - Classification Algorithms</text>
  <!-- Logic Base Class -->
  <rect
     x="300.16223"
     y="590.16223"
     width="209.20073"
     height="116.22263"
     fill="#ffffff"
     stroke="#4caf50"
     stroke-width="2.32445"
     rx="5.8111315"
     id="rect43" />
  <text
     x="404.7626"
     y="613.40674"
     text-anchor="middle"
     font-size="13.9467px"
     font-weight="bold"
     fill="#2e7d32"
     id="text44"
     style="stroke-width:1.16223">Logic (ABC)</text>
  <text
     x="311.78448"
     y="636.65131"
     font-size="10.46px"
     fill="#666666"
     id="text45"
     style="stroke-width:1.16223">name: str</text>
  <text
     x="311.78448"
     y="654.08466"
     font-size="10.46px"
     fill="#666666"
     id="text46"
     style="stroke-width:1.16223">layers: List[str]</text>
  <text
     x="311.78448"
     y="671.51807"
     font-size="10.46px"
     fill="#666666"
     id="text47"
     style="stroke-width:1.16223">count() [abstract]</text>
  <text
     x="311.78448"
     y="688.95148"
     font-size="10.46px"
     fill="#666666"
     id="text48"
     style="stroke-width:1.16223">stranded: bool</text>
  <!-- Permissive10X Class -->
  <rect
     x="590.71881"
     y="590.16223"
     width="209.20073"
     height="116.22263"
     fill="#ffffff"
     stroke="#66bb6a"
     stroke-width="2.32445"
     rx="5.8111315"
     id="rect48" />
  <text
     x="695.31915"
     y="613.40674"
     text-anchor="middle"
     font-size="13.9467px"
     font-weight="bold"
     fill="#2e7d32"
     id="text49"
     style="stroke-width:1.16223">Permissive10X</text>
  <text
     x="602.34106"
     y="636.65131"
     font-size="10.46px"
     fill="#666666"
     id="text50"
     style="stroke-width:1.16223">layers: [&quot;spliced&quot;,</text>
  <text
     x="602.34106"
     y="654.08466"
     font-size="10.46px"
     fill="#666666"
     id="text51"
     style="stroke-width:1.16223">&quot;unspliced&quot;,</text>
  <text
     x="602.34106"
     y="671.51807"
     font-size="10.46px"
     fill="#666666"
     id="text52"
     style="stroke-width:1.16223">&quot;ambiguous&quot;]</text>
  <text
     x="602.34106"
     y="688.95148"
     font-size="10.46px"
     fill="#666666"
     id="text53"
     style="stroke-width:1.16223">count() [implemented]</text>
  <!-- Other Logic Classes -->
  <!-- Orchestration Layer -->
  <rect
     x="49.877369"
     y="829.87738"
     width="1152.7778"
     height="150.24527"
     fill="#f3e5f5"
     stroke="#9c27b0"
     stroke-width="1.75474"
     rx="7.685185"
     id="rect59" />
  <text
     x="620"
     y="855"
     text-anchor="middle"
     font-size="16px"
     font-weight="bold"
     fill="#9c27b0"
     id="text60">Orchestration Layer - Main Processing Engine</text>
  <!-- ExInCounter Class -->
  <rect
     x="407.87885"
     y="879.15033"
     width="502.96133"
     height="100.59227"
     fill="#ffffff"
     stroke="#9c27b0"
     stroke-width="2.51481"
     rx="6.2870169"
     id="rect60" />
  <text
     x="659.3595"
     y="904.2984"
     text-anchor="middle"
     font-size="15.0888px"
     font-weight="bold"
     fill="#7b1fa2"
     id="text61"
     style="stroke-width:1.2574">ExInCounter</text>
  <text
     x="420.45288"
     y="929.44647"
     font-size="11.3166px"
     fill="#666666"
     id="text62"
     style="stroke-width:1.2574">logic: Logic, feature_indexes: Dict[str, FeatureIndex]</text>
  <text
     x="420.45288"
     y="948.30756"
     font-size="11.3166px"
     fill="#666666"
     id="text63"
     style="stroke-width:1.2574">count(), count_cell_batch(), iter_alignments()</text>
  <text
     x="420.45288"
     y="967.16858"
     font-size="11.3166px"
     fill="#666666"
     id="text64"
     style="stroke-width:1.2574">Orchestrates the entire counting pipeline</text>
  <!-- Relationships - Composition arrows -->
  <path
     d="m 500,170 h 40"
     stroke="#ff9800"
     stroke-width="3"
     marker-end="url(#composition)"
     id="path64" />
  <text
     x="520"
     y="165"
     text-anchor="middle"
     font-size="8px"
     fill="#ff9800"
     id="text65">contains</text>
  <path
     d="m 720,170 h 40"
     stroke="#ff9800"
     stroke-width="3"
     marker-end="url(#composition)"
     id="path65" />
  <text
     x="740"
     y="165"
     text-anchor="middle"
     font-size="8px"
     fill="#ff9800"
     id="text66">contains</text>
  <path
     d="m 940,170 h 40"
     stroke="#ff9800"
     stroke-width="3"
     marker-end="url(#composition)"
     id="path66" />
  <text
     x="960"
     y="165"
     text-anchor="middle"
     font-size="8px"
     fill="#ff9800"
     id="text67">contains</text>
  <!-- Index relationships -->
  <path
     d="M 487.38972,351.83813 412.64757,249.82836"
     stroke="#2196f3"
     stroke-width="1.66509"
     marker-end="url(#association)"
     id="path67" />
  <text
     x="443.67294"
     y="292.17249"
     text-anchor="middle"
     font-size="8px"
     fill="#2196f3"
     id="text68">indexes</text>
  <!-- Inheritance arrows -->
  <path
     d="M 590.71879,648.27354 H 509.36295"
     stroke="#4caf50"
     stroke-width="1.84498"
     marker-end="url(#inheritance)"
     id="path69" />
  <!-- ExInCounter relationships -->
  <path
     d="M 700.06484,880.34667 654.4252,717.93528"
     stroke="#9c27b0"
     stroke-width="2.48536"
     marker-end="url(#aggregation)"
     id="path72" />
  <text
     x="650"
     y="790"
     text-anchor="middle"
     font-size="8px"
     fill="#9c27b0"
     id="text72">uses</text>
  <path
     d="M 407.62142,918.89296 C 253.40252,886.04965 134.70242,797.21934 173.8551,459.57133"
     stroke="#9c27b0"
     stroke-width="1.5447"
     marker-end="url(#aggregation)"
     id="path73"
     sodipodi:nodetypes="cc"
     style="fill:none" />
  <text
     x="532.60748"
     y="694.76257"
     text-anchor="middle"
     font-size="9.29781px"
     fill="#9c27b0"
     id="text73"
     style="stroke-width:1.16223">uses</text>
  <!-- Arrow markers -->
  <defs
     id="defs76">
    <marker
       id="inheritance"
       markerWidth="12"
       markerHeight="8"
       refX="11"
       refY="4"
       orient="auto">
      <polygon
         points="12,4 0,8 0,0 "
         fill="none"
         stroke="#4caf50"
         stroke-width="2"
         id="polygon73" />
    </marker>
    <marker
       id="composition"
       markerWidth="12"
       markerHeight="8"
       refX="11"
       refY="4"
       orient="auto">
      <polygon
         points="12,4 0,8 6,4 0,0 "
         fill="#ff9800"
         id="polygon74" />
    </marker>
    <marker
       id="aggregation"
       markerWidth="12"
       markerHeight="8"
       refX="11"
       refY="4"
       orient="auto">
      <polygon
         points="12,4 0,8 6,4 0,0 "
         fill="none"
         stroke="#9c27b0"
         stroke-width="2"
         id="polygon75" />
    </marker>
    <marker
       id="association"
       markerWidth="8"
       markerHeight="6"
       refX="7"
       refY="3"
       orient="auto">
      <polygon
         points="8,3 0,6 0,0 "
         fill="#2196f3"
         id="polygon76" />
    </marker>
  </defs>
</svg>
