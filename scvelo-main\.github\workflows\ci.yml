name: CI

on:
    push:
        branches: [main]
    pull_request:
        branches: [main]

jobs:
    # Skip CI if commit message contains `[ci skip]` in the subject
    init:
        runs-on: ubuntu-latest
        outputs:
            skip: ${{ steps.ci-skip-step.outputs.ci-skip }}
        steps:
            - uses: actions/checkout@v2
              with:
                  fetch-depth: 0
            - id: ci-skip-step
              uses: mstachniuk/ci-skip@master

    # Check if pre-commit hooks pass and if README.rst can be converted to HTML
    linting:
        needs: init
        if: ${{ needs.init.outputs.skip == 'false' }}
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v2
            - name: Setup Python 3.10
              uses: actions/setup-python@v2
              with:
                  python-version: "3.10"
            - name: Install dependencies
              run: |
                  python -m pip install --upgrade pip
                  pip install pre-commit
            - name: Check pre-commit compatibility
              run: pre-commit run --all-files --show-diff-on-failure

    # Run unit tests
    test:
        needs: linting
        runs-on: ${{ matrix.os }}
        strategy:
            fail-fast: false
            matrix:
                os: [ubuntu-latest]
                python-version: ["3.9", "3.10"]
        steps:
            - uses: actions/checkout@v2
            - name: Set up Python ${{ matrix.python-version }}
              uses: actions/setup-python@v2
              with:
                  python-version: ${{ matrix.python-version }}
            - name: Install dependencies
              run: pip install -e '.[dev]'
            - name: Unit tests
              timeout-minutes: 60
              run: python -m pytest --durations=25 --ignore=tests/datasets/test_datasets.py --hypothesis-profile=ci --cov=scvelo -vv

    test-dataset-downloads:
        needs: test
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v2
            - name: Setup Python 3.10
              uses: actions/setup-python@v2
              with:
                  python-version: "3.10"
            - name: Install dependencies
              run: |
                  pip install -e .
                  pip install hypothesis pytest pytest-cov
            - name: Test dataset downloads
              timeout-minutes: 60
              run: python -m pytest tests/datasets/test_datasets.py -vv
