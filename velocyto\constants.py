MATCH_INSIDE = 1
MATCH_OVER5END = 2
MATCH_OVER3END = 4

MIN_FLANK = 5
PATCH_INDELS = 3
SPLIC_INACUR = 6
MIN_POLYT = 8
MAX_USHORT = 2**16 - 1

LOOM_NUMERIC_DTYPE = "uint16"

EXTENSION5_LEN = 0  # basepairs to extend 5' ends of models
EXTENSION3_LEN = 0  # basepairs to extend 3' ends of models

BINSIZE_BP = 100000  # binsize to look up for other transcripts used during interval determination
LONGEST_INTRON_ALLOWED = 1000000
BAM_COMPRESSION = 7

EXON = 1
ALT_EXON = 2  # From another transcript of same gene
OTHER_EXON = 4  # From an unrelated gene
INTRON = 8
ALT_INTRON = 16
OTHER_INTRON = 32
COMPETING_INTRON = ALT_INTRON | OTHER_INTRON
COMPETING_EXON = ALT_EXON | OTHER_EXON

PLACEHOLDER_UMI_LEN = 12  # the length of the placeholder random umi added if -U is set (complexity is 36 per unit length)

CIGAR = {0: "BAM_CMATCH",
         1: "BAM_CINS",
         2: "BAM_CDEL",
         3: "BAM_CREF_SKIP",
         4: "BAM_CSOFT_CLIP",
         5: "BAM_CHARD_CLIP",
         6: "BAM_CPAD",
         7: "BAM_CEQUAL",
         8: "BAM_CDIFF",
         9: "BAM_CBACK"}  # type: dict  # currently hard coded for speed

GEM_codes = {'SI-3A-A1': ['AAACGGCG', 'CCTACCAT', 'GGCGTTTC', 'TTGTAAGA'],
             'SI-3A-A10': ['ACAGCAAC', 'CGCAATTT', 'GAGTTGCG', 'TTTCGCGA'],
             'SI-3A-A11': ['ACCAGTCC', 'CTTTCCTT', 'GGACAGGG', 'TAGGTAAA'],
             'SI-3A-A12': ['ACTACTGT', 'CGGGAACG', 'GACCTCTC', 'TTATGGAA'],
             'SI-3A-A2': ['AGCCCTTT', 'CAAGTCCA', 'GTGAGAAG', 'TCTTAGGC'],
             'SI-3A-A3': ['AAAGCATA', 'CTGCAGCC', 'GCCTTTAT', 'TGTAGCGG'],
             'SI-3A-A4': ['AGAACGCC', 'CATGGCAG', 'GTCTTTGA', 'TCGCAATT'],
             'SI-3A-A5': ['ATTGGGAA', 'CAGTCTGG', 'GGCATACT', 'TCACACTC'],
             'SI-3A-A6': ['ACGGGACT', 'CTTTCGAC', 'GAACATGA', 'TGCATCTG'],
             'SI-3A-A7': ['AGGTCATA', 'CTCATCAT', 'GCTGAGGG', 'TAACGTCC'],
             'SI-3A-A8': ['ATGATACG', 'CCACAGAA', 'GACTGTTC', 'TGTGCCGT'],
             'SI-3A-A9': ['ACAACTTG', 'CTCCAACA', 'GAGTGCGT', 'TGTGTGAC'],
             'SI-3A-B1': ['AGGCTACC', 'CTAGCTGT', 'GCCAACAA', 'TATTGGTG'],
             'SI-3A-B10': ['ACCATTAA', 'CTGGACGT', 'GAACGGTC', 'TGTTCACG'],
             'SI-3A-B11': ['ATGGTCGC', 'CGACATAG', 'GATTCGCT', 'TCCAGATA'],
             'SI-3A-B12': ['ACGCTTGG', 'CGCTACAT', 'GAAAGACA', 'TTTGCGTC'],
             'SI-3A-B2': ['AAGTTGAT', 'CCCACCCA', 'GGTCGAGC', 'TTAGATTG'],
             'SI-3A-B3': ['ATTGGACG', 'CAGCTTAC', 'GGCAAGGA', 'TCATCCTT'],
             'SI-3A-B4': ['AGGGACTG', 'CCTCTAAC', 'GACAGGCT', 'TTATCTGA'],
             'SI-3A-B5': ['ATCGTACT', 'CATCAGTG', 'GGGACTAC', 'TCATGCGA'],
             'SI-3A-B6': ['AACGCGAA', 'CTATTTGG', 'GCGCACCT', 'TGTAGATC'],
             'SI-3A-B7': ['AGGGATGA', 'CTTCTGTT', 'GAATGCAC', 'TCCACACG'],
             'SI-3A-B8': ['ACGTTCAC', 'CAAGGTCT', 'GTTAAGTG', 'TGCCCAGA'],
             'SI-3A-B9': ['AAGCGTGT', 'CTTGACCG', 'GCCACGTA', 'TGATTAAC'],
             'SI-3A-C1': ['AGACTTTC', 'CCGAGGCA', 'GATGCAGT', 'TTCTACAG'],
             'SI-3A-C10': ['ATCTGATC', 'CGTGCTAA', 'GAGAAGGG', 'TCACTCCT'],
             'SI-3A-C11': ['ACCGAACA', 'CGACTCTT', 'GTTTGTGG', 'TAGACGAC'],
             'SI-3A-C12': ['ATCCGGCA', 'CCGTTATG', 'GGTAATGT', 'TAAGCCAC'],
             'SI-3A-C2': ['AATCACTA', 'CCGAGAAC', 'GTAGTGCG', 'TGCTCTGT'],
             'SI-3A-C3': ['ACGTTACA', 'CGTAGGTT', 'GACGACGG', 'TTACCTAC'],
             'SI-3A-C4': ['ACATTGGC', 'CTTAGTCA', 'GAGCCCAT', 'TGCGAATG'],
             'SI-3A-C5': ['ATGCATTC', 'CACTGACT', 'GGTACGGG', 'TCAGTCAA'],
             'SI-3A-C6': ['ACTCAGAC', 'CGCTCAGG', 'GAGGTTTA', 'TTAAGCCT'],
             'SI-3A-C7': ['ACACCGGG', 'CATAATCC', 'GGCGGAAT', 'TTGTTCTA'],
             'SI-3A-C8': ['AGCTCGAG', 'CAGGAAGA', 'GCACGTTT', 'TTTATCCC'],
             'SI-3A-C9': ['AGATCGGT', 'CATCGTCG', 'GTCATATA', 'TCGGACAC'],
             'SI-3A-D1': ['AGCTGCGT', 'CAACCATC', 'GTGGAGCA', 'TCTATTAG'],
             'SI-3A-D10': ['AGATAACA', 'CTTATTTG', 'GCGGGCAT', 'TACCCGGC'],
             'SI-3A-D11': ['ATATGAGA', 'CACCTCAG', 'GCTACTTC', 'TGGGAGCT'],
             'SI-3A-D12': ['AGAAACGT', 'CACTCAAC', 'GCTGTGTA', 'TTGCGTCG'],
             'SI-3A-D2': ['ACATTCCG', 'CTGCGGTA', 'GACACAAT', 'TGTGATGC'],
             'SI-3A-D3': ['ACTTCACT', 'CGAAGTTG', 'GAGCACGC', 'TTCGTGAA'],
             'SI-3A-D4': ['AAATCGTC', 'CTTCGAAT', 'GCGATCGG', 'TGCGATCA'],
             'SI-3A-D5': ['AGACGGAT', 'CCTTTAGA', 'GTCGACTC', 'TAGACTCG'],
             'SI-3A-D6': ['ATGCCAAA', 'CCTTATCG', 'GAAGTCTT', 'TGCAGGGC'],
             'SI-3A-D7': ['AACTTAGA', 'CCGGATCC', 'GGTCGCAT', 'TTAACGTG'],
             'SI-3A-D8': ['AATCTTTG', 'CTCAAGAC', 'GGATGAGT', 'TCGGCCCA'],
             'SI-3A-D9': ['ACCTACTG', 'CAAGGGAC', 'GGGACACA', 'TTTCTTGT'],
             'SI-3A-E1': ['ACGAAAGC', 'CGCCCGTA', 'GTTTGCCT', 'TAAGTTAG'],
             'SI-3A-E10': ['ATTGTTTC', 'CGCAGGAG', 'GCACCAGT', 'TAGTACCA'],
             'SI-3A-E11': ['ATCGCCAT', 'CATAAAGG', 'GGGTTTCC', 'TCACGGTA'],
             'SI-3A-E12': ['ACGCGGAA', 'CGCTATCC', 'GTTGCATG', 'TAAATCGT'],
             'SI-3A-E2': ['AGGCTGGT', 'CACAACTA', 'GTTGGTCC', 'TCATCAAG'],
             'SI-3A-E3': ['AACAAGTC', 'CGGCTCCA', 'GTATGTAT', 'TCTGCAGG'],
             'SI-3A-E4': ['AGCTGACG', 'CCGGTGTC', 'GTAAACAT', 'TATCCTGA'],
             'SI-3A-E5': ['ATCCAAGG', 'CCGTTGAA', 'GGAAGCTC', 'TATGCTCT'],
             'SI-3A-E6': ['ATTGAAAC', 'CAGCCCGA', 'GCCATTTG', 'TGATGGCT'],
             'SI-3A-E7': ['AAGACGTG', 'CCATGTGT', 'GTTCACAA', 'TGCGTACC'],
             'SI-3A-E8': ['AGCCTATG', 'CTAACGCA', 'GCTTACAT', 'TAGGGTGC'],
             'SI-3A-E9': ['AGTAAGCA', 'CCGGTAAT', 'GTATCTTC', 'TACCGCGG'],
             'SI-3A-F1': ['ATCGCTCC', 'CCGTACAG', 'GATAGGTA', 'TGACTAGT'],
             'SI-3A-F10': ['ATTCGTGC', 'CGCGTGCA', 'GAATACTG', 'TCGACAAT'],
             'SI-3A-F11': ['AGCAGTTA', 'CTTGTACC', 'GAACCCGG', 'TCGTAGAT'],
             'SI-3A-F12': ['AATTGAAC', 'CCAGTGGA', 'GTCCATTG', 'TGGACCCT'],
             'SI-3A-F2': ['ATGGTTAG', 'CATTGATA', 'GCAAACGC', 'TGCCCGCT'],
             'SI-3A-F3': ['AGTCTGTA', 'CAGAATAG', 'GCCTCCGT', 'TTAGGACC'],
             'SI-3A-F4': ['AACGACAC', 'CGTCCTCT', 'GCATGATA', 'TTGATGGG'],
             'SI-3A-F5': ['AACACAGC', 'CGGTTTAG', 'GTACGGCT', 'TCTGACTA'],
             'SI-3A-F6': ['ATGCCGGC', 'CCTAATTA', 'GACTTCCT', 'TGAGGAAG'],
             'SI-3A-F7': ['ACCCGAGA', 'CAAACTTT', 'GGTTAGAC', 'TTGGTCCG'],
             'SI-3A-F8': ['AGTTGGGA', 'CCAGAAAG', 'GTGCCCTC', 'TACATTCT'],
             'SI-3A-F9': ['AGTTAGTT', 'CACGCACG', 'GTACTTAA', 'TCGAGCGC'],
             'SI-3A-G1': ['ATGCGATT', 'CATATGCG', 'GGATACGA', 'TCCGCTAC'],
             'SI-3A-G10': ['ATACTGAG', 'CGGAGACT', 'GATGCCTC', 'TCCTATGA'],
             'SI-3A-G11': ['AGGGCGTT', 'CTATACGC', 'GCTCGTCA', 'TACATAAG'],
             'SI-3A-G12': ['ACCCGCAC', 'CATGCGTA', 'GTGATAGT', 'TGATATCG'],
             'SI-3A-G2': ['ATAACCTA', 'CGGTGAGC', 'GATCTTAT', 'TCCGAGCG'],
             'SI-3A-G3': ['ATGTCCAG', 'CGACGTCA', 'GCTATAGC', 'TACGAGTT'],
             'SI-3A-G4': ['AGCTTCTC', 'CCTGCGGT', 'GTACAACG', 'TAGAGTAA'],
             'SI-3A-G5': ['ATGAAGTA', 'CGCCGAAC', 'GAAGCTCG', 'TCTTTCGT'],
             'SI-3A-G6': ['AGCACTGG', 'CATTACAC', 'GTGCGACA', 'TCAGTGTT'],
             'SI-3A-G7': ['ATTACCGG', 'CAGTAATT', 'GCCGGTAA', 'TGACTGCC'],
             'SI-3A-G8': ['AAGTACTC', 'CTTGGAGA', 'GGAACTCT', 'TCCCTGAG'],
             'SI-3A-G9': ['AGTCTCAG', 'CAATGGCA', 'GCCGAAGT', 'TTGACTTC'],
             'SI-3A-H1': ['AAACTCAT', 'CGGGAGTA', 'GTCACAGG', 'TCTTGTCC'],
             'SI-3A-H10': ['ATTTCAGC', 'CGAGTGAT', 'GACCGCCA', 'TCGAATTG'],
             'SI-3A-H11': ['AGGATCGA', 'CACGATTC', 'GTATCGAG', 'TCTCGACT'],
             'SI-3A-H12': ['ACGAGTAG', 'CAATCCCT', 'GTCCAGGC', 'TGTGTATA'],
             'SI-3A-H2': ['AACGGTCA', 'CCGAACTC', 'GGTCCAAG', 'TTATTGGT'],
             'SI-3A-H3': ['ACACCTAA', 'CGTTTGGG', 'GACAAACC', 'TTGGGCTT'],
             'SI-3A-H4': ['ACTGGAGC', 'CGGTCGTG', 'GAAATCAA', 'TTCCATCT'],
             'SI-3A-H5': ['ATAGTATG', 'CCGCGTCT', 'GGCTCCAC', 'TATAAGGA'],
             'SI-3A-H6': ['AAGCATAA', 'CCCATCGC', 'GGTTGATG', 'TTAGCGCT'],
             'SI-3A-H7': ['AACGGGTG', 'CTAATTCT', 'GCTTCAAC', 'TGGCACGA'],
             'SI-3A-H8': ['AAGAGCGG', 'CTTGTTAT', 'GGCCCATC', 'TCATAGCA'],
             'SI-3A-H9': ['ACCTGCCA', 'CTTCATAC', 'GGAATATG', 'TAGGCGGT'],
             'SI-P03-A1': ['AAACGGCG', 'CCTACCAT', 'GGCGTTTC', 'TTGTAAGA'],
             'SI-P03-A10': ['ACAGCAAC', 'CGCAATTT', 'GAGTTGCG', 'TTTCGCGA'],
             'SI-P03-A11': ['ACCAGTCC', 'CTTTCCTT', 'GGACAGGG', 'TAGGTAAA'],
             'SI-P03-A12': ['ACTACTGT', 'CGGGAACG', 'GACCTCTC', 'TTATGGAA'],
             'SI-P03-A2': ['AGCCCTTT', 'CAAGTCCA', 'GTGAGAAG', 'TCTTAGGC'],
             'SI-P03-A3': ['AAAGCATA', 'CTGCAGCC', 'GCCTTTAT', 'TGTAGCGG'],
             'SI-P03-A4': ['AGAACGCC', 'CATGGCAG', 'GTCTTTGA', 'TCGCAATT'],
             'SI-P03-A5': ['ATTGGGAA', 'CAGTCTGG', 'GGCATACT', 'TCACACTC'],
             'SI-P03-A6': ['ACGGGACT', 'CTTTCGAC', 'GAACATGA', 'TGCATCTG'],
             'SI-P03-A7': ['AGGTCATA', 'CTCATCAT', 'GCTGAGGG', 'TAACGTCC'],
             'SI-P03-A8': ['ATGATACG', 'CCACAGAA', 'GACTGTTC', 'TGTGCCGT'],
             'SI-P03-A9': ['ACAACTTG', 'CTCCAACA', 'GAGTGCGT', 'TGTGTGAC'],
             'SI-P03-B1': ['AGGCTACC', 'CTAGCTGT', 'GCCAACAA', 'TATTGGTG'],
             'SI-P03-B10': ['ACCATTAA', 'CTGGACGT', 'GAACGGTC', 'TGTTCACG'],
             'SI-P03-B11': ['ATGGTCGC', 'CGACATAG', 'GATTCGCT', 'TCCAGATA'],
             'SI-P03-B12': ['ACGCTTGG', 'CGCTACAT', 'GAAAGACA', 'TTTGCGTC'],
             'SI-P03-B2': ['AAGTTGAT', 'CCCACCCA', 'GGTCGAGC', 'TTAGATTG'],
             'SI-P03-B3': ['ATTGGACG', 'CAGCTTAC', 'GGCAAGGA', 'TCATCCTT'],
             'SI-P03-B4': ['AGGGACTG', 'CCTCTAAC', 'GACAGGCT', 'TTATCTGA'],
             'SI-P03-B5': ['ATCGTACT', 'CATCAGTG', 'GGGACTAC', 'TCATGCGA'],
             'SI-P03-B6': ['AACGCGAA', 'CTATTTGG', 'GCGCACCT', 'TGTAGATC'],
             'SI-P03-B7': ['AGGGATGA', 'CTTCTGTT', 'GAATGCAC', 'TCCACACG'],
             'SI-P03-B8': ['ACGTTCAC', 'CAAGGTCT', 'GTTAAGTG', 'TGCCCAGA'],
             'SI-P03-B9': ['AAGCGTGT', 'CTTGACCG', 'GCCACGTA', 'TGATTAAC'],
             'SI-P03-C1': ['AGACTTTC', 'CCGAGGCA', 'GATGCAGT', 'TTCTACAG'],
             'SI-P03-C10': ['ATCTGATC', 'CGTGCTAA', 'GAGAAGGG', 'TCACTCCT'],
             'SI-P03-C11': ['ACCGAACA', 'CGACTCTT', 'GTTTGTGG', 'TAGACGAC'],
             'SI-P03-C12': ['ATCCGGCA', 'CCGTTATG', 'GGTAATGT', 'TAAGCCAC'],
             'SI-P03-C2': ['AATCACTA', 'CCGAGAAC', 'GTAGTGCG', 'TGCTCTGT'],
             'SI-P03-C3': ['ACGTTACA', 'CGTAGGTT', 'GACGACGG', 'TTACCTAC'],
             'SI-P03-C4': ['ACATTGGC', 'CTTAGTCA', 'GAGCCCAT', 'TGCGAATG'],
             'SI-P03-C5': ['ATGCATTC', 'CACTGACT', 'GGTACGGG', 'TCAGTCAA'],
             'SI-P03-C6': ['ACTCAGAC', 'CGCTCAGG', 'GAGGTTTA', 'TTAAGCCT'],
             'SI-P03-C7': ['ACACCGGG', 'CATAATCC', 'GGCGGAAT', 'TTGTTCTA'],
             'SI-P03-C8': ['AGCTCGAG', 'CAGGAAGA', 'GCACGTTT', 'TTTATCCC'],
             'SI-P03-C9': ['AGATCGGT', 'CATCGTCG', 'GTCATATA', 'TCGGACAC'],
             'SI-P03-D1': ['AGCTGCGT', 'CAACCATC', 'GTGGAGCA', 'TCTATTAG'],
             'SI-P03-D10': ['AGATAACA', 'CTTATTTG', 'GCGGGCAT', 'TACCCGGC'],
             'SI-P03-D11': ['ATATGAGA', 'CACCTCAG', 'GCTACTTC', 'TGGGAGCT'],
             'SI-P03-D12': ['AGAAACGT', 'CACTCAAC', 'GCTGTGTA', 'TTGCGTCG'],
             'SI-P03-D2': ['ACATTCCG', 'CTGCGGTA', 'GACACAAT', 'TGTGATGC'],
             'SI-P03-D3': ['ACTTCACT', 'CGAAGTTG', 'GAGCACGC', 'TTCGTGAA'],
             'SI-P03-D4': ['AAATCGTC', 'CTTCGAAT', 'GCGATCGG', 'TGCGATCA'],
             'SI-P03-D5': ['AGACGGAT', 'CCTTTAGA', 'GTCGACTC', 'TAGACTCG'],
             'SI-P03-D6': ['ATGCCAAA', 'CCTTATCG', 'GAAGTCTT', 'TGCAGGGC'],
             'SI-P03-D7': ['AACTTAGA', 'CCGGATCC', 'GGTCGCAT', 'TTAACGTG'],
             'SI-P03-D8': ['AATCTTTG', 'CTCAAGAC', 'GGATGAGT', 'TCGGCCCA'],
             'SI-P03-D9': ['ACCTACTG', 'CAAGGGAC', 'GGGACACA', 'TTTCTTGT'],
             'SI-P03-E1': ['ACGAAAGC', 'CGCCCGTA', 'GTTTGCCT', 'TAAGTTAG'],
             'SI-P03-E10': ['ATTGTTTC', 'CGCAGGAG', 'GCACCAGT', 'TAGTACCA'],
             'SI-P03-E11': ['ATCGCCAT', 'CATAAAGG', 'GGGTTTCC', 'TCACGGTA'],
             'SI-P03-E12': ['ACGCGGAA', 'CGCTATCC', 'GTTGCATG', 'TAAATCGT'],
             'SI-P03-E2': ['AGGCTGGT', 'CACAACTA', 'GTTGGTCC', 'TCATCAAG'],
             'SI-P03-E3': ['AACAAGTC', 'CGGCTCCA', 'GTATGTAT', 'TCTGCAGG'],
             'SI-P03-E4': ['AGCTGACG', 'CCGGTGTC', 'GTAAACAT', 'TATCCTGA'],
             'SI-P03-E5': ['ATCCAAGG', 'CCGTTGAA', 'GGAAGCTC', 'TATGCTCT'],
             'SI-P03-E6': ['ATTGAAAC', 'CAGCCCGA', 'GCCATTTG', 'TGATGGCT'],
             'SI-P03-E7': ['AAGACGTG', 'CCATGTGT', 'GTTCACAA', 'TGCGTACC'],
             'SI-P03-E8': ['AGCCTATG', 'CTAACGCA', 'GCTTACAT', 'TAGGGTGC'],
             'SI-P03-E9': ['AGTAAGCA', 'CCGGTAAT', 'GTATCTTC', 'TACCGCGG'],
             'SI-P03-F1': ['ATCGCTCC', 'CCGTACAG', 'GATAGGTA', 'TGACTAGT'],
             'SI-P03-F10': ['ATTCGTGC', 'CGCGTGCA', 'GAATACTG', 'TCGACAAT'],
             'SI-P03-F11': ['AGCAGTTA', 'CTTGTACC', 'GAACCCGG', 'TCGTAGAT'],
             'SI-P03-F12': ['AATTGAAC', 'CCAGTGGA', 'GTCCATTG', 'TGGACCCT'],
             'SI-P03-F2': ['ATGGTTAG', 'CATTGATA', 'GCAAACGC', 'TGCCCGCT'],
             'SI-P03-F3': ['AGTCTGTA', 'CAGAATAG', 'GCCTCCGT', 'TTAGGACC'],
             'SI-P03-F4': ['AACGACAC', 'CGTCCTCT', 'GCATGATA', 'TTGATGGG'],
             'SI-P03-F5': ['AACACAGC', 'CGGTTTAG', 'GTACGGCT', 'TCTGACTA'],
             'SI-P03-F6': ['ATGCCGGC', 'CCTAATTA', 'GACTTCCT', 'TGAGGAAG'],
             'SI-P03-F7': ['ACCCGAGA', 'CAAACTTT', 'GGTTAGAC', 'TTGGTCCG'],
             'SI-P03-F8': ['AGTTGGGA', 'CCAGAAAG', 'GTGCCCTC', 'TACATTCT'],
             'SI-P03-F9': ['AGTTAGTT', 'CACGCACG', 'GTACTTAA', 'TCGAGCGC'],
             'SI-P03-G1': ['ATGCGATT', 'CATATGCG', 'GGATACGA', 'TCCGCTAC'],
             'SI-P03-G10': ['ATACTGAG', 'CGGAGACT', 'GATGCCTC', 'TCCTATGA'],
             'SI-P03-G11': ['AGGGCGTT', 'CTATACGC', 'GCTCGTCA', 'TACATAAG'],
             'SI-P03-G12': ['ACCCGCAC', 'CATGCGTA', 'GTGATAGT', 'TGATATCG'],
             'SI-P03-G2': ['ATAACCTA', 'CGGTGAGC', 'GATCTTAT', 'TCCGAGCG'],
             'SI-P03-G3': ['ATGTCCAG', 'CGACGTCA', 'GCTATAGC', 'TACGAGTT'],
             'SI-P03-G4': ['AGCTTCTC', 'CCTGCGGT', 'GTACAACG', 'TAGAGTAA'],
             'SI-P03-G5': ['ATGAAGTA', 'CGCCGAAC', 'GAAGCTCG', 'TCTTTCGT'],
             'SI-P03-G6': ['AGCACTGG', 'CATTACAC', 'GTGCGACA', 'TCAGTGTT'],
             'SI-P03-G7': ['ATTACCGG', 'CAGTAATT', 'GCCGGTAA', 'TGACTGCC'],
             'SI-P03-G8': ['AAGTACTC', 'CTTGGAGA', 'GGAACTCT', 'TCCCTGAG'],
             'SI-P03-G9': ['AGTCTCAG', 'CAATGGCA', 'GCCGAAGT', 'TTGACTTC'],
             'SI-P03-H1': ['AAACTCAT', 'CGGGAGTA', 'GTCACAGG', 'TCTTGTCC'],
             'SI-P03-H10': ['ATTTCAGC', 'CGAGTGAT', 'GACCGCCA', 'TCGAATTG'],
             'SI-P03-H11': ['AGGATCGA', 'CACGATTC', 'GTATCGAG', 'TCTCGACT'],
             'SI-P03-H12': ['ACGAGTAG', 'CAATCCCT', 'GTCCAGGC', 'TGTGTATA'],
             'SI-P03-H2': ['AACGGTCA', 'CCGAACTC', 'GGTCCAAG', 'TTATTGGT'],
             'SI-P03-H3': ['ACACCTAA', 'CGTTTGGG', 'GACAAACC', 'TTGGGCTT'],
             'SI-P03-H4': ['ACTGGAGC', 'CGGTCGTG', 'GAAATCAA', 'TTCCATCT'],
             'SI-P03-H5': ['ATAGTATG', 'CCGCGTCT', 'GGCTCCAC', 'TATAAGGA'],
             'SI-P03-H6': ['AAGCATAA', 'CCCATCGC', 'GGTTGATG', 'TTAGCGCT'],
             'SI-P03-H7': ['AACGGGTG', 'CTAATTCT', 'GCTTCAAC', 'TGGCACGA'],
             'SI-P03-H8': ['AAGAGCGG', 'CTTGTTAT', 'GGCCCATC', 'TCATAGCA'],
             'SI-P03-H9': ['ACCTGCCA', 'CTTCATAC', 'GGAATATG', 'TAGGCGGT']}
