<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CIGAR Parsing in Velocyto</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        header {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            margin: 0;
            font-size: 2.5rem;
        }
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-top: 0.5rem;
        }
        .section {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        }
        .section h2 {
            color: #2575fc;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 0.5rem;
            margin-top: 0;
        }
        .card-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 1.5rem 0;
        }
        .card {
            background: #f1f3f5;
            border-radius: 8px;
            padding: 1.2rem;
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card h3 {
            margin-top: 0;
            color: #495057;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }
        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        th {
            background-color: #e9ecef;
            font-weight: 600;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .visualization {
            text-align: center;
            margin: 2rem 0;
        }
        .conclusion {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            margin: 2rem 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .conclusion h2 {
            margin-top: 0;
        }
        .key-point {
            background: rgba(255,255,255,0.2);
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        pre {
            background: #f1f3f5;
            padding: 1rem;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            font-family: 'Courier New', monospace;
        }
        .highlight {
            background: #fff3cd;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-weight: bold;
        }
        .important {
            background: #ffeaa7;
            padding: 1rem;
            border-left: 4px solid #fdcb6e;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <header>
        <h1>CIGAR Parsing in Velocyto</h1>
        <div class="subtitle">Understanding How Alignment Information is Interpreted for Splicing Detection</div>
    </header>

    <div class="section">
        <h2>What is CIGAR?</h2>
        <p><strong>CIGAR</strong> (Compact Idiosyncratic Gapped Alignment Report) is a string format in BAM/SAM files that describes how a read aligns to the reference genome.</p>
        
        <div class="important">
            <p><strong>Key Concept:</strong> Each CIGAR string consists of <strong>operations</strong> and <strong>lengths</strong>, formatted as [length][operation] pairs.</p>
        </div>
        
        <p>For example, a CIGAR string "100M300N100M" means:</p>
        <ul>
            <li>100M: Match/mismatch for 100 bases</li>
            <li>300N: Skipped region (intron) of 300 bases</li>
            <li>100M: Match/mismatch for 100 bases</li>
        </ul>
        
        <p>This indicates a spliced alignment where the read spans two exons separated by a 300-base intron.</p>
    </div>

    <div class="section">
        <h2>CIGAR Operations Table</h2>
        <table>
            <thead>
                <tr>
                    <th>Code</th>
                    <th>Operation</th>
                    <th>Description</th>
                    <th>In RNA-seq Context</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>M</td>
                    <td>BAM_CMATCH</td>
                    <td>Alignment match (can be match or mismatch)</td>
                    <td>Read aligns to exon sequence</td>
                </tr>
                <tr>
                    <td>I</td>
                    <td>BAM_CINS</td>
                    <td>Insertion to the reference</td>
                    <td>Read has extra bases not in reference</td>
                </tr>
                <tr>
                    <td>D</td>
                    <td>BAM_CDEL</td>
                    <td>Deletion from the reference</td>
                    <td>Reference has extra bases not in read</td>
                </tr>
                <tr>
                    <td>N</td>
                    <td>BAM_CREF_SKIP</td>
                    <td>Skipped region from the reference</td>
                    <td><span class="highlight">Intron skipping - key for splicing detection</span></td>
                </tr>
                <tr>
                    <td>S</td>
                    <td>BAM_CSOFT_CLIP</td>
                    <td>Soft clipping (clipped sequences present in SEQ)</td>
                    <td>Read ends that don't align</td>
                </tr>
                <tr>
                    <td>H</td>
                    <td>BAM_CHARD_CLIP</td>
                    <td>Hard clipping (clipped sequences NOT present in SEQ)</td>
                    <td>Read ends that were removed</td>
                </tr>
                <tr>
                    <td>P</td>
                    <td>BAM_CPAD</td>
                    <td>Padding (silent deletion from padded reference)</td>
                    <td>Rarely used in RNA-seq</td>
                </tr>
                <tr>
                    <td>=</td>
                    <td>BAM_CEQUAL</td>
                    <td>Sequence match</td>
                    <td>Exact match to reference</td>
                </tr>
                <tr>
                    <td>X</td>
                    <td>BAM_CDIFF</td>
                    <td>Sequence mismatch</td>
                    <td>Mismatch to reference</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="section">
        <h2>How CIGAR Parsing Works in Velocyto</h2>
        
        <div class="card-container">
            <div class="card">
                <h3>1. CIGAR String Extraction</h3>
                <p>From each BAM alignment, velocyto extracts the CIGAR string using pysam:</p>
                <pre><code>read.cigartuples  # Returns list of (operation, length) tuples</code></pre>
            </div>
            
            <div class="card">
                <h3>2. Operation Analysis</h3>
                <p>Veloctyo parses the CIGAR tuples to identify key operations:</p>
                <pre><code>for operation_id, length in cigartuples:
    if operation_id == 3:  # BAM_CREF_SKIP
        ref_skip = True</code></pre>
            </div>
            
            <div class="card">
                <h3>3. Segment Extraction</h3>
                <p>Alignment segments are extracted for further analysis:</p>
                <pre><code>if operation_id == 0:  # BAM_CMATCH
    segments.append((p, p + length - 1))
    p += length</code></pre>
            </div>
            
            <div class="card">
                <h3>4. Splicing Decision</h3>
                <p>If any BAM_CREF_SKIP operations are found, the read is marked as spliced:</p>
                <pre><code>def parse_cigar_tuple(cigartuples, pos):
    ref_skip = False
    # ... parsing logic ...
    return segments, ref_skip, clip5, clip3

# In Read initialization:
segments, ref_skipped, clip5, clip3 = parse_cigar_tuple(read.cigartuples, pos)
Read(..., ref_skipped=ref_skipped)</code></pre>
            </div>
        </div>
    </div>

    <div class="visualization">
        <h2>Visualizing CIGAR Parsing</h2>
        <svg width="100%" height="700" viewBox="0 0 900 700" xmlns="http://www.w3.org/2000/svg">
            <!-- Background -->
            <rect width="100%" height="100%" fill="#f8f9fa"/>
            
            <!-- Title -->
            <text x="450" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#333">
                CIGAR Parsing Process in Velocyto
            </text>
            
            <!-- Reference genome track -->
            <text x="50" y="80" font-size="16" font-weight="bold" fill="#495057">Reference Genome:</text>
            
            <!-- Exon 1 -->
            <rect x="150" y="70" width="200" height="40" fill="#28a745" rx="5"/>
            <text x="250" y="95" text-anchor="middle" font-size="12" fill="white">Exon 1</text>
            
            <!-- Intron -->
            <rect x="350" y="70" width="200" height="40" fill="#6c757d" rx="5"/>
            <text x="450" y="95" text-anchor="middle" font-size="12" fill="white">Intron (300 bp)</text>
            
            <!-- Exon 2 -->
            <rect x="550" y="70" width="200" height="40" fill="#28a745" rx="5"/>
            <text x="650" y="95" text-anchor="middle" font-size="12" fill="white">Exon 2</text>
            
            <!-- Read alignment -->
            <text x="50" y="160" font-size="16" font-weight="bold" fill="#007bff">Read Alignment:</text>
            
            <!-- Spliced read -->
            <rect x="175" y="180" width="150" height="20" fill="#007bff" rx="3"/>
            <text x="250" y="195" text-anchor="middle" font-size="10" fill="white">Segment 1 (150M)</text>
            
            <line x1="325" y1="190" x2="550" y2="190" stroke="#007bff" stroke-width="2" stroke-dasharray="5,5"/>
            <text x="437" y="180" text-anchor="middle" font-size="10" fill="#007bff">Skip (300N)</text>
            
            <rect x="550" y="180" width="150" height="20" fill="#007bff" rx="3"/>
            <text x="625" y="195" text-anchor="middle" font-size="10" fill="white">Segment 2 (150M)</text>
            
            <!-- CIGAR string -->
            <text x="250" y="230" text-anchor="middle" font-size="14" fill="#007bff">
                CIGAR: 150M300N150M
            </text>
            
            <!-- CIGAR parsing process -->
            <text x="50" y="280" font-size="16" font-weight="bold" fill="#28a745">CIGAR Parsing Process:</text>
            
            <!-- Step 1 -->
            <rect x="100" y="300" width="200" height="60" fill="#f1f3f5" rx="5" stroke="#28a745" stroke-width="2"/>
            <text x="200" y="320" text-anchor="middle" font-size="14" fill="#28a745">Step 1: Extract CIGAR</text>
            <text x="200" y="340" text-anchor="middle" font-size="12">150M 300N 150M</text>
            
            <!-- Arrow 1 -->
            <line x1="200" y1="360" x2="200" y2="380" stroke="#28a745" stroke-width="2" marker-end="url(#arrow)"/>
            
            <!-- Step 2 -->
            <rect x="100" y="380" width="200" height="60" fill="#f1f3f5" rx="5" stroke="#ffc107" stroke-width="2"/>
            <text x="200" y="400" text-anchor="middle" font-size="14" fill="#ffc107">Step 2: Parse Operations</text>
            <text x="200" y="420" text-anchor="middle" font-size="12">Op 0: 150, Op 3: 300, Op 0: 150</text>
            
            <!-- Arrow 2 -->
            <line x1="200" y1="440" x2="200" y2="460" stroke="#ffc107" stroke-width="2" marker-end="url(#arrow)"/>
            
            <!-- Step 3 -->
            <rect x="100" y="460" width="200" height="60" fill="#f1f3f5" rx="5" stroke="#6c757d" stroke-width="2"/>
            <text x="200" y="480" text-anchor="middle" font-size="14" fill="#6c757d">Step 3: Detect Skip</text>
            <text x="200" y="500" text-anchor="middle" font-size="12">Operation 3 (BAM_CREF_SKIP) found!</text>
            
            <!-- Arrow 3 -->
            <line x1="200" y1="520" x2="200" y2="540" stroke="#6c757d" stroke-width="2" marker-end="url(#arrow)"/>
            
            <!-- Step 4 -->
            <rect x="100" y="540" width="200" height="60" fill="#f1f3f5" rx="5" stroke="#20c997" stroke-width="2"/>
            <text x="200" y="560" text-anchor="middle" font-size="14" fill="#20c997">Step 4: Set ref_skipped</text>
            <text x="200" y="580" text-anchor="middle" font-size="12">ref_skipped = True</text>
            
            <!-- Arrow to result -->
            <line x1="300" y1="570" x2="400" y2="570" stroke="#20c997" stroke-width="2" marker-end="url(#arrow)"/>
            
            <!-- Result -->
            <rect x="400" y="540" width="200" height="60" fill="#28a745" rx="5"/>
            <text x="500" y="560" text-anchor="middle" font-size="14" fill="white">Result: Spliced Read</text>
            <text x="500" y="580" text-anchor="middle" font-size="12" fill="white">is_spliced = True</text>
            
            <!-- Arrow to segments -->
            <line x1="200" y1="300" x2="500" y2="240" stroke="#6c757d" stroke-width="1" stroke-dasharray="5,5"/>
            
            <!-- Segments extraction -->
            <rect x="450" y="250" width="150" height="40" fill="#f1f3f5" rx="5" stroke="#6f42c1" stroke-width="2"/>
            <text x="525" y="270" text-anchor="middle" font-size="12" fill="#6f42c1">Segments Extraction</text>
            <text x="525" y="285" text-anchor="middle" font-size="10">[(175, 324), (550, 699)]</text>
            
            <!-- Arrow markers -->
            <defs>
                <marker id="arrow" markerWidth="10" markerHeight="10" refX="6" refY="3" orient="auto" markerUnits="strokeWidth">
                    <path d="M0,0 L0,6 L9,3 z" fill="#333"/>
                </marker>
            </defs>
            
            <!-- Additional example: Unspliced read -->
            <text x="50" y="630" font-size="16" font-weight="bold" fill="#6c757d">Comparison - Unspliced Read:</text>
            <rect x="350" y="650" width="200" height="20" fill="#6c757d" rx="3"/>
            <text x="450" y="665" text-anchor="middle" font-size="10" fill="white">Continuous Alignment (200M)</text>
            <text x="450" y="685" text-anchor="middle" font-size="12" fill="#6c757d">
                CIGAR: 200M (No N operations)
            </text>
            <text x="450" y="700" text-anchor="middle" font-size="12" fill="#6c757d">
                ref_skipped = False → is_spliced = False
            </text>
        </svg>
    </div>

    <div class="conclusion">
        <h2>Key Takeaways</h2>
        
        <div class="key-point">
            <h3>1. CIGAR as Alignment Blueprint</h3>
            <p>The CIGAR string is essentially a blueprint that describes how a read aligns to the reference genome, with each operation representing a different type of alignment event.</p>
        </div>
        
        <div class="key-point">
            <h3>2. BAM_CREF_SKIP is Key for Splicing</h3>
            <p>Operation code 3 (BAM_CREF_SKIP) is the critical indicator of intron skipping in RNA-seq data, which allows velocyto to distinguish spliced from unspliced reads.</p>
        </div>
        
        <div class="key-point">
            <h3>3. Parsing Enables Quantification</h3>
            <p>By parsing CIGAR strings, velocyto can accurately quantify spliced and unspliced reads, which is essential for RNA velocity analysis.</p>
        </div>
        
        <div class="key-point">
            <h3>4. Segments for Feature Mapping</h3>
            <p>The extracted alignment segments are used to map reads to genomic features (exons, introns), enabling precise quantification of transcriptional activity.</p>
        </div>
    </div>
</body>
</html>