<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modifying Velocyto for RX UMI Tags</title>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 1rem 0 0 0;
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border-left: 4px solid #e74c3c;
            background: #f8f9fa;
            border-radius: 0 8px 8px 0;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.8rem;
            border-bottom: 2px solid #e74c3c;
            padding-bottom: 0.5rem;
        }
        
        .section h3 {
            color: #34495e;
            margin-top: 2rem;
            font-size: 1.4rem;
        }
        
        .code-box {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            position: relative;
        }
        
        .code-box .filename {
            position: absolute;
            top: 0.5rem;
            right: 1rem;
            background: #e74c3c;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 3px;
            font-size: 0.8rem;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .step-number {
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
        }
        
        .workflow-step {
            display: flex;
            align-items: flex-start;
            margin: 1.5rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 2px solid #17a2b8;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .success-box {
            background: #d4edda;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .diff-add {
            background: #d4edda;
            color: #155724;
            padding: 0.1rem 0.3rem;
            border-radius: 3px;
        }
        
        .diff-remove {
            background: #f8d7da;
            color: #721c24;
            padding: 0.1rem 0.3rem;
            border-radius: 3px;
            text-decoration: line-through;
        }
        
        .file-path {
            background: #6c757d;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Modifying Velocyto for RX UMI Tags</h1>
            <p>Step-by-step guide to support custom UMI tag formats</p>
        </div>
        
        <div class="content">
            <!-- Problem Description -->
            <div class="section">
                <h2>Problem Description</h2>
                
                <p>Velocyto.py automatically detects UMI tags by "peeking" into BAM files and looking for standard tags:</p>
                <ul>
                    <li><span class="highlight">UB</span> - 10X Genomics/Cell Ranger format</li>
                    <li><span class="highlight">XM</span> - Drop-seq format</li>
                </ul>
                
                <p>However, your data uses <span class="highlight">RX</span> tags for UMI information, which velocyto doesn't recognize by default.</p>
                
                <div class="warning-box">
                    <strong>⚠️ Important:</strong> The modifications below require editing the velocyto source code. Make sure to backup your original files before making changes.
                </div>
            </div>

            <!-- Solution Overview -->
            <div class="section">
                <h2>Solution Overview</h2>
                
                <p>We need to modify the <span class="file-path">velocyto/counter.py</span> file to recognize RX tags. The key functions to modify are:</p>
                
                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>peek():</strong> Detects UMI tag format by examining BAM file headers
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>peek_umi_only():</strong> Detects UMI tags when only UMI detection is needed
                    </div>
                </div>
                
                <p>Both functions need to be updated to recognize RX tags and set the appropriate <code>umibarcode_str</code> attribute.</p>
            </div>

            <!-- Step-by-step Modifications -->
            <div class="section">
                <h2>Step-by-Step Modifications</h2>
                
                <h3>Step 1: Locate the File</h3>
                <p>Navigate to your velocyto installation and find:</p>
                <div class="code-box">
<span class="filename">File Location</span>
# Typical locations:
# Conda: ~/anaconda3/envs/your_env/lib/python3.x/site-packages/velocyto/counter.py
# Pip: ~/.local/lib/python3.x/site-packages/velocyto/counter.py
# Or in your current directory: velocyto/counter.py

# Find the exact location:
python -c "import velocyto; print(velocyto.__file__)"
                </div>
                
                <h3>Step 2: Modify the peek() Function</h3>
                <p>Find the <code>peek()</code> function around line 131 and modify it as follows:</p>
                
                <div class="code-box">
<span class="filename">velocyto/counter.py</span>
def peek(self, bamfile: str, lines: int=1000) -> None:
    """Peeks into the samfile to determine if it is a cellranger or dropseq file
    """
    logging.debug(f"Peeking into {bamfile}")
    fin = pysam.AlignmentFile(bamfile)  # type: pysam.AlignmentFile
    cellranger: int = 0
    dropseq: int = 0
    <span class="diff-add">rx_format: int = 0  # Add counter for RX format</span>
    failed: int = 0
    for i, read in enumerate(fin):
        if read.is_unmapped:
            continue
        if read.has_tag("CB") and read.has_tag("UB"):
            cellranger += 1
        elif read.has_tag("XC") and read.has_tag("XM"):
            dropseq += 1
        <span class="diff-add">elif read.has_tag("CB") and read.has_tag("RX"):  # Add RX detection
            rx_format += 1</span>
        else:
            logging.warn(f"Not found cell and umi barcode in entry {i} of the bam file")
            failed += 1
        if cellranger > lines:
            self.cellbarcode_str = "CB"
            self.umibarcode_str = "UB"
            break
        elif dropseq > lines:
            self.cellbarcode_str = "XC"
            self.umibarcode_str = "XM"
            break
        <span class="diff-add">elif rx_format > lines:  # Add RX format handling
            self.cellbarcode_str = "CB"
            self.umibarcode_str = "RX"
            break</span>
        elif failed > 5 * lines:
            raise IOError("The bam file does not contain cell and umi barcodes appropriatelly formatted. If you are runnin UMI-less data you should use the -U flag.")
        else:
            pass
    fin.close()
                </div>
                
                <h3>Step 3: Modify the peek_umi_only() Function</h3>
                <p>Find the <code>peek_umi_only()</code> function around line 163 and modify it:</p>
                
                <div class="code-box">
<span class="filename">velocyto/counter.py</span>
def peek_umi_only(self, bamfile: str, lines: int=30) -> None:
    """Peeks for umi into the samfile to determine if it is a cellranger or dropseq file
    """
    logging.debug(f"Peeking into {bamfile}")
    fin = pysam.AlignmentFile(bamfile)  # type: pysam.AlignmentFile
    cellranger: int = 0
    dropseq: int = 0
    <span class="diff-add">rx_format: int = 0  # Add counter for RX format</span>
    failed: int = 0
    for i, read in enumerate(fin):
        if read.is_unmapped:
            continue
        if read.has_tag("UB"):
            cellranger += 1
        elif read.has_tag("XM"):
            dropseq += 1
        <span class="diff-add">elif read.has_tag("RX"):  # Add RX detection
            rx_format += 1</span>
        else:
            logging.warn(f"Not found cell and umi barcode in entry {i} of the bam file")
            failed += 1
        if cellranger > lines:
            self.umibarcode_str = "UB"
            break
        elif dropseq > lines:
            self.umibarcode_str = "XM"
            break
        <span class="diff-add">elif rx_format > lines:  # Add RX format handling
            self.umibarcode_str = "RX"
            break</span>
        elif failed > 5 * lines:
            raise IOError("The bam file does not contain umi barcodes appropriatelly formatted. If you are runnin UMI-less data you should use the -U flag.")
        else:
            pass
    fin.close()
                </div>
            </div>

            <!-- Alternative Solutions -->
            <div class="section">
                <h2>Alternative Solutions</h2>
                
                <h3>Option 1: Command Line Override (Recommended)</h3>
                <p>If you don't want to modify the source code, you can create a simple wrapper script:</p>
                
                <div class="code-box">
<span class="filename">run_velocyto_rx.py</span>
#!/usr/bin/env python3
import velocyto as vcy
import sys

# Create counter object
counter = vcy.ExInCounter()

# Manually set the UMI barcode string to RX
counter.umibarcode_str = "RX"
counter.cellbarcode_str = "CB"  # Assuming you use CB for cell barcodes

# Skip the automatic peek detection
# counter.peek(bamfile)  # Don't call this

# Continue with normal velocyto workflow
# ... rest of your analysis
                </div>
                
                <h3>Option 2: Environment Variable Approach</h3>
                <p>You could modify velocyto to check for an environment variable:</p>
                
                <div class="code-box">
<span class="filename">Modified counter.py</span>
import os

def peek(self, bamfile: str, lines: int=1000) -> None:
    # Check for environment variable override
    umi_tag_override = os.environ.get('VELOCYTO_UMI_TAG')
    cell_tag_override = os.environ.get('VELOCYTO_CELL_TAG')
    
    if umi_tag_override and cell_tag_override:
        self.umibarcode_str = umi_tag_override
        self.cellbarcode_str = cell_tag_override
        return
    
    # Original peek logic here...
                </div>
                
                <p>Then run velocyto with:</p>
                <div class="code-box">
<span class="filename">Terminal</span>
export VELOCYTO_UMI_TAG=RX
export VELOCYTO_CELL_TAG=CB
velocyto run sample.bam annotation.gtf
                </div>
            </div>

            <!-- Testing and Verification -->
            <div class="section">
                <h2>Testing and Verification</h2>
                
                <h3>Step 1: Test the Modification</h3>
                <div class="code-box">
<span class="filename">test_rx_detection.py</span>
import velocyto as vcy
import pysam

# Test if RX detection works
counter = vcy.ExInCounter()
counter.peek("your_sample.bam")

print(f"Detected cell barcode tag: {counter.cellbarcode_str}")
print(f"Detected UMI barcode tag: {counter.umibarcode_str}")

# Should output:
# Detected cell barcode tag: CB
# Detected UMI barcode tag: RX
                </div>
                
                <h3>Step 2: Verify BAM File Tags</h3>
                <div class="code-box">
<span class="filename">check_bam_tags.py</span>
import pysam

# Check what tags are actually in your BAM file
bamfile = pysam.AlignmentFile("your_sample.bam")
for i, read in enumerate(bamfile):
    if i > 10:  # Check first 10 reads
        break
    if not read.is_unmapped:
        tags = dict(read.tags)
        print(f"Read {i} tags: {list(tags.keys())}")
        if 'RX' in tags:
            print(f"  RX (UMI): {tags['RX']}")
        if 'CB' in tags:
            print(f"  CB (Cell): {tags['CB']}")
bamfile.close()
                </div>
                
                <h3>Step 3: Run Velocyto</h3>
                <div class="code-box">
<span class="filename">Terminal</span>
# After making the modifications, run velocyto normally:
velocyto run your_sample.bam annotation.gtf

# Or with additional options:
velocyto run your_sample.bam annotation.gtf -o output_folder
                </div>
            </div>

            <!-- Troubleshooting -->
            <div class="section">
                <h2>Troubleshooting</h2>
                
                <div class="warning-box">
                    <strong>Common Issues:</strong>
                    <ul>
                        <li><strong>Import Error:</strong> If you get import errors after modification, check Python syntax</li>
                        <li><strong>Still Using UB:</strong> Make sure you modified both <code>peek()</code> and <code>peek_umi_only()</code></li>
                        <li><strong>No RX Tags Found:</strong> Verify your BAM file actually contains RX tags</li>
                    </ul>
                </div>
                
                <div class="info-box">
                    <strong>💡 Debug Tips:</strong>
                    <ul>
                        <li>Add print statements to see which detection path is taken</li>
                        <li>Use <code>samtools view -H your_file.bam</code> to check BAM header</li>
                        <li>Test with a small subset of your BAM file first</li>
                    </ul>
                </div>
                
                <div class="success-box">
                    <strong>✅ Success Indicators:</strong>
                    <ul>
                        <li>Velocyto runs without "barcode not found" errors</li>
                        <li>Output .loom file is generated successfully</li>
                        <li>Cell and UMI counts look reasonable</li>
                    </ul>
                </div>
            </div>

            <!-- Summary -->
            <div class="section">
                <h2>Summary</h2>
                
                <p>To make velocyto work with RX UMI tags, you need to:</p>
                
                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>Modify peek() function:</strong> Add RX detection logic alongside existing UB/XM detection
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>Modify peek_umi_only() function:</strong> Add RX detection for UMI-only scenarios
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div>
                        <strong>Test thoroughly:</strong> Verify the modifications work with your specific BAM files
                    </div>
                </div>
                
                <p>The modifications are minimal and follow the existing pattern used for UB and XM tags. Once implemented, velocyto should automatically detect and use RX tags for UMI information.</p>
                
                <div class="highlight" style="display: block; text-align: center; padding: 1rem; margin: 2rem 0; font-size: 1.1rem;">
                    🎯 <strong>Key Point:</strong> These changes maintain backward compatibility while adding support for your RX tag format.
                </div>
            </div>
        </div>
    </div>
</body>
</html>
