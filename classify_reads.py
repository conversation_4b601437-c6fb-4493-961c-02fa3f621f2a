#!/usr/bin/env python3
"""
Read Classification Script using velocyto

This script takes a BAM file and GTF file as input and outputs read-level classification results.
Each read is classified as spliced, unspliced, or ambiguous based on velocyto's logic.

Usage:
    python classify_reads.py <bam_file> <gtf_file> [output_file]

Requirements:
    - velocyto
    - pysam
    - numpy
"""

import sys
import logging
import argparse
from typing import List, Dict, Tuple, Set, Any, Optional
from collections import defaultdict
import os

# Import velocyto components
import velocyto as vcy
import numpy as np


# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ReadClassifier:
    """Wrapper class that uses velocyto for read classification"""
    
    def __init__(self, sample_id: str = "read_classification"):
        """Initialize the classifier using velocyto components"""
        self.sample_id = sample_id
        
        # Initialize the ExInCounter with validation enabled
        # Use Permissive10X logic which performs intron validation
        self.counter = vcy.ExInCounter(
            sampleid=sample_id,
            logic=vcy.logic.Permissive10X,  # Pass the class, not an instance
            valid_bcset=None,  # No barcode filtering
            umi_extension="without_umi",  # No UMI processing needed
            onefilepercell=False,
            dump_option="0",
            outputfolder="./",
            loom_numeric_dtype="float32"
        )
        
        # Override barcode detection since we don't need it for classification
        self.counter.cellbarcode_str = "CB"
        self.counter.umibarcode_str = "UB"
        
        # Store read classifications
        self.read_classifications = []
    
    def load_annotations(self, gtf_file: str):
        """Load transcript annotations from GTF file using velocyto"""
        logger.info(f"Loading annotations from {gtf_file}")
        
        # Use velocyto's GTF reader
        self.counter.read_transcriptmodels(gtf_file)
        
        # Log statistics
        total_transcripts = sum(len(models) for models in self.counter.annotations_by_chrm_strand.values())
        logger.info(f"Loaded {total_transcripts} transcript models from {len(self.counter.annotations_by_chrm_strand)} chromosome+strand combinations")
    
    def validate_introns(self, bam_file: str):
        """Validate introns using velocyto's mark_up_introns method"""
        logger.info(f"Validating introns using reads from {bam_file}")
        
        # Use velocyto's intron markup functionality
        self.counter.mark_up_introns((bam_file,), multimap=False)
        
        logger.info("Intron validation completed")
    
    def classify_reads(self, bam_file: str):
        """Classify reads using velocyto's logic"""
        logger.info(f"Classifying reads from {bam_file}")
        
        # Create a custom logic instance for classification
        logic = vcy.logic.Permissive10X()
        
        # Process reads and classify them
        read_count = 0
        classified_count = 0
        
        for read in self.counter.iter_alignments((bam_file,), unique=True):
            if read is None:
                continue  # File boundary marker
            
            read_count += 1
            if read_count % 100000 == 0:
                logger.info(f"Processed {read_count // 1000} thousand reads")
            
            # Get chromosome+strand
            chromstrand = read.chrom + read.strand
            
            # Skip if no annotations for this chromosome+strand
            if chromstrand not in self.counter.annotations_by_chrm_strand:
                continue
            
            # Find overlapping features using velocyto's feature index
            if hasattr(self.counter, 'feature_indexes') and chromstrand in self.counter.feature_indexes:
                feature_index = self.counter.feature_indexes[chromstrand]
                mappings_record = feature_index.find_overlapping_ivls(read)
                
                # Classify the read based on its mappings
                classification = self._classify_read_mappings(read, mappings_record, logic)
                
                if classification:
                    self.read_classifications.append(classification)
                    classified_count += 1
        
        logger.info(f"Classified {classified_count} reads out of {read_count} total reads")
    
    def _classify_read_mappings(self, read: vcy.Read, mappings_record: Dict, logic) -> Optional[Dict]:
        """Classify a single read based on its feature mappings"""
        if not mappings_record:
            return None
        
        # For each transcript model that this read maps to
        for transcript_model, segment_matches in mappings_record.items():
            if not segment_matches:
                continue
            
            # Analyze the mapping to determine classification
            has_exons = False
            has_introns = False
            has_validated_introns = False
            has_spanning = False
            
            for segment_match in segment_matches:
                feature = segment_match.feature
                
                if feature.kind == ord("e"):  # Exon
                    has_exons = True
                elif feature.kind == ord("i"):  # Intron
                    has_introns = True
                    if feature.is_validated:
                        has_validated_introns = True
                        # Check if this read spans exon-intron boundaries
                        if (feature.end_overlaps_with_part_of(segment_match.segment) or
                            feature.start_overlaps_with_part_of(segment_match.segment)):
                            has_spanning = True
            
            # Determine classification based on velocyto logic
            classification = self._determine_classification(
                read, transcript_model, has_exons, has_introns, 
                has_validated_introns, has_spanning
            )
            
            if classification:
                return {
                    'read_id': f"{read.chrom}:{read.pos}-{read.end}:{read.strand}",
                    'chromosome': read.chrom,
                    'start': read.pos,
                    'end': read.end,
                    'strand': read.strand,
                    'transcript_id': transcript_model.trid,
                    'transcript_name': transcript_model.trname,
                    'gene_id': transcript_model.geneid,
                    'gene_name': transcript_model.genename,
                    'classification': classification,
                    'is_spliced': read.is_spliced,
                    'has_exons': has_exons,
                    'has_introns': has_introns,
                    'has_validated_introns': has_validated_introns,
                    'has_spanning': has_spanning
                }
        
        return None
    
    def _determine_classification(self, read, transcript_model, has_exons, has_introns, 
                                has_validated_introns, has_spanning) -> str:
        """Determine read classification based on velocyto Permissive10X logic"""
        
        # Spliced reads (have reference skips) are generally spliced
        if read.is_spliced:
            if has_exons:
                return "spliced"
            else:
                return "ambiguous"
        
        # Non-spliced reads
        if has_exons and not has_introns:
            # Only exonic mappings
            return "spliced"
        elif has_introns and not has_exons:
            # Only intronic mappings
            if has_validated_introns:
                return "unspliced"
            else:
                # Permissive logic: count non-validated introns as unspliced too
                return "unspliced"
        elif has_exons and has_introns:
            # Mixed exonic and intronic mappings
            if has_validated_introns or has_spanning:
                return "unspliced"
            else:
                return "ambiguous"
        else:
            # No clear mappings
            return "ambiguous"
    
    def write_output(self, output_file: str):
        """Write read classifications to output file"""
        logger.info(f"Writing {len(self.read_classifications)} classified reads to {output_file}")
        
        with open(output_file, 'w') as f:
            # Write header
            header = [
                'read_id', 'chromosome', 'start', 'end', 'strand',
                'transcript_id', 'transcript_name', 'gene_id', 'gene_name',
                'classification', 'is_spliced', 'has_exons', 'has_introns',
                'has_validated_introns', 'has_spanning'
            ]
            f.write('\t'.join(header) + '\n')
            
            # Sort reads by chromosome, start position
            sorted_reads = sorted(self.read_classifications, 
                                key=lambda x: (x['chromosome'], x['start']))
            
            # Write classified reads
            for read_info in sorted_reads:
                row = [str(read_info[col]) for col in header]
                f.write('\t'.join(row) + '\n')
    
    def get_classification_summary(self) -> Dict[str, int]:
        """Get summary statistics of read classifications"""
        summary = defaultdict(int)
        for read_info in self.read_classifications:
            summary[read_info['classification']] += 1
        return dict(summary)


def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(
        description="Classify reads as spliced/unspliced/ambiguous using BAM and GTF files with velocyto",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python classify_reads.py sample.bam annotations.gtf
    python classify_reads.py sample.bam annotations.gtf read_classifications.tsv
    python classify_reads.py sample.bam annotations.gtf --verbose
        """
    )
    
    parser.add_argument('bam_file', help='Input BAM file (should be coordinate sorted)')
    parser.add_argument('gtf_file', help='Input GTF annotation file')
    parser.add_argument('output_file', nargs='?', default='read_classifications.tsv',
                       help='Output file for read classifications (default: read_classifications.tsv)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--sample-id', default='read_classification',
                       help='Sample ID for velocyto processing (default: read_classification)')
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        # Also set velocyto logging to debug
        logging.getLogger('velocyto').setLevel(logging.DEBUG)
    
    # Check if files exist
    if not os.path.exists(args.bam_file):
        logger.error(f"BAM file not found: {args.bam_file}")
        sys.exit(1)
    
    if not os.path.exists(args.gtf_file):
        logger.error(f"GTF file not found: {args.gtf_file}")
        sys.exit(1)
    
    try:
        # Initialize classifier using velocyto
        logger.info("Initializing read classifier using velocyto...")
        classifier = ReadClassifier(args.sample_id)
        
        # Load annotations
        logger.info("Loading transcript annotations...")
        classifier.load_annotations(args.gtf_file)
        
        # Validate introns (required for proper classification)
        logger.info("Validating introns...")
        classifier.validate_introns(args.bam_file)
        
        # Classify reads
        logger.info("Classifying reads...")
        classifier.classify_reads(args.bam_file)
        
        # Write output
        classifier.write_output(args.output_file)
        
        # Summary statistics
        summary = classifier.get_classification_summary()
        total_reads = sum(summary.values())
        
        logger.info(f"Classification complete!")
        logger.info(f"Total classified reads: {total_reads}")
        for classification, count in summary.items():
            percentage = count / total_reads * 100 if total_reads > 0 else 0
            logger.info(f"  {classification}: {count} ({percentage:.1f}%)")
        
        logger.info(f"Results written to: {args.output_file}")
        
    except Exception as e:
        logger.error(f"Error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
