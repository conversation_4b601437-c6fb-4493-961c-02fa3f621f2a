# Sensitive and Emotional Annotations in Velocyto Codebase

This document captures the sensitive and emotional annotations created by the authors of the Velocyto codebase. These annotations reveal the authors' thoughts, concerns, frustrations, and design decisions throughout the development process.

## Frustrations and Concerns

### Counter.py
- Line 29: `# NOTE: maybe there shoulb be a self.logic.verify_inputs(args) step at the end of init`
  - Shows uncertainty about whether a verification step should be added

- Line 65: `# NOTE: by using a default dict and not logging access to keys that do not exist, we might miss bugs!!!`
  - Expresses concern about potential bugs due to design choices

- Line 77-83: Commented out code with note about unsupported feature
  - Shows awareness of limitations in the current implementation

- Line 121: `# NOTE: maybe we should make so that the reads get discarded`
  - Indicates uncertainty about the best approach for handling insertions

- Line 123: `logging.warn("Hard clip was encountered! All mapping are assumed soft clipped")`
  - Shows concern about how the code handles edge cases

- Line 147: `logging.warn(f"Not found cell and umi barcode in entry {i} of the bam file")`
  - Expresses concern about missing barcodes in data

- Line 158: `raise IOError("The bam file does not contain cell and umi barcodes appropriatelly formatted. If you are runnin UMI-less data you should use the -U flag.")`
  - Shows frustration with data formatting issues

- Line 179: `logging.warn(f"Not found cell and umi barcode in entry {i} of the bam file")`
  - Repeated concern about barcode formatting

- Line 188: `raise IOError("The bam file does not contain umi barcodes appropriatelly formatted. If you are runnin UMI-less data you should use the -U flag.")`
  - Similar frustration with data formatting

- Line 235: `# No peeking here, this will happen a layer above, and only once  on the not sorted file! before it was self.peek(samfile, lines=10)`
  - Shows awareness of design decisions and previous implementations

- Line 265: `raise KeyError(f"Some errors in parsing the cell barcode has occurred {self.cellbarcode_str}, {self.umibarcode_str}\n{read}")`
  - Indicates potential issues with barcode parsing

- Line 276-277: `# I have to deal with incongruences with the .gft (what is cellranger doing???)`
  - Shows frustration with inconsistencies in external tools

- Line 279: `# NOTE Why this happens?`
  - Expresses confusion about unexpected behavior

- Line 287: `logging.debug("No segments in read:%s" % read.qname)`
  - Debugging concern about empty segments

- Line 292: `logging.warn(f"Trashing read, too long span\n{read.tostring(fin)}")`
  - Shows concern about handling extreme cases

- Line 297: `logging.warn(f"Trashing read, too long span\n{read.tostring(fin)}")`
  - Repeated concern about long reads

- Line 306: `logging.debug(f"{counter_skipped_no_barcode} reads were skipped because no apropiate cell or umi barcode was found")`
  - Notes about data quality issues

- Line 326-332: Commented out code for sorting GTF file
  - Shows consideration of alternative approaches

- Line 353-356: Code to handle chromosome naming inconsistencies
  - Shows awareness of data format issues

- Line 390: `# On chromosome or strand change: save and clean after yourself`
  - Friendly reminder to developers about cleanup

- Line 394: `# NOTE exin_no is being used to store curr_n, and kind could be used to store tag for some extra memory cost`
  - Shows consideration of memory trade-offs

- Line 401-411: Comments about storing extra information
  - Shows awareness of memory vs. functionality trade-offs

- Line 495-497: `if not chrom + strand not in self.annotations_by_chrm_strand:`
  - Expresses concern about file sorting issues

- Line 497: `raise IOError(f"Genome annotation gtf file is not sorted correctly! Run the following command:\nsort -k1,1 -k7,7 -k4,4n -o [GTF_OUTFILE] [GTF_INFILE]")`
  - Shows frustration with improperly formatted input files

- Line 523: `# NOTE: Don't try to release this constraint, velocyto relies on it for safe calculations! Rather make a utility script that does putative annotation separatelly.`
  - Strong warning about critical constraints

- Line 540-541: `logging.debug(f"Fixing corner cases of transcript models containg intron longer than {vcy.LONGEST_INTRON_ALLOWED//1000}Kbp")`
  - Shows awareness of edge cases in biological data

- Line 547-548: `# NOTE: not sure it is needed downstream anymore also not sure it guarantees exactly the same order`
  - Expresses uncertainty about code necessity

- Line 578-580: `# If at least one exon was missing the exon number create the entry for all the others`
  - Shows concern about handling incomplete data

- Line 580: `logging.warning("The entry exon_number was not present in the gtf file. It will be infferred from the position.")`
  - Warning about inferring missing information

- Line 643-644: `if not self.logic.perform_validation_markup:`
  - Shows conditional logic based on validation needs

- Line 653: `# VERBOSE: dump_list = []`
  - Debugging annotation

- Line 694-695: `# VERBOSE: # Look for overlap between the intervals and the read`
  - Debugging annotation

- Line 698-699: `# VERBOSE: import pickle`
  - Debugging annotation

- Line 750: `logging.debug(f"Validated {n_is_intron_valid} introns (of which unique intervals {len(unique_valid)}) out of {n_is_intron} total possible introns (considering each possible transcript models).")`
  - Shows satisfaction with validation results

- Line 765: `logging.warning("The barcode selection mode is off, no cell events will be identified by <80 counts")`
  - Warning about potential data quality issues

- Line 770: `logging.warning(f"{np.sum(tot_mol < 80)} of the barcodes where without cell")`
  - Shows concern about low-quality barcodes

- Line 810: `# NOTE This duplications of method is bad for code mantainance`
  - Expresses frustration with code duplication

- Line 814-815: `# NOTE NOTE!!!! Here I changed the way to sort because it was using the strand causing to skip a lot of reads in SmartSeq2`
  - Shows awareness of a significant bug fix

- Line 826: `# VERBOSE`
  - Debugging annotation

- Line 832-838: Commented out debugging code
  - Shows development process with debug statements

- Line 839: `logging.debug(f"{repeats_reads_count} reads not considered because fully enclosed in repeat masked regions")  # VERBOSE`
  - Debugging annotation

- Line 860-865: Multiple warning messages about molitem failures
  - Shows concern about high failure rates in processing

- Line 961: `# NOTE This duplications of method is bad for code mantainance`
  - Repeated frustration with code duplication

- Line 979: `# VERBOSE`
  - Debugging annotation

- Line 995: `logging.debug(f"{repeats_reads_count} reads not considered because fully enclosed in repeat masked regions")  # VERBOSE`
  - Debugging annotation

- Line 1151-1154: Multiple debug logging statements
  - Shows detailed tracking of read processing

- Line 1260: `raise NotImplementedError("Implement this using multiprocessiong")`
  - Shows awareness of incomplete features

- Line 1265: `raise NotImplementedError("This will be a used by .pcount")`
  - Shows awareness of incomplete features

### Analysis.py
- Line 71: `logging.warn(f"fraction of _Valid cells is {np.mean(self.ca['_Valid'])} but all will be taken in consideration")`
  - Warning about data quality

- Line 259: `logging.debug(f"min_expr_cells is too low for winsorization with upper_perc ={winsor_perc[1]}, upgrading to min_expr_cells ={min_expr_cells}")`
  - Shows adaptive parameter adjustment

- Line 278: `logging.debug(f"svr_gamma set to {svr_gamma}")`
  - Debugging information

- Line 303: `logging.debug(f"min_expr_cells is too low for winsorization with upper_perc ={winsor_perc[1]}, upgrading to min_expr_cells ={min_expr_cells}")`
  - Shows adaptive parameter adjustment

- Line 322: `logging.debug(f"svr_gamma set to {svr_gamma}")`
  - Debugging information

- Line 389: `logging.warning("object does not have the attribute \`small_U_pop\`, so all the unspliced will be normalized by relative size, this might cause the overinflation the unspliced counts of cells where only few unspliced molecules were detected")`
  - Warning about potential issues with normalization

- Line 468: `logging.debug("Filtering by cluster expression")`
  - Debugging information

- Line 472: `logging.debug("Filtering by cv vs mean")`
  - Debugging information

- Line 476: `logging.debug("Filtering by detection level")`
  - Debugging information

- Line 480: `logging.debug("Filtering by custom boolean array")`
  - Debugging information

- Line 483: `logging.debug("Filtering by custom index array")`
  - Debugging information

- Line 488-489: `if hasattr(self, "U_prefilter"):`
  - Shows awareness of object state

- Line 572-573: `with warnings.catch_warnings():`
  - Shows awareness of potential warnings

- Line 580: `# it happened only once but it is here as a precaution`
  - Shows defensive programming approach

- Line 621-622: `with warnings.catch_warnings():`
  - Shows awareness of potential warnings

- Line 628: `# it happened only once but it is here as a precaution`
  - Shows defensive programming approach

- Line 729: `raise ValueError(f"min_perc_U={min_perc_U} corresponds to total Unspliced of 1 molecule of less. Please choose higher value or filter our these cell")`
  - Shows validation of input parameters

- Line 787: `raise ValueError(f"min_perc_U={min_perc_U} corresponds to total Unspliced of 1 molecule of less. Please choose higher value or filter our these cell")`
  - Shows validation of input parameters

- Line 890: `logging.warning("object does not have the attribute \`small_U_pop\`, so all the unspliced will be normalized by relative size, this might cause the overinflation the unspliced counts of cells where only few unspliced molecules were detected")`
  - Repeated warning about normalization issues

- Line 1008-1009: `with warnings.catch_warnings():`
  - Shows awareness of potential warnings

- Line 1017: `# Make a differently named varaible for backwards compatibility`
  - Shows concern for backward compatibility

- Line 1116: `# NOTE This might be not computationally efficient after transpose, maybe better to use csc for the genes`
  - Shows awareness of performance considerations

- Line 1230: `if limit_gamma:`
  - Shows conditional implementation

- Line 1231: `logging.warning("limit_gamma not implemented with this settings")`
  - Shows incomplete feature implementation

- Line 1239: `if limit_gamma:`
  - Shows conditional implementation

- Line 1240: `logging.warning("limit_gamma not implemented with this settings")`
  - Shows incomplete feature implementation

- Line 1254: `if limit_gamma:`
  - Shows conditional implementation

- Line 1255: `logging.warning("limit_gamma not implemented with this settings")`
  - Shows incomplete feature implementation

- Line 1342: `logging.warn("Predicting U without intercept but intercept was previously fit! Set which_offset='q' or 'q_W' ")`
  - Warning about inconsistent parameter usage

- Line 1501: `numba_random_seed(random_seed)`
  - Shows awareness of reproducibility

- Line 1530: `self.corr_calc = "knn_random"`
  - Shows tracking of calculation method

- Line 1574: `logging.debug(f"Correlation Calculation '{self.corr_calc}'")`
  - Debugging information

- Line 1579: `logging.debug(f"Correlation Calculation for negative control")`
  - Debugging information

- Line 1587: `logging.debug(f"Correlation Calculation for negative control")`
  - Debugging information

- Line 1593: `logging.debug(f"Correlation Calculation for negative control")`
  - Debugging information

- Line 1600: `logging.debug(f"Correlation Calculation for negative control")`
  - Debugging information

- Line 1607: `logging.warning("Nans encountered in corrcoef and corrected to 1s. If not identical cells were present it is probably a small isolated cluster converging after imputation.")`
  - Warning about data quality issues

- Line 1611: `logging.warning("Nans encountered in corrcoef_random and corrected to 1s. If not identical cells were present it is probably a small isolated cluster converging after imputation.")`
  - Warning about data quality issues

- Line 1613: `logging.debug(f"Done Correlation Calculation")`
  - Debugging information

- Line 1636: `logging.debug("Correlation Calculation 'full'")`
  - Debugging information

- Line 1641: `logging.debug(f"Correlation Calculation for negative control")`
  - Debugging information

- Line 1649: `logging.debug(f"Correlation Calculation for negative control")`
  - Debugging information

- Line 1655: `logging.debug(f"Correlation Calculation for negative control")`
  - Debugging information

- Line 1662: `logging.debug(f"Correlation Calculation for negative control")`
  - Debugging information

- Line 1908: `logging.warning("DEPRECATION WARNING - the current function is deprecated. Please refer to documentation for default parameters usage")`
  - Shows awareness of deprecated functionality

- Line 1954: `logging.warning("DEPRECATION WARNING - the current function is deprecated. Please refer to documetation for default parameters usage")`
  - Shows awareness of deprecated functionality

- Line 2044-2045: `raise ValueError(""""`scale_type` was set to 'relative' but the randomized control was not computed when running estimate_transition_prob...")`
  - Shows validation of parameter combinations

- Line 2158-2160: `raise ValueError("""`scale_type` was set to 'relative' but the randomized control was not computed when running estimate_transition_prob...")`
  - Shows validation of parameter combinations

- Line 2162: `logging.warning("The arrow scale was set to be 'absolute' make sure you know how to properly interpret the plots")`
  - Warning about advanced usage

### Logic.py
- Line 84: `# NOTE This can be simplified qyuite a bit, without loss of acuracy!`
  - Shows awareness of potential code improvements

- Line 256: `# NOTE This can be simplified qyuite a bit, without loss of acuracy!`
  - Shows awareness of potential code improvements

- Line 421: `# NOTE This can be simplified qyuite a bit, without loss of acuracy!`
  - Shows awareness of potential code improvements

- Line 580: `# NOTE This can be simplified qyuite a bit, without loss of acuracy!`
  - Shows awareness of potential code improvements

- Line 741: `# NOTE This can be simplified qyuite a bit, without loss of acuracy!`
  - Shows awareness of potential code improvements

- Line 893: `# NOTE This can be simplified qyuite a bit, without loss of acuracy!`
  - Shows awareness of potential code improvements

- Line 1113: `# NOTE it does not happen for Smartseq2`
  - Shows awareness of technology-specific behavior

### Feature.py
- Line 32: `# NOTE: pos is diffetent from start, consider chagning`
  - Shows awareness of inconsistent naming

### Transcript_model.py
- Line 32-34: `# NOTE: This should be accessed only after the creation of the transcript model is finished`
  - Shows awareness of object lifecycle constraints

- Line 39-41: `# NOTE: This should be accessed only after the creation of the transcript model is finished`
  - Shows awareness of object lifecycle constraints

- Line 80: `# To avoid that extremelly long intron mask the counting of interal genes`
  - Shows awareness of biological edge cases

### Init.py
- Line 19-21: Comments about a "nasty bug in Anaconda"
  - Shows awareness of external environment issues

- Line 26-37: Multi-line error message about MKL bug
  - Shows concern about critical numerical computation issues

## Design Philosophy and Decisions

### Counter.py
- Line 523: `# NOTE: Don't try to release this constraint, velocyto relies on it for safe calculations! Rather make a utility script that does putative annotation separatelly.`
  - Shows strong commitment to data integrity over flexibility

### Analysis.py
- Line 1908: Deprecation warnings
  - Shows commitment to maintaining backward compatibility while evolving the API

## Debugging and Development Process

### Counter.py
- Multiple "VERBOSE" comments
- Commented out debugging code blocks
- Detailed debug logging throughout the code

### Analysis.py
- Extensive debug logging
- Multiple correlation calculation modes ("knn_random", "full")

## Performance Considerations

### Counter.py
- Line 1116: `# NOTE This might be not computationally efficient after transpose, maybe better to use csc for the genes`
  - Shows awareness of performance implications

### Analysis.py
- Line 1528: `np.random.seed(random_seed)`
  - Shows awareness of reproducibility in stochastic algorithms

## Error Handling and Validation

Throughout the codebase, there are numerous examples of:
- Detailed error messages guiding users to solutions
- Validation of input parameters
- Warnings about potential issues
- Defensive programming practices

## Conclusion

The annotations in the Velocyto codebase reveal a development process characterized by:
1. Careful attention to data quality and integrity
2. Awareness of edge cases in biological data
3. Concern for performance and computational efficiency
4. Commitment to backward compatibility
5. Frustration with external tool inconsistencies
6. Thorough debugging and validation practices
7. Strong emphasis on user guidance through error messages

These annotations provide valuable insights into the challenges faced during development and the thought processes of the authors as they built this complex tool for analyzing RNA velocity in single-cell sequencing data.