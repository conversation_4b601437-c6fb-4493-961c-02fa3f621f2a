<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FeatureIndex Methods and Algorithms</title>
    
    <!-- MathJax 3 for LaTeX rendering -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.8rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 1rem 0 0 0;
            font-size: 1.3rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
            border-radius: 0 8px 8px 0;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.8rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        
        .section h3 {
            color: #34495e;
            margin-top: 2rem;
            font-size: 1.4rem;
        }
        
        .code-box {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            font-size: 0.9rem;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .svg-container {
            text-align: center;
            margin: 2rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-x: auto;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 2px solid #17a2b8;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .success-box {
            background: #d4edda;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .method-box {
            background: white;
            border: 2px solid #3498db;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            position: relative;
        }
        
        .method-name {
            background: #3498db;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            position: absolute;
            top: -15px;
            left: 20px;
        }
        
        .algorithm-step {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 1rem;
            margin: 0.5rem 0;
            border-left: 4px solid #28a745;
        }
        
        .step-number {
            background: #28a745;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
            font-size: 0.9rem;
        }
        
        .complexity-box {
            background: #fff3e0;
            border: 2px solid #f57c00;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .toc {
            background: #34495e;
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .toc h3 {
            margin-top: 0;
            color: #ecf0f1;
        }
        
        .toc ul {
            list-style: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 0.5rem 0;
            padding-left: 1rem;
        }
        
        .toc a {
            color: #3498db;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .toc a:hover {
            color: #5dade2;
        }
        
        .overlap-type {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-weight: bold;
            margin: 0.2rem;
            display: inline-block;
        }
        
        .contains {
            background: #d4edda;
            color: #155724;
            border: 1px solid #28a745;
        }
        
        .start-overlap {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffc107;
        }
        
        .end-overlap {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #dc3545;
        }
        
        .intersects {
            background: #e3f2fd;
            color: #0d47a1;
            border: 1px solid #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 FeatureIndex Methods & Algorithms</h1>
            <p>Deep Dive into Velocyto's Genomic Search Engine</p>
        </div>
        
        <div class="content">
            <!-- Table of Contents -->
            <div class="toc">
                <h3>📚 Table of Contents</h3>
                <ul>
                    <li><a href="#overview">1. Algorithm Overview</a></li>
                    <li><a href="#core-methods">2. Core Methods</a></li>
                    <li><a href="#has-ivls-enclosing">3. has_ivls_enclosing() Algorithm</a></li>
                    <li><a href="#mark-overlapping">4. mark_overlapping_ivls() Algorithm</a></li>
                    <li><a href="#find-overlapping">5. find_overlapping_ivls() Algorithm</a></li>
                    <li><a href="#overlap-detection">6. Overlap Detection Methods</a></li>
                    <li><a href="#optimization">7. Performance Optimization</a></li>
                    <li><a href="#complexity">8. Complexity Analysis</a></li>
                </ul>
            </div>

            <!-- Section 1: Overview -->
            <div class="section" id="overview">
                <h2>1. Algorithm Overview</h2>
                
                <div class="info-box">
                    <strong>🎯 Core Purpose:</strong> FeatureIndex implements efficient algorithms for finding overlaps between sequencing reads and genomic features (exons, introns) using sorted interval search with state preservation.
                </div>
                
                <h3>1.1 Fundamental Algorithm Pattern</h3>
                <div class="svg-container">
                    <svg width="1400" height="300" viewBox="0 0 1400 300">
                        <!-- Background -->
                        <rect width="1400" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
                        
                        <!-- Title -->
                        <text x="700" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Common Algorithm Pattern in All FeatureIndex Methods</text>
                        
                        <!-- Step 1: Skip Past -->
                        <rect x="50" y="70" width="250" height="80" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
                        <text x="175" y="95" text-anchor="middle" font-size="12" font-weight="bold" fill="#1976d2">1. Skip Past Features</text>
                        <text x="60" y="115" font-size="9" fill="#666">while feature.ends_upstream_of(read):</text>
                        <text x="70" y="130" font-size="9" fill="#666">iidx += 1</text>
                        <text x="60" y="145" font-size="9" fill="#666">Move to next feature</text>
                        
                        <!-- Step 2: Local Search -->
                        <rect x="350" y="70" width="250" height="80" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="475" y="95" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">2. Local Search</text>
                        <text x="360" y="115" font-size="9" fill="#666">for segment in read.segments:</text>
                        <text x="370" y="130" font-size="9" fill="#666">i = self.iidx</text>
                        <text x="370" y="145" font-size="9" fill="#666">Search forward from current position</text>
                        
                        <!-- Step 3: Overlap Detection -->
                        <rect x="650" y="70" width="250" height="80" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="775" y="95" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">3. Overlap Detection</text>
                        <text x="660" y="115" font-size="9" fill="#666">while feature.doesnt_start_after(segment):</text>
                        <text x="670" y="130" font-size="9" fill="#666">Check various overlap types</text>
                        <text x="670" y="145" font-size="9" fill="#666">Process matches</text>
                        
                        <!-- Step 4: State Maintenance -->
                        <rect x="950" y="70" width="250" height="80" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="5"/>
                        <text x="1075" y="95" text-anchor="middle" font-size="12" font-weight="bold" fill="#9c27b0">4. State Maintenance</text>
                        <text x="960" y="115" font-size="9" fill="#666">Keep iidx at optimal position</text>
                        <text x="960" y="130" font-size="9" fill="#666">for next read processing</text>
                        <text x="960" y="145" font-size="9" fill="#666">Preserve search state</text>
                        
                        <!-- Flow arrows -->
                        <path d="M 300 110 L 350 110" stroke="#666" stroke-width="3" marker-end="url(#arrow)"/>
                        <path d="M 600 110 L 650 110" stroke="#666" stroke-width="3" marker-end="url(#arrow)"/>
                        <path d="M 900 110 L 950 110" stroke="#666" stroke-width="3" marker-end="url(#arrow)"/>
                        
                        <!-- Key optimization -->
                        <text x="700" y="200" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Key Optimization: State Preservation</text>
                        <text x="700" y="220" text-anchor="middle" font-size="12" fill="#666">iidx maintains position between reads, avoiding re-scanning from beginning</text>
                        <text x="700" y="240" text-anchor="middle" font-size="12" fill="#666">Assumes reads are processed in roughly genomic order (typical for BAM files)</text>
                        <text x="700" y="260" text-anchor="middle" font-size="12" fill="#666">Provides amortized O(1) advancement for sequential reads</text>
                        
                        <!-- Arrow marker -->
                        <defs>
                            <marker id="arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>
                
                <h3>1.2 Core Data Structures</h3>
                <div class="method-box">
                    <div class="method-name">FeatureIndex Attributes</div>
                    <div class="code-box">
class FeatureIndex:
    def __init__(self, ivls: List[Feature] = []):
        self.ivls = ivls                    # Sorted list of Feature objects
        self.ivls.sort()                    # Ensure sorted by genomic position
        self.iidx = 0                       # Current search position index
        self.maxiidx = len(ivls) - 1        # Maximum valid index
    
    @property
    def last_interval_not_reached(self) -> bool:
        return self.iidx < self.maxiidx     # Check if more features available
                    </div>
                </div>
            </div>

            <!-- Section 2: Core Methods -->
            <div class="section" id="core-methods">
                <h2>2. Core Methods</h2>

                <h3>2.1 Method Overview</h3>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem; margin: 1rem 0;">
                    <div class="method-box">
                        <div class="method-name">reset()</div>
                        <p><strong>Purpose:</strong> Reset search position to beginning</p>
                        <div class="code-box">
def reset(self) -> None:
    """Reset current feature to first feature"""
    self.iidx = 0
                        </div>
                        <p><strong>Usage:</strong> Called between batches to restart scanning</p>
                    </div>

                    <div class="method-box">
                        <div class="method-name">last_interval_not_reached</div>
                        <p><strong>Purpose:</strong> Check if more features available</p>
                        <div class="code-box">
@property
def last_interval_not_reached(self) -> bool:
    return self.iidx < self.maxiidx
                        </div>
                        <p><strong>Usage:</strong> Loop control in search algorithms</p>
                    </div>
                </div>

                <h3>2.2 Main Search Methods</h3>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem; margin: 1rem 0;">
                    <div style="background: #e3f2fd; padding: 1rem; border-radius: 8px; text-align: center;">
                        <strong>has_ivls_enclosing()</strong><br>
                        <small>Check if ALL segments are fully contained</small><br>
                        <small>Returns: bool</small>
                    </div>
                    <div style="background: #fff3e0; padding: 1rem; border-radius: 8px; text-align: center;">
                        <strong>mark_overlapping_ivls()</strong><br>
                        <small>Mark introns as validated by spanning reads</small><br>
                        <small>Returns: None (modifies features)</small>
                    </div>
                    <div style="background: #e8f5e8; padding: 1rem; border-radius: 8px; text-align: center;">
                        <strong>find_overlapping_ivls()</strong><br>
                        <small>Generate complete mapping records</small><br>
                        <small>Returns: Dict[TranscriptModel, List[SegmentMatch]]</small>
                    </div>
                </div>
            </div>

            <!-- Section 3: has_ivls_enclosing -->
            <div class="section" id="has-ivls-enclosing">
                <h2>3. has_ivls_enclosing() Algorithm</h2>

                <div class="method-box">
                    <div class="method-name">has_ivls_enclosing(read: Read) → bool</div>
                    <p><strong>Purpose:</strong> Determine if ALL read segments are fully contained within features (used for masking/repeat detection)</p>

                    <h3>3.1 Algorithm Steps</h3>
                    <div class="algorithm-step">
                        <span class="step-number">1</span>
                        <strong>Skip Past Features:</strong> Move iidx forward until finding features that could overlap with the read
                    </div>

                    <div class="algorithm-step">
                        <span class="step-number">2</span>
                        <strong>Check Each Segment:</strong> For each read segment, search for containing features
                    </div>

                    <div class="algorithm-step">
                        <span class="step-number">3</span>
                        <strong>Accumulate Match Types:</strong> Use bitwise OR to combine different overlap types
                    </div>

                    <div class="algorithm-step">
                        <span class="step-number">4</span>
                        <strong>Validate Containment:</strong> Check if all segments have MATCH_INSIDE flag
                    </div>

                    <h3>3.2 Implementation</h3>
                    <div class="code-box">
def has_ivls_enclosing(self, read: Read) -> bool:
    """Check if all read segments are fully contained within features"""

    if len(self.ivls) == 0:
        return False

    # Step 1: Skip past features that end before read starts
    feature = self.ivls[self.iidx]
    while self.last_interval_not_reached and feature.ends_upstream_of(read):
        self.iidx += 1
        feature = self.ivls[self.iidx]

    # Step 2: Check each segment for containment
    for segment in read.segments:
        segment_matchtype = 0  # Initialize match type accumulator
        i = self.iidx
        feature = self.ivls[i]

        # Step 3: Local search for this segment
        while i < self.maxiidx and feature.doesnt_start_after(segment):
            matchtype = 0  # Reset for this feature

            # Check different types of overlaps
            if feature.contains(segment):
                matchtype = MATCH_INSIDE

            if feature.start_overlaps_with_part_of(segment):
                matchtype |= MATCH_OVER5END

            if feature.end_overlaps_with_part_of(segment):
                matchtype |= MATCH_OVER3END

            # Accumulate match types for this segment
            segment_matchtype |= matchtype
            i += 1
            feature = self.ivls[i]

        # Step 4: Check if this segment is NOT fully contained
        if not (segment_matchtype & MATCH_INSIDE):
            return False  # At least one segment not contained

    return True  # All segments are contained
                    </div>

                    <h3>3.3 Visual Algorithm Flow</h3>
                    <div class="svg-container">
                        <svg width="1400" height="350" viewBox="0 0 1400 350">
                            <!-- Background -->
                            <rect width="1400" height="350" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                            <!-- Title -->
                            <text x="700" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">has_ivls_enclosing() Algorithm Flow</text>

                            <!-- Read with segments -->
                            <text x="100" y="70" font-size="12" font-weight="bold" fill="#333">Input: Read with Multiple Segments</text>
                            <rect x="100" y="80" width="60" height="15" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                            <text x="130" y="92" text-anchor="middle" font-size="8" fill="#1976d2">Seg1</text>

                            <rect x="200" y="80" width="60" height="15" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                            <text x="230" y="92" text-anchor="middle" font-size="8" fill="#1976d2">Seg2</text>

                            <!-- Features -->
                            <text x="100" y="130" font-size="12" font-weight="bold" fill="#333">Features to Check Against</text>
                            <rect x="80" y="140" width="100" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="130" y="155" text-anchor="middle" font-size="8" fill="#155724">Feature A</text>

                            <rect x="190" y="140" width="100" height="20" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                            <text x="240" y="155" text-anchor="middle" font-size="8" fill="#856404">Feature B</text>

                            <!-- Match type checking -->
                            <text x="400" y="70" font-size="12" font-weight="bold" fill="#333">Match Type Accumulation</text>

                            <!-- Segment 1 checking -->
                            <rect x="400" y="90" width="200" height="60" fill="white" stroke="#ccc" stroke-width="1"/>
                            <text x="500" y="110" text-anchor="middle" font-size="10" font-weight="bold" fill="#333">Segment 1 Analysis</text>
                            <text x="410" y="130" font-size="9" fill="#666">Feature A: MATCH_INSIDE ✓</text>
                            <text x="410" y="145" font-size="9" fill="#666">segment_matchtype |= MATCH_INSIDE</text>

                            <!-- Segment 2 checking -->
                            <rect x="400" y="170" width="200" height="60" fill="white" stroke="#ccc" stroke-width="1"/>
                            <text x="500" y="190" text-anchor="middle" font-size="10" font-weight="bold" fill="#333">Segment 2 Analysis</text>
                            <text x="410" y="210" font-size="9" fill="#666">Feature B: MATCH_INSIDE ✓</text>
                            <text x="410" y="225" font-size="9" fill="#666">segment_matchtype |= MATCH_INSIDE</text>

                            <!-- Final result -->
                            <rect x="650" y="130" width="200" height="80" fill="#d4edda" stroke="#28a745" stroke-width="2" rx="5"/>
                            <text x="750" y="155" text-anchor="middle" font-size="12" font-weight="bold" fill="#155724">Result: TRUE</text>
                            <text x="660" y="175" font-size="9" fill="#666">All segments have MATCH_INSIDE</text>
                            <text x="660" y="190" font-size="9" fill="#666">Read is fully enclosed</text>
                            <text x="660" y="205" font-size="9" fill="#666">Likely in masked region</text>

                            <!-- Match type legend -->
                            <text x="1000" y="70" font-size="12" font-weight="bold" fill="#333">Match Type Constants</text>
                            <rect x="950" y="80" width="300" height="120" fill="white" stroke="#ccc" stroke-width="1"/>

                            <div class="contains">MATCH_INSIDE</div>
                            <text x="960" y="105" font-size="9" fill="#666">MATCH_INSIDE = 1</text>
                            <text x="960" y="120" font-size="9" fill="#666">MATCH_OVER5END = 2</text>
                            <text x="960" y="135" font-size="9" fill="#666">MATCH_OVER3END = 4</text>

                            <text x="960" y="160" font-size="9" fill="#666" font-weight="bold">Bitwise OR combination:</text>
                            <text x="960" y="175" font-size="9" fill="#666">segment_matchtype |= matchtype</text>
                            <text x="960" y="190" font-size="9" fill="#666">Accumulates all overlap types</text>

                            <!-- Use case -->
                            <text x="700" y="280" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Primary Use Case</text>
                            <text x="700" y="300" text-anchor="middle" font-size="10" fill="#666">Detecting reads that fall entirely within masked/repeat regions</text>
                            <text x="700" y="320" text-anchor="middle" font-size="10" fill="#666">Such reads are typically excluded from velocity analysis</text>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Section 4: mark_overlapping_ivls -->
            <div class="section" id="mark-overlapping">
                <h2>4. mark_overlapping_ivls() Algorithm</h2>

                <div class="method-box">
                    <div class="method-name">mark_overlapping_ivls(read: Read) → None</div>
                    <p><strong>Purpose:</strong> Mark intronic features as validated when reads span exon-intron boundaries (critical for quality control)</p>

                    <h3>4.1 Intron Validation Logic</h3>
                    <div class="warning-box">
                        <strong>⚠️ Critical for Accuracy:</strong> This method prevents false positives by ensuring only introns with evidence of active splicing are used for unspliced read classification.
                    </div>

                    <div class="svg-container">
                        <svg width="1400" height="400" viewBox="0 0 1400 400">
                            <!-- Background -->
                            <rect width="1400" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                            <!-- Title -->
                            <text x="700" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Intron Validation Through Spanning Reads</text>

                            <!-- Gene structure -->
                            <text x="100" y="70" font-size="12" font-weight="bold" fill="#333">Gene Structure</text>
                            <line x1="100" y1="100" x2="600" y2="100" stroke="#333" stroke-width="2"/>

                            <!-- Exons -->
                            <rect x="150" y="90" width="80" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="190" y="105" text-anchor="middle" font-size="9" fill="#155724">Exon 1</text>

                            <rect x="350" y="90" width="80" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="390" y="105" text-anchor="middle" font-size="9" fill="#155724">Exon 2</text>

                            <!-- Intron -->
                            <rect x="240" y="115" width="100" height="15" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                            <text x="290" y="127" text-anchor="middle" font-size="8" fill="#856404">Intron 1</text>

                            <!-- Spanning reads -->
                            <text x="100" y="170" font-size="12" font-weight="bold" fill="#333">Spanning Reads</text>

                            <!-- Left boundary spanning read -->
                            <rect x="210" y="180" width="60" height="12" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
                            <text x="240" y="190" text-anchor="middle" font-size="8" fill="#1976d2">Read 1</text>
                            <text x="240" y="205" text-anchor="middle" font-size="7" fill="#666">Exon1 → Intron1</text>

                            <!-- Right boundary spanning read -->
                            <rect x="320" y="180" width="60" height="12" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2"/>
                            <text x="350" y="190" text-anchor="middle" font-size="8" fill="#9c27b0">Read 2</text>
                            <text x="350" y="205" text-anchor="middle" font-size="7" fill="#666">Intron1 → Exon2</text>

                            <!-- Validation arrows -->
                            <path d="M 240 192 L 290 130" stroke="#e74c3c" stroke-width="2" marker-end="url(#validation-arrow)"/>
                            <path d="M 350 192 L 290 130" stroke="#e74c3c" stroke-width="2" marker-end="url(#validation-arrow)"/>

                            <text x="290" y="145" text-anchor="middle" font-size="10" fill="#e74c3c" font-weight="bold">VALIDATED!</text>

                            <!-- Algorithm steps -->
                            <text x="800" y="70" font-size="12" font-weight="bold" fill="#333">Validation Algorithm</text>
                            <rect x="750" y="80" width="500" height="250" fill="white" stroke="#ccc" stroke-width="1"/>

                            <text x="770" y="105" font-size="10" fill="#666" font-weight="bold">For each read segment:</text>
                            <text x="770" y="125" font-size="9" fill="#666">1. Skip past features that end before read</text>
                            <text x="770" y="140" font-size="9" fill="#666">2. For each feature that could overlap:</text>

                            <text x="790" y="160" font-size="9" fill="#666" font-weight="bold">If feature is intron (kind == ord("i")):</text>

                            <text x="810" y="180" font-size="9" fill="#666" font-weight="bold">Left Boundary Check:</text>
                            <text x="810" y="195" font-size="8" fill="#666">if intron.end_overlaps_with_part_of(segment):</text>
                            <text x="820" y="210" font-size="8" fill="#666">downstream_exon = intron.get_downstream_exon()</text>
                            <text x="820" y="225" font-size="8" fill="#666">if downstream_exon.start_overlaps_with_part_of(segment):</text>
                            <text x="830" y="240" font-size="8" fill="#666">intron.is_validated = True</text>

                            <text x="810" y="260" font-size="9" fill="#666" font-weight="bold">Right Boundary Check:</text>
                            <text x="810" y="275" font-size="8" fill="#666">if intron.start_overlaps_with_part_of(segment):</text>
                            <text x="820" y="290" font-size="8" fill="#666">upstream_exon = intron.get_upstream_exon()</text>
                            <text x="820" y="305" font-size="8" fill="#666">if upstream_exon.end_overlaps_with_part_of(segment):</text>
                            <text x="830" y="320" font-size="8" fill="#666">intron.is_validated = True</text>

                            <!-- Arrow marker -->
                            <defs>
                                <marker id="validation-arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                    <polygon points="0 0, 8 3, 0 6" fill="#e74c3c"/>
                                </marker>
                            </defs>
                        </svg>
                    </div>

                    <h3>4.2 Implementation</h3>
                    <div class="code-box">
def mark_overlapping_ivls(self, read: Read) -> None:
    """Mark intronic features if spanned by exon-intron boundary reads"""

    if len(self.ivls) == 0:
        return

    # Skip past features that end before read starts
    feature = self.ivls[self.iidx]
    while self.last_interval_not_reached and feature.ends_upstream_of(read):
        self.iidx += 1
        feature = self.ivls[self.iidx]

    # Check each read segment
    for n_seg, segment in enumerate(read.segments):
        i = self.iidx
        feature = self.ivls[i]

        # Local search for this segment
        while i < self.maxiidx and feature.doesnt_start_after(segment):

            # Only check introns for validation
            if feature.kind == ord("i"):  # 'i' = intron

                # Left boundary check: intron end → downstream exon start
                if feature.end_overlaps_with_part_of(segment):
                    downstream_exon = feature.get_downstream_exon()
                    if downstream_exon and downstream_exon.start_overlaps_with_part_of(segment):
                        feature.is_validated = True

                # Right boundary check: upstream exon end → intron start
                if feature.start_overlaps_with_part_of(segment):
                    upstream_exon = feature.get_upstream_exon()
                    if upstream_exon and upstream_exon.end_overlaps_with_part_of(segment):
                        feature.is_validated = True

            i += 1
            feature = self.ivls[i]
                    </div>

                    <h3>4.3 Quality Control Impact</h3>
                    <div class="success-box">
                        <strong>✅ Validation Benefits:</strong>
                        <ul>
                            <li><strong>Prevents False Positives:</strong> Eliminates genomic DNA contamination</li>
                            <li><strong>Confirms Active Splicing:</strong> Only counts introns with splicing evidence</li>
                            <li><strong>Improves Accuracy:</strong> Reduces noise in unspliced read counts</li>
                            <li><strong>Quality Metrics:</strong> Validation rate indicates data quality</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Section 5: find_overlapping_ivls -->
            <div class="section" id="find-overlapping">
                <h2>5. find_overlapping_ivls() Algorithm</h2>

                <div class="method-box">
                    <div class="method-name">find_overlapping_ivls(read: Read) → Dict[TranscriptModel, List[SegmentMatch]]</div>
                    <p><strong>Purpose:</strong> Generate complete mapping records linking read segments to genomic features for classification</p>

                    <h3>5.1 Algorithm Overview</h3>
                    <div class="info-box">
                        <strong>🎯 Most Complex Method:</strong> This is the primary method for read-to-feature mapping, including quality control steps to ensure accurate classification.
                    </div>

                    <div class="svg-container">
                        <svg width="1400" height="500" viewBox="0 0 1400 500">
                            <!-- Background -->
                            <rect width="1400" height="500" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                            <!-- Title -->
                            <text x="700" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">find_overlapping_ivls() Complete Algorithm</text>

                            <!-- Step 1: Initial Mapping -->
                            <rect x="50" y="60" width="300" height="100" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
                            <text x="200" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#1976d2">1. Initial Mapping</text>
                            <text x="70" y="105" font-size="9" fill="#666">• Skip past irrelevant features</text>
                            <text x="70" y="120" font-size="9" fill="#666">• For each segment, find overlaps</text>
                            <text x="70" y="135" font-size="9" fill="#666">• Create SegmentMatch objects</text>
                            <text x="70" y="150" font-size="9" fill="#666">• Group by TranscriptModel</text>

                            <!-- Step 2: Quality Control -->
                            <rect x="400" y="60" width="300" height="100" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                            <text x="550" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">2. Quality Control</text>
                            <text x="420" y="105" font-size="9" fill="#666">• Remove suboptimal transcripts</text>
                            <text x="420" y="120" font-size="9" fill="#666">• Keep only max segment matches</text>
                            <text x="420" y="135" font-size="9" fill="#666">• Validate splice junctions</text>
                            <text x="420" y="150" font-size="9" fill="#666">• Check skip_makes_sense</text>

                            <!-- Step 3: Final Output -->
                            <rect x="750" y="60" width="300" height="100" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                            <text x="900" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">3. Final Output</text>
                            <text x="770" y="105" font-size="9" fill="#666">• Clean mapping record</text>
                            <text x="770" y="120" font-size="9" fill="#666">• High-quality matches only</text>
                            <text x="770" y="135" font-size="9" fill="#666">• Ready for classification</text>
                            <text x="770" y="150" font-size="9" fill="#666">• Validated splice patterns</text>

                            <!-- Flow arrows -->
                            <path d="M 350 110 L 400 110" stroke="#666" stroke-width="3" marker-end="url(#flow-arrow)"/>
                            <path d="M 700 110 L 750 110" stroke="#666" stroke-width="3" marker-end="url(#flow-arrow)"/>

                            <!-- Detailed mapping process -->
                            <text x="700" y="200" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Detailed Mapping Process</text>

                            <!-- Input read -->
                            <rect x="100" y="220" width="200" height="60" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                            <text x="200" y="240" text-anchor="middle" font-size="11" font-weight="bold" fill="#1976d2">Input Read</text>
                            <text x="110" y="260" font-size="9" fill="#666">Segments: [(1000, 1050), (1200, 1250)]</text>
                            <text x="110" y="275" font-size="9" fill="#666">is_spliced: True</text>

                            <!-- Feature matching -->
                            <rect x="350" y="220" width="200" height="60" fill="#fff3e0" stroke="#f57c00" stroke-width="1"/>
                            <text x="450" y="240" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">Feature Matching</text>
                            <text x="360" y="260" font-size="9" fill="#666">Seg1 → Exon A (MATCH_INSIDE)</text>
                            <text x="360" y="275" font-size="9" fill="#666">Seg2 → Exon B (MATCH_INSIDE)</text>

                            <!-- SegmentMatch creation -->
                            <rect x="600" y="220" width="200" height="60" fill="#e8f5e8" stroke="#4caf50" stroke-width="1"/>
                            <text x="700" y="240" text-anchor="middle" font-size="11" font-weight="bold" fill="#4caf50">SegmentMatch Objects</text>
                            <text x="610" y="260" font-size="9" fill="#666">SM1: (Seg1, ExonA, True)</text>
                            <text x="610" y="275" font-size="9" fill="#666">SM2: (Seg2, ExonB, True)</text>

                            <!-- Mapping record -->
                            <rect x="850" y="220" width="200" height="60" fill="#f3e5f5" stroke="#9c27b0" stroke-width="1"/>
                            <text x="950" y="240" text-anchor="middle" font-size="11" font-weight="bold" fill="#9c27b0">Mapping Record</text>
                            <text x="860" y="260" font-size="9" fill="#666">TranscriptModel_1: [SM1, SM2]</text>
                            <text x="860" y="275" font-size="9" fill="#666">TranscriptModel_2: [SM1]</text>

                            <!-- Quality control steps -->
                            <text x="700" y="320" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Quality Control Steps</text>

                            <!-- QC Step 1 -->
                            <rect x="200" y="340" width="250" height="60" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                            <text x="325" y="360" text-anchor="middle" font-size="10" font-weight="bold" fill="#856404">Remove Suboptimal</text>
                            <text x="210" y="380" font-size="8" fill="#666">max_segments = 2</text>
                            <text x="210" y="395" font-size="8" fill="#666">Remove TranscriptModel_2 (only 1 segment)</text>

                            <!-- QC Step 2 -->
                            <rect x="500" y="340" width="250" height="60" fill="#f8d7da" stroke="#dc3545" stroke-width="1"/>
                            <text x="625" y="360" text-anchor="middle" font-size="10" font-weight="bold" fill="#721c24">Validate Splicing</text>
                            <text x="510" y="380" font-size="8" fill="#666">Check skip_makes_sense for each SM</text>
                            <text x="510" y="395" font-size="8" fill="#666">Remove if splice pattern invalid</text>

                            <!-- Final result -->
                            <rect x="800" y="340" width="250" height="60" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="925" y="360" text-anchor="middle" font-size="10" font-weight="bold" fill="#155724">Final Result</text>
                            <text x="810" y="380" font-size="8" fill="#666">Clean mapping record</text>
                            <text x="810" y="395" font-size="8" fill="#666">Ready for classification</text>

                            <!-- Arrow marker -->
                            <defs>
                                <marker id="flow-arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                    <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                                </marker>
                            </defs>
                        </svg>
                    </div>

                    <h3>5.2 Implementation</h3>
                    <div class="code-box">
def find_overlapping_ivls(self, read: Read) -> Dict[TranscriptModel, List[SegmentMatch]]:
    """Generate mapping records linking reads to transcript models"""

    mapping_record = defaultdict(list)

    if len(self.ivls) == 0:
        return mapping_record

    # Step 1: Skip past features that end before read starts
    feature = self.ivls[self.iidx]
    while self.last_interval_not_reached and feature.ends_upstream_of(read):
        self.iidx += 1
        feature = self.ivls[self.iidx]

    # Step 2: For each read segment, find overlapping features
    for seg_n, segment in enumerate(read.segments):
        i = self.iidx
        feature = self.ivls[i]

        # Local search for this segment
        while i < self.maxiidx and feature.doesnt_start_after(segment):

            # Check for meaningful overlap (minimum flank size)
            if feature.intersects(segment) and (segment[-1] - segment[0]) > MIN_FLANK:
                # Create SegmentMatch and add to mapping record
                segment_match = SegmentMatch(segment, feature, read.is_spliced)
                mapping_record[feature.transcript_model].append(segment_match)

            i += 1
            feature = self.ivls[i]

    # Step 3: Quality control - remove suboptimal transcript models
    if len(mapping_record) != 0:
        max_n_segments = len(max(mapping_record.values(), key=len))
        for tm, segmatch_list in list(mapping_record.items()):
            if len(segmatch_list) < max_n_segments:
                del mapping_record[tm]

    # Step 4: Quality control - validate splice junctions
    if len(mapping_record) != 0:
        for tm, segmatch_list in list(mapping_record.items()):
            for sm in segmatch_list:
                if not sm.skip_makes_sense:
                    del mapping_record[tm]
                    break

    return mapping_record
                    </div>
                </div>
            </div>

            <!-- Section 6: Overlap Detection -->
            <div class="section" id="overlap-detection">
                <h2>6. Overlap Detection Methods</h2>

                <h3>6.1 Core Overlap Detection Functions</h3>
                <div class="info-box">
                    <strong>🔍 Foundation Methods:</strong> These methods in the Feature class provide the building blocks for all overlap detection algorithms.
                </div>

                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem; margin: 1rem 0;">
                    <div class="method-box">
                        <div class="method-name">contains(segment)</div>
                        <div class="code-box">
def contains(self, segment: Tuple[int, int]) -> bool:
    """Check if segment is fully inside feature"""
    return (self.start <= segment[0] and
            segment[1] <= self.end)
                        </div>
                        <div class="contains">MATCH_INSIDE</div>
                    </div>

                    <div class="method-box">
                        <div class="method-name">intersects(segment)</div>
                        <div class="code-box">
def intersects(self, segment: Tuple[int, int]) -> bool:
    """Check if feature and segment overlap at all"""
    return not (self.end < segment[0] or
                segment[1] < self.start)
                        </div>
                        <div class="intersects">Any Overlap</div>
                    </div>

                    <div class="method-box">
                        <div class="method-name">start_overlaps_with_part_of(segment)</div>
                        <div class="code-box">
def start_overlaps_with_part_of(self, segment: Tuple[int, int]) -> bool:
    """Check if feature start overlaps with segment"""
    return (segment[0] <= self.start <= segment[1])
                        </div>
                        <div class="start-overlap">MATCH_OVER5END</div>
                    </div>

                    <div class="method-box">
                        <div class="method-name">end_overlaps_with_part_of(segment)</div>
                        <div class="code-box">
def end_overlaps_with_part_of(self, segment: Tuple[int, int]) -> bool:
    """Check if feature end overlaps with segment"""
    return (segment[0] <= self.end <= segment[1])
                        </div>
                        <div class="end-overlap">MATCH_OVER3END</div>
                    </div>
                </div>

                <h3>6.2 Overlap Type Visualization</h3>
                <div class="svg-container">
                    <svg width="1400" height="400" viewBox="0 0 1400 400">
                        <!-- Background -->
                        <rect width="1400" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="700" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Types of Feature-Segment Overlaps</text>

                        <!-- Feature representation -->
                        <rect x="200" y="70" width="200" height="30" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
                        <text x="300" y="90" text-anchor="middle" font-size="12" fill="#1976d2" font-weight="bold">Genomic Feature</text>

                        <!-- Type 1: Contains -->
                        <text x="50" y="140" font-size="14" font-weight="bold" fill="#28a745">1. CONTAINS</text>
                        <rect x="200" y="150" width="200" height="20" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <rect x="250" y="155" width="100" height="10" fill="#d4edda" stroke="#28a745" stroke-width="2"/>
                        <text x="300" y="190" text-anchor="middle" font-size="10" fill="#666">feature.contains(segment)</text>
                        <text x="300" y="205" text-anchor="middle" font-size="10" fill="#666">Segment fully inside feature</text>

                        <!-- Type 2: Start Overlap -->
                        <text x="450" y="140" font-size="14" font-weight="bold" fill="#f57c00">2. START OVERLAP</text>
                        <rect x="600" y="150" width="200" height="20" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <rect x="550" y="155" width="100" height="10" fill="#fff3cd" stroke="#f57c00" stroke-width="2"/>
                        <text x="700" y="190" text-anchor="middle" font-size="10" fill="#666">feature.start_overlaps_with_part_of(segment)</text>
                        <text x="700" y="205" text-anchor="middle" font-size="10" fill="#666">Feature start within segment</text>

                        <!-- Type 3: End Overlap -->
                        <text x="50" y="260" font-size="14" font-weight="bold" fill="#dc3545">3. END OVERLAP</text>
                        <rect x="200" y="270" width="200" height="20" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <rect x="350" y="275" width="100" height="10" fill="#f8d7da" stroke="#dc3545" stroke-width="2"/>
                        <text x="300" y="310" text-anchor="middle" font-size="10" fill="#666">feature.end_overlaps_with_part_of(segment)</text>
                        <text x="300" y="325" text-anchor="middle" font-size="10" fill="#666">Feature end within segment</text>

                        <!-- Type 4: General Intersect -->
                        <text x="450" y="260" font-size="14" font-weight="bold" fill="#1976d2">4. INTERSECTS</text>
                        <rect x="600" y="270" width="200" height="20" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <rect x="550" y="275" width="300" height="10" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
                        <text x="700" y="310" text-anchor="middle" font-size="10" fill="#666">feature.intersects(segment)</text>
                        <text x="700" y="325" text-anchor="middle" font-size="10" fill="#666">Any overlap at all</text>

                        <!-- Bitwise combination -->
                        <text x="1000" y="140" font-size="12" font-weight="bold" fill="#333">Bitwise Combination</text>
                        <rect x="950" y="150" width="300" height="180" fill="white" stroke="#ccc" stroke-width="1"/>

                        <text x="970" y="175" font-size="10" fill="#666">Match types can be combined:</text>
                        <text x="970" y="195" font-size="9" font-family="monospace" fill="#666">matchtype = 0</text>
                        <text x="970" y="210" font-size="9" font-family="monospace" fill="#666">if feature.contains(segment):</text>
                        <text x="980" y="225" font-size="9" font-family="monospace" fill="#666">matchtype = MATCH_INSIDE</text>
                        <text x="970" y="245" font-size="9" font-family="monospace" fill="#666">if feature.start_overlaps_with_part_of(segment):</text>
                        <text x="980" y="260" font-size="9" font-family="monospace" fill="#666">matchtype |= MATCH_OVER5END</text>
                        <text x="970" y="280" font-size="9" font-family="monospace" fill="#666">if feature.end_overlaps_with_part_of(segment):</text>
                        <text x="980" y="295" font-size="9" font-family="monospace" fill="#666">matchtype |= MATCH_OVER3END</text>

                        <text x="970" y="320" font-size="9" fill="#666" font-weight="bold">Result: Combined match type</text>
                    </svg>
                </div>

                <h3>6.3 Optimization Helper Methods</h3>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem; margin: 1rem 0;">
                    <div class="method-box">
                        <div class="method-name">ends_upstream_of(read)</div>
                        <div class="code-box">
def ends_upstream_of(self, read: Read) -> bool:
    """Check if feature ends before read starts"""
    return self.end < read.pos
                        </div>
                        <p><strong>Usage:</strong> Skip past irrelevant features</p>
                    </div>

                    <div class="method-box">
                        <div class="method-name">doesnt_start_after(segment)</div>
                        <div class="code-box">
def doesnt_start_after(self, segment: Tuple[int, int]) -> bool:
    """Check if feature could overlap with segment"""
    return self.start <= segment[1]
                        </div>
                        <p><strong>Usage:</strong> Early termination in local search</p>
                    </div>
                </div>
            </div>

            <!-- Section 7: Complexity Analysis -->
            <div class="section" id="complexity">
                <h2>8. Complexity Analysis</h2>

                <h3>8.1 Time Complexity</h3>
                <div class="complexity-box">
                    <strong>📊 Algorithm Complexity Analysis:</strong>
                    <ul>
                        <li><strong>Naive Approach:</strong> O(n × m) - check every read against every feature</li>
                        <li><strong>FeatureIndex Approach:</strong> O(n × log m) - sorted features with smart scanning</li>
                        <li><strong>Amortized Performance:</strong> O(n) for sequential reads due to state preservation</li>
                    </ul>
                </div>

                <div class="svg-container">
                    <svg width="1400" height="300" viewBox="0 0 1400 300">
                        <!-- Background -->
                        <rect width="1400" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="700" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Performance Comparison</text>

                        <!-- Y-axis -->
                        <line x1="100" y1="60" x2="100" y2="250" stroke="#333" stroke-width="2"/>
                        <text x="80" y="155" text-anchor="middle" font-size="10" fill="#666" transform="rotate(-90 80 155)">Time (log scale)</text>

                        <!-- X-axis -->
                        <line x1="100" y1="250" x2="1200" y2="250" stroke="#333" stroke-width="2"/>
                        <text x="650" y="270" text-anchor="middle" font-size="10" fill="#666">Number of Features (m)</text>

                        <!-- Performance curves -->
                        <path d="M 150 200 Q 400 150 650 100 Q 900 80 1150 60" stroke="#dc3545" stroke-width="3" fill="none"/>
                        <text x="800" y="90" font-size="12" fill="#dc3545" font-weight="bold">O(n×m) - Naive</text>

                        <path d="M 150 230 Q 400 210 650 200 Q 900 195 1150 190" stroke="#28a745" stroke-width="3" fill="none"/>
                        <text x="800" y="180" font-size="12" fill="#28a745" font-weight="bold">O(n×log m) - FeatureIndex</text>

                        <path d="M 150 240 Q 400 235 650 232 Q 900 230 1150 228" stroke="#1976d2" stroke-width="3" fill="none"/>
                        <text x="800" y="220" font-size="12" fill="#1976d2" font-weight="bold">O(n) - Amortized (sequential reads)</text>

                        <!-- Performance metrics -->
                        <rect x="200" y="60" width="1000" height="40" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="700" y="85" text-anchor="middle" font-size="12" fill="#666">Human genome: ~450,000 features per chromosome, ~100M reads typical</text>
                    </svg>
                </div>

                <h3>8.2 Space Complexity</h3>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem; margin: 1rem 0;">
                    <div style="background: #e3f2fd; padding: 1rem; border-radius: 8px;">
                        <strong>Feature Storage</strong><br>
                        <small>O(m) - Linear in number of features<br>
                        ~100-200 bytes per feature<br>
                        Human genome: ~3-6 GB total</small>
                    </div>
                    <div style="background: #fff3e0; padding: 1rem; border-radius: 8px;">
                        <strong>Search State</strong><br>
                        <small>O(1) - Constant space<br>
                        Single iidx pointer<br>
                        Minimal memory overhead</small>
                    </div>
                    <div style="background: #e8f5e8; padding: 1rem; border-radius: 8px;">
                        <strong>Output Records</strong><br>
                        <small>O(k) - Linear in matches<br>
                        SegmentMatch objects<br>
                        Typically small per read</small>
                    </div>
                </div>

                <h3>8.3 Key Optimizations</h3>
                <div class="success-box">
                    <strong>✅ Performance Optimizations:</strong>
                    <ul>
                        <li><strong>Sorted Features:</strong> Enable binary-search-like behavior</li>
                        <li><strong>State Preservation:</strong> iidx avoids re-scanning from beginning</li>
                        <li><strong>Early Termination:</strong> Stop when no more overlaps possible</li>
                        <li><strong>Minimum Flank:</strong> Filter spurious short overlaps</li>
                        <li><strong>Quality Control:</strong> Remove suboptimal mappings efficiently</li>
                        <li><strong>Batch Reset:</strong> Optimal state management between batches</li>
                    </ul>
                </div>
            </div>

            <!-- Summary -->
            <div class="section">
                <h2>Summary</h2>

                <div class="highlight" style="display: block; text-align: center; padding: 1rem; margin: 2rem 0; font-size: 1.1rem;">
                    🎯 <strong>Algorithm Excellence:</strong> FeatureIndex implements sophisticated algorithms that balance simplicity with performance, providing the foundation for accurate and efficient genomic interval searches in RNA velocity analysis.
                </div>

                <h3>Method Summary</h3>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem; margin: 1rem 0;">
                    <div style="background: #e3f2fd; padding: 1rem; border-radius: 8px; text-align: center;">
                        <strong>has_ivls_enclosing()</strong><br>
                        <small>Masking detection<br>O(log m) per read</small>
                    </div>
                    <div style="background: #fff3e0; padding: 1rem; border-radius: 8px; text-align: center;">
                        <strong>mark_overlapping_ivls()</strong><br>
                        <small>Intron validation<br>O(log m) per read</small>
                    </div>
                    <div style="background: #e8f5e8; padding: 1rem; border-radius: 8px; text-align: center;">
                        <strong>find_overlapping_ivls()</strong><br>
                        <small>Complete mapping<br>O(log m) + QC per read</small>
                    </div>
                </div>

                <h3>Technical Excellence</h3>
                <div class="info-box">
                    <strong>🏗️ Design Highlights:</strong>
                    <ul>
                        <li><strong>Unified Pattern:</strong> All methods follow the same optimization strategy</li>
                        <li><strong>State Preservation:</strong> Amortized O(1) advancement for sequential reads</li>
                        <li><strong>Quality Control:</strong> Multiple validation steps ensure accuracy</li>
                        <li><strong>Flexible Overlap Detection:</strong> Supports various overlap types with bitwise combination</li>
                        <li><strong>Memory Efficient:</strong> Minimal overhead beyond feature storage</li>
                        <li><strong>Production Ready:</strong> Handles edge cases and provides robust error handling</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
