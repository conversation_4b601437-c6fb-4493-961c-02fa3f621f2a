<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Velocyto Intron Validation Explained</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        h1 {
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            border-bottom: 1px solid #bdc3c7;
            padding-bottom: 5px;
            margin-top: 30px;
        }
        code {
            background-color: #ecf0f1;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .explanation {
            background-color: #e8f4fc;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
        }
        .important {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
        }
        .example {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 20px 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>Velocyto Intron Validation Explained</h1>
    
    <div class="explanation">
        <p><strong>Intron validation</strong> is a critical quality control mechanism in Velocyto that helps distinguish true introns from other genomic regions that might accidentally align to reads. This process ensures that only genuine introns are counted in the unspliced category, reducing false positives in RNA velocity analysis.</p>
    </div>

    <h2>How Intron Validation Works</h2>
    
    <h3>1. Purpose of Intron Validation</h3>
    <ul>
        <li>Distinguishes true introns from other genomic regions that might accidentally align to reads</li>
        <li>Ensures that only genuine introns are counted in the unspliced category</li>
        <li>Reduces false positives in RNA velocity analysis</li>
        <li>Provides confidence measure for intron annotations</li>
    </ul>
    
    <h3>2. The <code>is_validated</code> Flag</h3>
    <p>The core of intron validation in Velocyto is the <code>is_validated</code> flag in the <code>Feature</code> class:</p>
    
    <pre><code>class Feature:
    """A simple class representing an annotated genomic feature (e.g. exon, intron, masked repeat)"""
    __slots__ = ["start", "end", "kind", "exin_no", "is_validated", "transcript_model"]
    
    def __init__(self, start: int, end: int, kind: int, exin_no: str, transcript_model: Any=None) -> None:
        self.start = start
        self.end = end
        self.transcript_model = transcript_model
        self.kind = kind  # it should be ord("e"), ord("i"), ord("m"), ....
        self.exin_no = int(exin_no)
        self.is_validated = False  # Initially all introns are not validated
</code></pre>
    
    <h3>3. Validation Process</h3>
    <p>Intron validation occurs in two main steps:</p>
    
    <h4>Step 1: Markup Phase (Intron Validation)</h4>
    <p>During the markup phase, Velocyto scans through all non-spliced reads to identify reads that span exon-intron boundaries:</p>
    
    <pre><code>def mark_overlapping_ivls(self, read: vcy.Read) -> None:
    """Finds the overlap between Read and Features and mark intronic features if spanned
    
    Args
    ----
    read: vcy.Read
        the read object to be analyzed
    
    Returns
    -------
    Nothing, it marks the vcy.Feature object (is_validated = True) if there is evidence of exon-intron spanning
    """
    
    # Loop through the mapping segments of a read
    for n_seg, segment in enumerate(read.segments):
        # Local search for each segment
        i = self.iidx
        feature = self.ivls[self.iidx]
        while i < self.maxiidx and feature.doesnt_start_after(segment):
            if feature.kind == ord("i"):  # Check if feature is an intron
                # Check if read overlaps with downstream exon
                if feature.end_overlaps_with_part_of(segment):
                    downstream_exon = feature.get_downstream_exon()
                    if downstream_exon.start_overlaps_with_part_of(segment):
                        feature.is_validated = True  # VALIDATE the intron
                
                # Check if read overlaps with upstream exon
                if feature.start_overlaps_with_part_of(segment):
                    upstream_exon = feature.get_upstream_exon()
                    if upstream_exon.end_overlaps_with_part_of(segment):
                        feature.is_validated = True  # VALIDATE the intron
            # Move to the next interval
            i += 1
            feature = self.ivls[i]
</code></pre>
    
    <h4>Step 2: Counting Phase (Using Validation)</h4>
    <p>During the counting phase, the <code>is_validated</code> flag is used to make informed decisions about how to classify reads:</p>
    
    <pre><code>def count(self, molitem: vcy.Molitem, cell_bcidx: int, dict_layers_columns: Dict[str, np.ndarray], geneid2ix: Dict[str, int]) -> int:
    # Analyze mapping patterns for single-gene reads
    has_validated_intron = False  # Track if we find validated introns
    has_spanning = False          # Track if we find exon-intron spanning
    has_only_exons = True         # Assume exon-only until proven otherwise
    has_introns = False           # Track if we find any introns
    
    # Analyze each transcript model
    for transcript_model, segments_list in molitem.mappings_record.items():
        # Analyze each segment
        for segment_match in segments_list:
            if segment_match.maps_to_intron:
                has_introns = True
                has_only_exons = False
                
                # THIS IS WHERE is_validated IS USED!
                if segment_match.feature.is_validated:
                    # This intron has been confirmed by spanning reads
                    has_validated_intron = True
                    
                    # Check if this specific read spans exon-intron boundaries
                    if (segment_match.feature.end_overlaps_with_part_of(segment_match.segment) or
                        segment_match.feature.start_overlaps_with_part_of(segment_match.segment)):
                        has_spanning = True
    
    # Apply classification rules using is_validated
    # Exon-only reads → SPLICED
    if has_only_exons and not has_introns:
        gene_ix = geneid2ix[transcript_model.geneid]
        spliced[gene_ix, cell_bcidx] += 1
        return 0
        
    # Exon-intron spanning reads → UNSPLICED
    elif has_spanning:
        gene_ix = geneid2ix[transcript_model.geneid]
        unspliced[gene_ix, cell_bcidx] += 1
        return 0
        
    # Validated introns (confirmed by OTHER reads) → UNSPLICED
    elif has_validated_intron and not has_spanning:
        gene_ix = geneid2ix[transcript_model.geneid]
        unspliced[gene_ix, cell_bcidx] += 1
        return 0
        
    # Other intron mappings → UNSPLICED (per permissive logic)
    elif has_introns and not has_validated_intron and not has_spanning:
        gene_ix = geneid2ix[transcript_model.geneid]
        unspliced[gene_ix, cell_bcidx] += 1
        return 0
</code></pre>
    
    <h3>4. Validation Criteria</h3>
    <p>A read spans an exon-intron boundary if it overlaps with BOTH:</p>
    <ol>
        <li>An intron feature</li>
        <li>A neighboring exon feature</li>
    </ol>
    
    <p>This is detected using the overlap functions:</p>
    <ul>
        <li><code>end_overlaps_with_part_of()</code></li>
        <li><code>start_overlaps_with_part_of()</code></li>
    </ul>
    
    <pre><code>def end_overlaps_with_part_of(self, segment: Tuple[int, int], minimum_flanking: int=vcy.MIN_FLANK) -> bool:
    """The following situation happens
    
                                  *---|||segment||---*
            *|||||||||||||Ivl||||||||||||||||*
    
    where `---` indicates the minimum flanking
    """
    return (segment[0] + minimum_flanking < self.end) and (segment[-1] - minimum_flanking > self.end)
    
def start_overlaps_with_part_of(self, segment: Tuple[int, int], minimum_flanking: int=vcy.MIN_FLANK) -> bool:
    """The following situation happens
    
      *---|||segment||---*
            *|||||||||||||Ivl||||||||||||||||*
    
    where `---` indicates the minimum flanking
    """
    return (segment[0] + minimum_flanking < self.start) and (segment[-1] - minimum_flanking > self.start)
</code></pre>
    
    <h3>5. Example Scenarios</h3>
    
    <div class="example">
        <h4>Scenario 1: Confirmed Intron</h4>
        <p>Read: <code>---------********************------------------</code></p>
        <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|</p>
        <p>Exon: <code>---------*</code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<code>*------------------</code></p>
        <p>Intron: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<code>******************</code></p>
        <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|</p>
        <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Read validates this intron (<code>is_validated = True</code>)</p>
    </div>
    
    <div class="example">
        <h4>Scenario 2: Unconfirmed Intron</h4>
        <p>Read: <code>-----------------------******</code></p>
        <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp;|</p>
        <p>Exon: <code>--------***************</code>&nbsp;&nbsp;&nbsp;&nbsp;|</p>
        <p>Intron: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<code>***********</code> (<code>is_validated = False</code>)</p>
        <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|</p>
        <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Read does not span boundary</p>
    </div>
    
    <div class="example">
        <h4>Scenario 3: Spanning Read (Validates Intron)</h4>
        <p>Read: <code>----*********************---------------------</code></p>
        <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|</p>
        <p>Exon: <code>----*</code>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|</p>
        <p>Intron: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<code>*****************</code>|&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|</p>
        <p>Exon: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<code>*---------------------</code></p>
        <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|</p>
        <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Read spans both features</p>
        <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Intron becomes validated (<code>is_validated = True</code>)</p>
        <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This read is classified as UNSPLICED</p>
    </div>
    
    <h3>6. Key Functions for Validation</h3>
    
    <h4>Feature.get_downstream_exon()</h4>
    <pre><code>def get_downstream_exon(self) -> Any:
    """To use only for introns. Returns the vcy.Feature corresponding to the neighbour exon downstream"""
    if self.transcript_model.chromstrand[-1] == "+":
        ix = self.exin_no * 2
    else:
        # in the case on strand -
        ix = len(self.transcript_model.list_features) - 2 * self.exin_no + 1
    return self.transcript_model.list_features[ix]
</code></pre>
    
    <h4>Feature.get_upstream_exon()</h4>
    <pre><code>def get_upstream_exon(self) -> Any:
    """To use only for introns. Returns the vcy.Feature corresponding to the neighbour exon downstream"""
    if self.transcript_model.chromstrand[-1] == "+":
        ix = (self.exin_no * 2) - 2
    else:
        # in the case on strand -
        ix = len(self.transcript_model.list_features) - 2 * self.exin_no - 1
    return self.transcript_model.list_features[ix]
</code></pre>
    
    <h3>7. Benefits of Intron Validation</h3>
    <ul>
        <li>Improves accuracy of spliced/unspliced quantification</li>
        <li>Reduces noise from misaligned reads</li>
        <li>Provides confidence measure for intron annotations</li>
        <li>Helps distinguish true pre-mRNA molecules from genomic DNA contamination</li>
    </ul>
    
    <h3>8. Usage in Classification Logic</h3>
    <p>Different classification logics in Velocyto use the <code>is_validated</code> flag in various ways:</p>
    
    <table>
        <tr>
            <th>Logic</th>
            <th>Non-validated Introns</th>
            <th>Validated Introns</th>
        </tr>
        <tr>
            <td>Permissive10X</td>
            <td>Counted as unspliced</td>
            <td>Counted as unspliced</td>
        </tr>
        <tr>
            <td>Intermediate10X</td>
            <td>Singletons discarded, non-singletons counted</td>
            <td>Counted as unspliced</td>
        </tr>
        <tr>
            <td>ValidatedIntrons10X</td>
            <td>Discarded</td>
            <td>Counted as unspliced</td>
        </tr>
        <tr>
            <td>Stricter10X</td>
            <td>Discarded</td>
            <td>Singletons discarded, non-singletons counted</td>
        </tr>
    </table>
    
    <div class="important">
        <h3>Important Notes</h3>
        <ul>
            <li>Initially, all introns have <code>is_validated = False</code></li>
            <li>During analysis, Velocyto looks for reads that span exon-intron boundaries</li>
            <li>When such reads are found, the corresponding intron's <code>is_validated</code> flag is set to <code>True</code></li>
            <li>This validation can come from the same read or from other reads in the dataset</li>
            <li>Reads mapping to validated introns are confidently classified as unspliced</li>
        </ul>
    </div>
    
    <h2>Conclusion</h2>
    <p>Intron validation is a sophisticated mechanism in Velocyto that significantly improves the accuracy of RNA velocity analysis. By requiring evidence of exon-intron spanning reads to validate introns, Velocyto reduces false positives from genomic DNA contamination or mapping artifacts. The <code>is_validated</code> flag serves as a quality control measure that helps ensure only genuine introns are used for unspliced read classification, leading to more reliable RNA velocity estimates.</p>
</body>
</html>