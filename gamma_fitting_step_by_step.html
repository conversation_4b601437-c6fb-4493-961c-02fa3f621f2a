<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Velocyto Gamma Fitting - Step by Step</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        header {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            margin: 0;
            font-size: 2.5rem;
        }
        h2 {
            color: #2575fc;
            border-bottom: 2px solid #2575fc;
            padding-bottom: 0.5rem;
            margin-top: 2rem;
        }
        h3 {
            color: #6a11cb;
            margin-top: 1.5rem;
        }
        .section {
            background: white;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .code-block {
            background: #2b2b2b;
            color: #f8f8f2;
            padding: 1rem;
            border-radius: 5px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
        }
        .explanation {
            background: #e8f4fc;
            padding: 1rem;
            border-radius: 5px;
            margin: 1rem 0;
        }
        .parameter {
            background: #e8f5e9;
            padding: 0.5rem;
            border-left: 4px solid #4caf50;
            margin: 0.5rem 0;
        }
        .step {
            background: #fff3e0;
            padding: 1rem;
            border-radius: 5px;
            margin: 1rem 0;
            border-left: 4px solid #ff9800;
        }
        .function-ref {
            background: #fce4ec;
            padding: 0.5rem;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .conclusion {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 1.5rem;
            border-radius: 8px;
            margin-top: 2rem;
        }
        code {
            background: #f1f1f1;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .highlight {
            background: #fff9c4;
            padding: 0.2rem;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <header>
        <h1>Velocyto Gamma Fitting</h1>
        <p>Step-by-Step Breakdown of the fit_gammas Method</p>
    </header>

    <div class="section">
        <h2>Overview</h2>
        <p>The <code>fit_gammas</code> method in velocyto is responsible for estimating the degradation/splicing rate (gamma) for each gene by fitting a linear relationship between spliced (S) and unspliced (U) RNA molecules. This is a crucial step in RNA velocity analysis.</p>
        
        <div class="explanation">
            <h3>Mathematical Foundation</h3>
            <p>The method fits the relationship:</p>
            <div style="text-align: center; padding: 1rem;">
                \(U = \gamma \cdot S + q\)
            </div>
            <p>Where:</p>
            <ul>
                <li>\(U\): Unspliced RNA abundance</li>
                <li>\(S\): Spliced RNA abundance</li>
                <li>\(\gamma\): Degradation/splicing rate (the gamma we're fitting)</li>
                <li>\(q\): Offset/y-intercept (optional)</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <h2>Method Signature and Parameters</h2>
        <div class="code-block">
def fit_gammas(self, steady_state_bool: np.ndarray=None, use_imputed_data: bool=True, use_size_norm: bool=True,
               fit_offset: bool=True, fixperc_q: bool=False, weighted: bool=True, weights: np.ndarray = "maxmin_diag",
               limit_gamma: bool=False, maxmin_perc: List[float]=[2, 98], maxmin_weighted_pow: float=15) -> None:
        </div>
        
        <h3>Parameter Explanations</h3>
        <div class="parameter">
            <strong>steady_state_bool</strong>: If provided, only these cells are used for fitting (assumed to be in steady state)
        </div>
        <div class="parameter">
            <strong>use_imputed_data</strong>: Whether to use k-nearest neighbors smoothed data (Sx, Ux) or raw data (S, U)
        </div>
        <div class="parameter">
            <strong>use_size_norm</strong>: Whether to use size-normalized data (S_sz, U_sz, Sx_sz, Ux_sz) or raw counts
        </div>
        <div class="parameter">
            <strong>fit_offset</strong>: Whether to fit with an offset term (y-intercept)
        </div>
        <div class="parameter">
            <strong>fixperc_q</strong>: When not fitting offset, whether to fix it to a lower percentile of unspliced data
        </div>
        <div class="parameter">
            <strong>weighted</strong>: Whether to use weighted least squares for fitting
        </div>
        <div class="parameter">
            <strong>weights</strong>: Method for determining weights ("maxmin_diag", "maxmin", "sum", "prod", "maxmin_weighted", or custom array)
        </div>
        <div class="parameter">
            <strong>limit_gamma</strong>: Whether to constrain gamma values when unspliced is much higher than spliced
        </div>
        <div class="parameter">
            <strong>maxmin_perc</strong>: Percentiles used for "maxmin" weighting methods
        </div>
        <div class="parameter">
            <strong>maxmin_weighted_pow</strong>: Power parameter for "maxmin_weighted" weighting method
        </div>
    </div>

    <div class="section">
        <h2>Step-by-Step Breakdown</h2>
        
        <div class="step">
            <h3>Step 1: Setting up the steady state cells</h3>
            <div class="code-block">
if steady_state_bool:
    self.steady_state = steady_state_bool
else:
    self.steady_state = np.ones(self.S.shape[1], dtype=bool)
            </div>
            <p>This determines which cells to use for fitting. If no specific cells are provided, all cells are assumed to be in steady state.</p>
        </div>
        
        <div class="step">
            <h3>Step 2: Selecting the data source</h3>
            <div class="code-block">
if use_imputed_data:
    if use_size_norm:
        tmpS = self.Sx_sz
        tmpU = self.Ux_sz
    else:
        tmpS = self.Sx
        tmpU = self.Ux
else:
    if use_size_norm:
        tmpS = self.S_sz
        tmpU = self.U_sz
    else:
        tmpS = self.S
        tmpU = self.U
            </div>
            <p>Based on the parameters, the method selects which data to use for fitting:</p>
            <ul>
                <li><code>Sx_sz</code>, <code>Ux_sz</code>: Size-normalized, kNN-smoothed data</li>
                <li><code>Sx</code>, <code>Ux</code>: kNN-smoothed data (without size normalization)</li>
                <li><code>S_sz</code>, <code>U_sz</code>: Size-normalized, raw data</li>
                <li><code>S</code>, <code>U</code>: Raw count data</li>
            </ul>
        </div>
        
        <div class="step">
            <h3>Step 3: Computing weights (if weighted fitting is enabled)</h3>
            <div class="code-block">
if weighted:
    if type(weights) is np.ndarray:
        W = weights
    elif weights == "sum":
        W = (tmpS / np.percentile(tmpS, 99, 1)[:, None]) + (tmpU / np.percentile(tmpU, 99, 1)[:, None])
    elif weights == "prod":
        W = (tmpS / np.percentile(tmpS, 99, 1)[:, None]) * (tmpU / np.percentile(tmpU, 99, 1)[:, None])
    # ... (other weighting methods)
            </div>
            <p>If weighted fitting is enabled, the method computes weights for each cell. Several weighting schemes are supported:</p>
            
            <h4>Weighting Methods:</h4>
            <div class="explanation">
                <p><strong>"sum"</strong>: Weight is sum of normalized spliced and unspliced</p>
                <p><strong>"prod"</strong>: Weight is product of normalized spliced and unspliced</p>
                <p><strong>"maxmin"</strong>: Weight is 1 for cells in top/bottom percentiles, 0 otherwise</p>
                <p><strong>"maxmin_diag"</strong>: More complex weighting based on normalized data</p>
                <p><strong>"maxmin_weighted"</strong>: Smooth weighting using power functions</p>
                <p><strong>"maxmin_double"</strong>: Combination of two maxmin approaches</p>
                <p><strong>Custom array</strong>: User-provided weights</p>
            </div>
        </div>
        
        <div class="step">
            <h3>Step 4: Performing the actual fitting</h3>
            <p>Based on the parameters, different fitting functions are called:</p>
            
            <h4>Case 1: Fitting with offset (fit_offset=True)</h4>
            <div class="code-block">
if weighted:
    self.gammas, self.q, self.R2 = fit_slope_weighted_offset(tmpU[:, self.steady_state],
                                                             tmpS[:, self.steady_state],
                                                             W,
                                                             return_R2=True,
                                                             limit_gamma=limit_gamma)
else:
    self.gammas, self.q = fit_slope_offset(tmpU[:, self.steady_state],
                                           tmpS[:, self.steady_state])
            </div>
            
            <h4>Case 2: Fixing offset to percentile (fixperc_q=True)</h4>
            <div class="code-block">
if weighted:
    self.gammas, self.q = fit_slope_weighted_offset(tmpU[:, self.steady_state],
                                                    tmpS[:, self.steady_state],
                                                    W, fixperc_q=True, limit_gamma=limit_gamma)
else:
    self.gammas, self.q = fit_slope_offset(tmpU[:, self.steady_state],
                                           tmpS[:, self.steady_state],
                                           fixperc_q=True)
            </div>
            
            <h4>Case 3: No offset (both fit_offset and fixperc_q are False)</h4>
            <div class="code-block">
if weighted:
    self.gammas, self.R2 = fit_slope_weighted(tmpU[:, self.steady_state],
                                              tmpS[:, self.steady_state],
                                              W, 
                                              return_R2=True,
                                              limit_gamma=limit_gamma)
    self.q = np.zeros_like(self.gammas)
else:
    self.gammas = fit_slope(tmpU[:, self.steady_state],
                            tmpS[:, self.steady_state])
    self.q = np.zeros_like(self.gammas)
            </div>
        </div>
        
        <div class="step">
            <h3>Step 5: Post-processing gamma values</h3>
            <div class="code-block">
# Fix gammas
self.gammas[~np.isfinite(self.gammas)] = 0
            </div>
            <p>Non-finite gamma values (NaN or infinity) are set to 0 to avoid issues in downstream calculations.</p>
        </div>
    </div>

    <div class="section">
        <h2>Underlying Fitting Functions</h2>
        <p>The actual fitting is performed by functions in <code>velocyto.estimation</code>:</p>
        
        <div class="function-ref">
            <strong>fit_slope_weighted_offset</strong>: Weighted linear regression with offset
        </div>
        <div class="function-ref">
            <strong>fit_slope_offset</strong>: Unweighted linear regression with offset
        </div>
        <div class="function-ref">
            <strong>fit_slope_weighted</strong>: Weighted linear regression without offset
        </div>
        <div class="function-ref">
            <strong>fit_slope</strong>: Unweighted linear regression without offset
        </div>
        
        <p>These functions implement the mathematical optimization to find the best-fit gamma and offset values for each gene.</p>
    </div>

    <div class="section">
        <h2>Outputs</h2>
        <p>The method creates the following attributes:</p>
        <ul>
            <li><code>gammas</code>: Array of fitted gamma values for each gene</li>
            <li><code>q</code>: Array of fitted offset values for each gene</li>
            <li><code>R2</code>: Optional array of coefficient of determination values (goodness of fit)</li>
        </ul>
    </div>

    <div class="conclusion">
        <h2>Summary</h2>
        <p>The <code>fit_gammas</code> method is a sophisticated implementation of linear regression tailored for RNA velocity analysis. It:</p>
        <ol>
            <li>Selects appropriate data based on user parameters</li>
            <li>Computes weights to emphasize biologically relevant cells</li>
            <li>Performs linear regression with various options (weighted/unweighted, with/without offset)</li>
            <li>Handles edge cases and constraints</li>
            <li>Produces gamma values that represent degradation/splicing rates for each gene</li>
        </ol>
        <p>These gamma values are then used to calculate RNA velocity by comparing observed unspliced RNA to the expected amount at steady state.</p>
    </div>
</body>
</html>
