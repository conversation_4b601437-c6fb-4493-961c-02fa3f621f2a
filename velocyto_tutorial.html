<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Velocyto.py: RNA Velocity Analysis Tutorial</title>
    
    <!-- MathJax 3 for LaTeX rendering -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 1rem 0 0 0;
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
            border-radius: 0 8px 8px 0;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.8rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        
        .section h3 {
            color: #34495e;
            margin-top: 2rem;
            font-size: 1.4rem;
        }
        
        .math-box {
            background: #fff;
            border: 2px solid #e74c3c;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            box-shadow: 0 2px 10px rgba(231, 76, 60, 0.1);
        }
        
        .code-box {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .step-number {
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
        }
        
        .workflow-step {
            display: flex;
            align-items: flex-start;
            margin: 1.5rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .svg-container {
            text-align: center;
            margin: 2rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .toc {
            background: #34495e;
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .toc h3 {
            margin-top: 0;
            color: #ecf0f1;
        }
        
        .toc ul {
            list-style: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 0.5rem 0;
            padding-left: 1rem;
        }
        
        .toc a {
            color: #3498db;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .toc a:hover {
            color: #5dade2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧬 Velocyto.py Tutorial</h1>
            <p>Understanding RNA Velocity Analysis: Process and Mathematical Background</p>
        </div>
        
        <div class="content">
            <!-- Table of Contents -->
            <div class="toc">
                <h3>📚 Table of Contents</h3>
                <ul>
                    <li><a href="#introduction">1. Introduction to RNA Velocity</a></li>
                    <li><a href="#biological-background">2. Biological Background</a></li>
                    <li><a href="#mathematical-foundation">3. Mathematical Foundation</a></li>
                    <li><a href="#workflow">4. Velocyto.py Workflow</a></li>
                    <li><a href="#counting-process">5. Counting Process</a></li>
                    <li><a href="#velocity-estimation">6. Velocity Estimation</a></li>
                    <li><a href="#practical-implementation">7. Practical Implementation</a></li>
                    <li><a href="#visualization">8. Visualization and Interpretation</a></li>
                </ul>
            </div>

            <!-- Section 1: Introduction -->
            <div class="section" id="introduction">
                <h2>1. Introduction to RNA Velocity</h2>
                <p><span class="highlight">RNA velocity</span> is a computational method that predicts the future state of individual cells by analyzing the ratio of spliced to unspliced mRNA molecules. This revolutionary approach allows researchers to infer the direction and speed of cellular state transitions in single-cell RNA sequencing data.</p>
                
                <div class="svg-container">
                    <svg width="800" height="300" viewBox="0 0 800 300">
                        <!-- Background -->
                        <rect width="800" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
                        
                        <!-- Title -->
                        <text x="400" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">RNA Velocity Concept Overview</text>
                        
                        <!-- Cell 1 (Past) -->
                        <circle cx="150" cy="150" r="60" fill="#e3f2fd" stroke="#1976d2" stroke-width="3"/>
                        <text x="150" y="155" text-anchor="middle" font-size="14" font-weight="bold" fill="#1976d2">Cell State A</text>
                        <text x="150" y="220" text-anchor="middle" font-size="12" fill="#666">High Unspliced</text>
                        <text x="150" y="235" text-anchor="middle" font-size="12" fill="#666">Low Spliced</text>
                        
                        <!-- Arrow 1 -->
                        <path d="M 220 150 Q 300 120 380 150" stroke="#e74c3c" stroke-width="4" fill="none" marker-end="url(#arrowhead)"/>
                        <text x="300" y="135" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">RNA Velocity</text>
                        
                        <!-- Cell 2 (Present) -->
                        <circle cx="450" cy="150" r="60" fill="#fff3e0" stroke="#f57c00" stroke-width="3"/>
                        <text x="450" y="155" text-anchor="middle" font-size="14" font-weight="bold" fill="#f57c00">Cell State B</text>
                        <text x="450" y="220" text-anchor="middle" font-size="12" fill="#666">Medium Unspliced</text>
                        <text x="450" y="235" text-anchor="middle" font-size="12" fill="#666">Medium Spliced</text>
                        
                        <!-- Arrow 2 -->
                        <path d="M 520 150 Q 600 120 680 150" stroke="#e74c3c" stroke-width="4" fill="none" marker-end="url(#arrowhead)"/>
                        
                        <!-- Cell 3 (Future) -->
                        <circle cx="750" cy="150" r="60" fill="#e8f5e8" stroke="#388e3c" stroke-width="3"/>
                        <text x="750" y="155" text-anchor="middle" font-size="14" font-weight="bold" fill="#388e3c">Cell State C</text>
                        <text x="750" y="220" text-anchor="middle" font-size="12" fill="#666">Low Unspliced</text>
                        <text x="750" y="235" text-anchor="middle" font-size="12" fill="#666">High Spliced</text>
                        
                        <!-- Arrow marker definition -->
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
                            </marker>
                        </defs>
                        
                        <!-- Time axis -->
                        <line x1="100" y1="280" x2="700" y2="280" stroke="#666" stroke-width="2"/>
                        <text x="400" y="295" text-anchor="middle" font-size="12" fill="#666">Time →</text>
                    </svg>
                </div>
                
                <p>The key insight is that <span class="highlight">newly transcribed (unspliced) mRNA</span> provides information about the current transcriptional state, while <span class="highlight">mature (spliced) mRNA</span> reflects the past transcriptional activity. By comparing these two quantities, we can predict future cellular states.</p>
            </div>

            <!-- Section 2: Biological Background -->
            <div class="section" id="biological-background">
                <h2>2. Biological Background</h2>
                
                <h3>2.1 RNA Processing in Eukaryotes</h3>
                <p>In eukaryotic cells, gene expression involves multiple steps:</p>
                
                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>Transcription:</strong> DNA is transcribed into pre-mRNA containing both exons and introns
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>Splicing:</strong> Introns are removed and exons are joined to form mature mRNA
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div>
                        <strong>Export:</strong> Mature mRNA is exported from nucleus to cytoplasm
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="step-number">4</div>
                    <div>
                        <strong>Translation:</strong> mRNA is translated into proteins
                    </div>
                </div>
                
                <div class="svg-container">
                    <svg width="800" height="400" viewBox="0 0 800 400">
                        <!-- Background -->
                        <rect width="800" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
                        
                        <!-- Title -->
                        <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">RNA Processing and Velocity Detection</text>
                        
                        <!-- Nucleus -->
                        <ellipse cx="200" cy="200" rx="150" ry="120" fill="#e3f2fd" stroke="#1976d2" stroke-width="3" fill-opacity="0.3"/>
                        <text x="200" y="100" text-anchor="middle" font-size="16" font-weight="bold" fill="#1976d2">Nucleus</text>
                        
                        <!-- DNA -->
                        <rect x="120" y="140" width="160" height="20" fill="#ff9800" stroke="#f57c00" stroke-width="2"/>
                        <text x="200" y="155" text-anchor="middle" font-size="12" font-weight="bold" fill="white">DNA Gene</text>
                        
                        <!-- Pre-mRNA (unspliced) -->
                        <rect x="130" y="180" width="30" height="15" fill="#4caf50" stroke="#388e3c" stroke-width="1"/>
                        <rect x="170" y="180" width="20" height="15" fill="#f44336" stroke="#d32f2f" stroke-width="1"/>
                        <rect x="200" y="180" width="30" height="15" fill="#4caf50" stroke="#388e3c" stroke-width="1"/>
                        <rect x="240" y="180" width="20" height="15" fill="#f44336" stroke="#d32f2f" stroke-width="1"/>
                        <text x="200" y="210" text-anchor="middle" font-size="10" fill="#666">Pre-mRNA (Unspliced)</text>
                        
                        <!-- Splicing process -->
                        <path d="M 280 187 Q 320 160 360 187" stroke="#9c27b0" stroke-width="3" fill="none" marker-end="url(#arrowhead2)"/>
                        <text x="320" y="175" text-anchor="middle" font-size="12" font-weight="bold" fill="#9c27b0">Splicing</text>
                        
                        <!-- Mature mRNA (spliced) -->
                        <rect x="380" y="180" width="30" height="15" fill="#4caf50" stroke="#388e3c" stroke-width="1"/>
                        <rect x="420" y="180" width="30" height="15" fill="#4caf50" stroke="#388e3c" stroke-width="1"/>
                        <text x="425" y="210" text-anchor="middle" font-size="10" fill="#666">Mature mRNA (Spliced)</text>
                        
                        <!-- Cytoplasm -->
                        <rect x="500" y="50" width="250" height="300" fill="#fff3e0" stroke="#ff9800" stroke-width="3" fill-opacity="0.3"/>
                        <text x="625" y="80" text-anchor="middle" font-size="16" font-weight="bold" fill="#ff9800">Cytoplasm</text>
                        
                        <!-- Export arrow -->
                        <path d="M 470 187 L 520 187" stroke="#2196f3" stroke-width="4" fill="none" marker-end="url(#arrowhead2)"/>
                        <text x="495" y="175" text-anchor="middle" font-size="10" fill="#2196f3">Export</text>
                        
                        <!-- mRNA in cytoplasm -->
                        <rect x="540" y="180" width="30" height="15" fill="#4caf50" stroke="#388e3c" stroke-width="1"/>
                        <rect x="580" y="180" width="30" height="15" fill="#4caf50" stroke="#388e3c" stroke-width="1"/>
                        
                        <!-- Legend -->
                        <rect x="50" y="320" width="20" height="15" fill="#4caf50" stroke="#388e3c" stroke-width="1"/>
                        <text x="80" y="332" font-size="12" fill="#666">Exon</text>
                        <rect x="150" y="320" width="20" height="15" fill="#f44336" stroke="#d32f2f" stroke-width="1"/>
                        <text x="180" y="332" font-size="12" fill="#666">Intron</text>
                        
                        <!-- Velocity concept -->
                        <text x="400" y="370" text-anchor="middle" font-size="14" font-weight="bold" fill="#e74c3c">RNA Velocity = f(Unspliced, Spliced)</text>
                        
                        <!-- Arrow marker definition -->
                        <defs>
                            <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#9c27b0"/>
                            </marker>
                        </defs>
                    </svg>
                </div>
                
                <h3>2.2 Key Biological Insights</h3>
                <p>The fundamental principle behind RNA velocity is that:</p>
                <ul>
                    <li><strong>Unspliced mRNA abundance</strong> indicates current transcriptional activity</li>
                    <li><strong>Spliced mRNA abundance</strong> reflects accumulated past transcription</li>
                    <li><strong>The ratio between them</strong> reveals the direction of gene expression change</li>
                </ul>
            </div>

            <!-- Section 3: Mathematical Foundation -->
            <div class="section" id="mathematical-foundation">
                <h2>3. Mathematical Foundation</h2>

                <h3>3.1 Basic RNA Kinetics Model</h3>
                <p>The RNA velocity model is based on a simple kinetic equation describing the relationship between unspliced and spliced mRNA:</p>

                <div class="math-box">
                    <p><strong>Fundamental Equations:</strong></p>
                    $$\frac{du}{dt} = \alpha - \beta u$$
                    $$\frac{ds}{dt} = \beta u - \gamma s$$

                    <p>Where:</p>
                    <ul>
                        <li>$u$ = unspliced mRNA concentration</li>
                        <li>$s$ = spliced mRNA concentration</li>
                        <li>$\alpha$ = transcription rate</li>
                        <li>$\beta$ = splicing rate</li>
                        <li>$\gamma$ = degradation rate</li>
                    </ul>
                </div>

                <h3>3.2 Steady State Analysis</h3>
                <p>At steady state ($\frac{du}{dt} = 0$ and $\frac{ds}{dt} = 0$), we get:</p>

                <div class="math-box">
                    $$u_{ss} = \frac{\alpha}{\beta}$$
                    $$s_{ss} = \frac{\alpha}{\gamma}$$

                    <p>This gives us the steady-state ratio:</p>
                    $$\frac{u_{ss}}{s_{ss}} = \frac{\gamma}{\beta}$$
                </div>

                <h3>3.3 RNA Velocity Definition</h3>
                <p>RNA velocity is defined as the time derivative of spliced mRNA:</p>

                <div class="math-box">
                    $$v = \frac{ds}{dt} = \beta u - \gamma s$$

                    <p>In the context of single-cell data, this becomes:</p>
                    $$v_i = \beta_i u_i - \gamma_i s_i$$

                    <p>Where $i$ represents individual genes.</p>
                </div>

                <div class="svg-container">
                    <svg width="800" height="500" viewBox="0 0 800 500">
                        <!-- Background -->
                        <rect width="800" height="500" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">RNA Velocity Phase Portrait</text>

                        <!-- Axes -->
                        <line x1="100" y1="400" x2="700" y2="400" stroke="#333" stroke-width="2"/>
                        <line x1="100" y1="400" x2="100" y2="100" stroke="#333" stroke-width="2"/>

                        <!-- Axis labels -->
                        <text x="400" y="430" text-anchor="middle" font-size="14" fill="#333">Spliced mRNA (s)</text>
                        <text x="70" y="250" text-anchor="middle" font-size="14" fill="#333" transform="rotate(-90 70 250)">Unspliced mRNA (u)</text>

                        <!-- Steady state line -->
                        <line x1="100" y1="400" x2="600" y2="150" stroke="#e74c3c" stroke-width="3" stroke-dasharray="5,5"/>
                        <text x="500" y="140" font-size="12" fill="#e74c3c" font-weight="bold">u = γ/β × s (Steady State)</text>

                        <!-- Data points with velocity vectors -->
                        <!-- Point 1: High u, low s (induction) -->
                        <circle cx="200" cy="200" r="6" fill="#2196f3"/>
                        <path d="M 200 200 L 250 220" stroke="#ff9800" stroke-width="3" marker-end="url(#arrowhead3)"/>
                        <text x="180" y="190" font-size="10" fill="#666">Induction</text>

                        <!-- Point 2: Medium u, medium s (steady) -->
                        <circle cx="350" cy="275" r="6" fill="#2196f3"/>
                        <path d="M 350 275 L 370 280" stroke="#ff9800" stroke-width="3" marker-end="url(#arrowhead3)"/>
                        <text x="330" y="265" font-size="10" fill="#666">Steady</text>

                        <!-- Point 3: Low u, high s (repression) -->
                        <circle cx="500" cy="350" r="6" fill="#2196f3"/>
                        <path d="M 500 350 L 480 370" stroke="#ff9800" stroke-width="3" marker-end="url(#arrowhead3)"/>
                        <text x="480" y="340" font-size="10" fill="#666">Repression</text>

                        <!-- Velocity field grid -->
                        <g opacity="0.3">
                            <!-- Grid of small arrows showing velocity field -->
                            <path d="M 150 150 L 160 155" stroke="#666" stroke-width="1" marker-end="url(#smallarrow)"/>
                            <path d="M 200 150 L 215 158" stroke="#666" stroke-width="1" marker-end="url(#smallarrow)"/>
                            <path d="M 250 150 L 270 160" stroke="#666" stroke-width="1" marker-end="url(#smallarrow)"/>
                            <path d="M 300 150 L 325 162" stroke="#666" stroke-width="1" marker-end="url(#smallarrow)"/>

                            <path d="M 150 200 L 165 210" stroke="#666" stroke-width="1" marker-end="url(#smallarrow)"/>
                            <path d="M 200 200 L 220 215" stroke="#666" stroke-width="1" marker-end="url(#smallarrow)"/>
                            <path d="M 250 200 L 275 220" stroke="#666" stroke-width="1" marker-end="url(#smallarrow)"/>
                            <path d="M 300 200 L 330 225" stroke="#666" stroke-width="1" marker-end="url(#smallarrow)"/>

                            <path d="M 150 250 L 170 265" stroke="#666" stroke-width="1" marker-end="url(#smallarrow)"/>
                            <path d="M 200 250 L 225 270" stroke="#666" stroke-width="1" marker-end="url(#smallarrow)"/>
                            <path d="M 250 250 L 280 275" stroke="#666" stroke-width="1" marker-end="url(#smallarrow)"/>
                            <path d="M 300 250 L 335 280" stroke="#666" stroke-width="1" marker-end="url(#smallarrow)"/>

                            <path d="M 400 200 L 420 225" stroke="#666" stroke-width="1" marker-end="url(#smallarrow)"/>
                            <path d="M 450 200 L 465 230" stroke="#666" stroke-width="1" marker-end="url(#smallarrow)"/>
                            <path d="M 500 200 L 510 235" stroke="#666" stroke-width="1" marker-end="url(#smallarrow)"/>

                            <path d="M 400 300 L 410 320" stroke="#666" stroke-width="1" marker-end="url(#smallarrow)"/>
                            <path d="M 450 300 L 455 325" stroke="#666" stroke-width="1" marker-end="url(#smallarrow)"/>
                            <path d="M 500 300 L 500 330" stroke="#666" stroke-width="1" marker-end="url(#smallarrow)"/>

                            <path d="M 400 350 L 395 370" stroke="#666" stroke-width="1" marker-end="url(#smallarrow)"/>
                            <path d="M 450 350 L 440 375" stroke="#666" stroke-width="1" marker-end="url(#smallarrow)"/>
                            <path d="M 500 350 L 485 380" stroke="#666" stroke-width="1" marker-end="url(#smallarrow)"/>
                        </g>

                        <!-- Legend -->
                        <rect x="550" y="60" width="200" height="120" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="650" y="80" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Velocity Interpretation</text>

                        <circle cx="570" cy="95" r="4" fill="#2196f3"/>
                        <text x="585" y="100" font-size="10" fill="#666">Cell state</text>

                        <path d="M 570 110 L 590 115" stroke="#ff9800" stroke-width="2" marker-end="url(#arrowhead3)"/>
                        <text x="600" y="118" font-size="10" fill="#666">Velocity vector</text>

                        <line x1="570" y1="130" x2="590" y2="125" stroke="#e74c3c" stroke-width="2" stroke-dasharray="3,3"/>
                        <text x="600" y="133" font-size="10" fill="#666">Steady state</text>

                        <path d="M 570 145 L 580 148" stroke="#666" stroke-width="1" marker-end="url(#smallarrow)" opacity="0.7"/>
                        <text x="590" y="150" font-size="10" fill="#666">Velocity field</text>

                        <!-- Arrow marker definitions -->
                        <defs>
                            <marker id="arrowhead3" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#ff9800"/>
                            </marker>
                            <marker id="smallarrow" markerWidth="6" markerHeight="4" refX="5" refY="2" orient="auto">
                                <polygon points="0 0, 6 2, 0 4" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>3.4 Practical Estimation</h3>
                <p>In practice, velocyto.py estimates velocity using linear regression:</p>

                <div class="math-box">
                    <p><strong>Linear Model:</strong></p>
                    $$u = \gamma s + \epsilon$$

                    <p>Where $\gamma$ is the slope parameter estimated by least squares:</p>
                    $$\gamma = \frac{\sum_i (u_i - \bar{u})(s_i - \bar{s})}{\sum_i (s_i - \bar{s})^2}$$

                    <p>The velocity is then calculated as:</p>
                    $$v = u - \gamma s$$
                </div>
            </div>

            <!-- Section 4: Velocyto.py Workflow -->
            <div class="section" id="workflow">
                <h2>4. Velocyto.py Workflow</h2>

                <p>The velocyto.py pipeline consists of two main phases:</p>

                <div class="svg-container">
                    <svg width="800" height="600" viewBox="0 0 800 600">
                        <!-- Background -->
                        <rect width="800" height="600" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="400" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">Velocyto.py Complete Workflow</text>

                        <!-- Phase 1: Data Processing -->
                        <rect x="50" y="60" width="300" height="250" fill="#e3f2fd" stroke="#1976d2" stroke-width="3" rx="10"/>
                        <text x="200" y="85" text-anchor="middle" font-size="16" font-weight="bold" fill="#1976d2">Phase 1: Data Processing (CLI)</text>

                        <!-- Input files -->
                        <rect x="70" y="100" width="80" height="30" fill="#fff" stroke="#666" stroke-width="1" rx="5"/>
                        <text x="110" y="120" text-anchor="middle" font-size="10" fill="#333">BAM files</text>

                        <rect x="160" y="100" width="80" height="30" fill="#fff" stroke="#666" stroke-width="1" rx="5"/>
                        <text x="200" y="120" text-anchor="middle" font-size="10" fill="#333">GTF annotation</text>

                        <rect x="250" y="100" width="80" height="30" fill="#fff" stroke="#666" stroke-width="1" rx="5"/>
                        <text x="290" y="120" text-anchor="middle" font-size="10" fill="#333">Barcodes</text>

                        <!-- Processing steps -->
                        <rect x="70" y="150" width="260" height="25" fill="#bbdefb" stroke="#1976d2" stroke-width="1" rx="3"/>
                        <text x="200" y="167" text-anchor="middle" font-size="11" fill="#1976d2">1. Read alignment parsing</text>

                        <rect x="70" y="180" width="260" height="25" fill="#bbdefb" stroke="#1976d2" stroke-width="1" rx="3"/>
                        <text x="200" y="197" text-anchor="middle" font-size="11" fill="#1976d2">2. Exon/Intron classification</text>

                        <rect x="70" y="210" width="260" height="25" fill="#bbdefb" stroke="#1976d2" stroke-width="1" rx="3"/>
                        <text x="200" y="227" text-anchor="middle" font-size="11" fill="#1976d2">3. Spliced/Unspliced counting</text>

                        <rect x="70" y="240" width="260" height="25" fill="#bbdefb" stroke="#1976d2" stroke-width="1" rx="3"/>
                        <text x="200" y="257" text-anchor="middle" font-size="11" fill="#1976d2">4. Generate .loom file</text>

                        <!-- Output -->
                        <rect x="120" y="280" width="160" height="20" fill="#4caf50" stroke="#388e3c" stroke-width="2" rx="5"/>
                        <text x="200" y="293" text-anchor="middle" font-size="12" font-weight="bold" fill="white">.loom file</text>

                        <!-- Arrow to Phase 2 -->
                        <path d="M 350 200 Q 400 150 450 200" stroke="#e74c3c" stroke-width="4" fill="none" marker-end="url(#arrowhead4)"/>

                        <!-- Phase 2: Analysis -->
                        <rect x="450" y="60" width="300" height="480" fill="#fff3e0" stroke="#f57c00" stroke-width="3" rx="10"/>
                        <text x="600" y="85" text-anchor="middle" font-size="16" font-weight="bold" fill="#f57c00">Phase 2: Analysis (Python API)</text>

                        <!-- Analysis steps -->
                        <rect x="470" y="110" width="260" height="25" fill="#ffcc02" stroke="#f57c00" stroke-width="1" rx="3"/>
                        <text x="600" y="127" text-anchor="middle" font-size="11" fill="#333">1. Load .loom file</text>

                        <rect x="470" y="140" width="260" height="25" fill="#ffcc02" stroke="#f57c00" stroke-width="1" rx="3"/>
                        <text x="600" y="157" text-anchor="middle" font-size="11" fill="#333">2. Normalization & filtering</text>

                        <rect x="470" y="170" width="260" height="25" fill="#ffcc02" stroke="#f57c00" stroke-width="1" rx="3"/>
                        <text x="600" y="187" text-anchor="middle" font-size="11" fill="#333">3. Feature selection</text>

                        <rect x="470" y="200" width="260" height="25" fill="#ffcc02" stroke="#f57c00" stroke-width="1" rx="3"/>
                        <text x="600" y="217" text-anchor="middle" font-size="11" fill="#333">4. PCA dimensionality reduction</text>

                        <rect x="470" y="230" width="260" height="25" fill="#ffcc02" stroke="#f57c00" stroke-width="1" rx="3"/>
                        <text x="600" y="247" text-anchor="middle" font-size="11" fill="#333">5. k-NN graph construction</text>

                        <rect x="470" y="260" width="260" height="25" fill="#ffcc02" stroke="#f57c00" stroke-width="1" rx="3"/>
                        <text x="600" y="277" text-anchor="middle" font-size="11" fill="#333">6. Imputation & smoothing</text>

                        <rect x="470" y="290" width="260" height="25" fill="#ffcc02" stroke="#f57c00" stroke-width="1" rx="3"/>
                        <text x="600" y="307" text-anchor="middle" font-size="11" fill="#333">7. Fit splicing dynamics</text>

                        <rect x="470" y="320" width="260" height="25" fill="#ffcc02" stroke="#f57c00" stroke-width="1" rx="3"/>
                        <text x="600" y="337" text-anchor="middle" font-size="11" fill="#333">8. Calculate velocity</text>

                        <rect x="470" y="350" width="260" height="25" fill="#ffcc02" stroke="#f57c00" stroke-width="1" rx="3"/>
                        <text x="600" y="367" text-anchor="middle" font-size="11" fill="#333">9. Project to embedding</text>

                        <rect x="470" y="380" width="260" height="25" fill="#ffcc02" stroke="#f57c00" stroke-width="1" rx="3"/>
                        <text x="600" y="397" text-anchor="middle" font-size="11" fill="#333">10. Estimate transition probabilities</text>

                        <rect x="470" y="410" width="260" height="25" fill="#ffcc02" stroke="#f57c00" stroke-width="1" rx="3"/>
                        <text x="600" y="427" text-anchor="middle" font-size="11" fill="#333">11. Visualization</text>

                        <!-- Final outputs -->
                        <rect x="480" y="450" width="70" height="20" fill="#9c27b0" stroke="#7b1fa2" stroke-width="1" rx="3"/>
                        <text x="515" y="463" text-anchor="middle" font-size="9" fill="white">Velocity field</text>

                        <rect x="560" y="450" width="70" height="20" fill="#9c27b0" stroke="#7b1fa2" stroke-width="1" rx="3"/>
                        <text x="595" y="463" text-anchor="middle" font-size="9" fill="white">Stream plot</text>

                        <rect x="640" y="450" width="70" height="20" fill="#9c27b0" stroke="#7b1fa2" stroke-width="1" rx="3"/>
                        <text x="675" y="463" text-anchor="middle" font-size="9" fill="white">Pseudotime</text>

                        <rect x="480" y="480" width="70" height="20" fill="#9c27b0" stroke="#7b1fa2" stroke-width="1" rx="3"/>
                        <text x="515" y="493" text-anchor="middle" font-size="9" fill="white">Trajectories</text>

                        <rect x="560" y="480" width="70" height="20" fill="#9c27b0" stroke="#7b1fa2" stroke-width="1" rx="3"/>
                        <text x="595" y="493" text-anchor="middle" font-size="9" fill="white">Gene trends</text>

                        <rect x="640" y="480" width="70" height="20" fill="#9c27b0" stroke="#7b1fa2" stroke-width="1" rx="3"/>
                        <text x="675" y="493" text-anchor="middle" font-size="9" fill="white">Markov chain</text>

                        <!-- Command examples -->
                        <text x="50" y="350" font-size="12" font-weight="bold" fill="#333">CLI Commands:</text>
                        <text x="50" y="370" font-size="10" font-family="monospace" fill="#666">velocyto run10x sample/ annotation.gtf</text>
                        <text x="50" y="385" font-size="10" font-family="monospace" fill="#666">velocyto run sample.bam annotation.gtf</text>

                        <text x="50" y="420" font-size="12" font-weight="bold" fill="#333">Python API:</text>
                        <text x="50" y="440" font-size="10" font-family="monospace" fill="#666">import velocyto as vcy</text>
                        <text x="50" y="455" font-size="10" font-family="monospace" fill="#666">vlm = vcy.VelocytoLoom("data.loom")</text>

                        <!-- Arrow marker definition -->
                        <defs>
                            <marker id="arrowhead4" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
                            </marker>
                        </defs>
                    </svg>
                </div>
            </div>

            <!-- Section 5: Counting Process -->
            <div class="section" id="counting-process">
                <h2>5. Counting Process</h2>

                <h3>5.1 Read Classification Logic</h3>
                <p>Velocyto.py classifies each sequencing read based on its alignment to gene features:</p>

                <div class="svg-container">
                    <svg width="800" height="400" viewBox="0 0 800 400">
                        <!-- Background -->
                        <rect width="800" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Read Classification in Velocyto</text>

                        <!-- Gene structure -->
                        <text x="50" y="70" font-size="14" font-weight="bold" fill="#333">Gene Structure:</text>

                        <!-- Exons -->
                        <rect x="100" y="80" width="60" height="20" fill="#4caf50" stroke="#388e3c" stroke-width="2"/>
                        <text x="130" y="95" text-anchor="middle" font-size="10" fill="white" font-weight="bold">Exon 1</text>

                        <rect x="200" y="80" width="60" height="20" fill="#4caf50" stroke="#388e3c" stroke-width="2"/>
                        <text x="230" y="95" text-anchor="middle" font-size="10" fill="white" font-weight="bold">Exon 2</text>

                        <rect x="300" y="80" width="60" height="20" fill="#4caf50" stroke="#388e3c" stroke-width="2"/>
                        <text x="330" y="95" text-anchor="middle" font-size="10" fill="white" font-weight="bold">Exon 3</text>

                        <!-- Introns -->
                        <rect x="160" y="85" width="40" height="10" fill="#f44336" stroke="#d32f2f" stroke-width="1"/>
                        <text x="180" y="93" text-anchor="middle" font-size="8" fill="white">Intron 1</text>

                        <rect x="260" y="85" width="40" height="10" fill="#f44336" stroke="#d32f2f" stroke-width="1"/>
                        <text x="280" y="93" text-anchor="middle" font-size="8" fill="white">Intron 2</text>

                        <!-- Read types -->
                        <text x="50" y="140" font-size="14" font-weight="bold" fill="#333">Read Types:</text>

                        <!-- Spliced read -->
                        <text x="70" y="165" font-size="12" font-weight="bold" fill="#2196f3">1. Spliced Read:</text>
                        <rect x="100" y="170" width="25" height="8" fill="#2196f3" opacity="0.7"/>
                        <rect x="200" y="170" width="25" height="8" fill="#2196f3" opacity="0.7"/>
                        <line x1="125" y1="174" x2="200" y2="174" stroke="#2196f3" stroke-width="2" stroke-dasharray="3,3"/>
                        <text x="400" y="178" font-size="11" fill="#666">→ Counted as SPLICED</text>

                        <!-- Unspliced read -->
                        <text x="70" y="200" font-size="12" font-weight="bold" fill="#ff9800">2. Unspliced Read:</text>
                        <rect x="160" y="205" width="40" height="8" fill="#ff9800" opacity="0.7"/>
                        <text x="400" y="213" font-size="11" fill="#666">→ Counted as UNSPLICED</text>

                        <!-- Spanning read -->
                        <text x="70" y="235" font-size="12" font-weight="bold" fill="#9c27b0">3. Spanning Read:</text>
                        <rect x="140" y="240" width="40" height="8" fill="#9c27b0" opacity="0.7"/>
                        <rect x="200" y="240" width="20" height="8" fill="#9c27b0" opacity="0.7"/>
                        <text x="400" y="248" font-size="11" fill="#666">→ Counted as UNSPLICED</text>

                        <!-- Ambiguous read -->
                        <text x="70" y="270" font-size="12" font-weight="bold" fill="#795548">4. Ambiguous Read:</text>
                        <rect x="100" y="275" width="15" height="8" fill="#795548" opacity="0.7"/>
                        <rect x="200" y="275" width="15" height="8" fill="#795548" opacity="0.7"/>
                        <rect x="300" y="275" width="15" height="8" fill="#795548" opacity="0.7"/>
                        <text x="400" y="283" font-size="11" fill="#666">→ Counted as AMBIGUOUS</text>

                        <!-- Decision tree -->
                        <text x="450" y="140" font-size="14" font-weight="bold" fill="#333">Classification Logic:</text>

                        <rect x="450" y="150" width="300" height="200" fill="white" stroke="#ccc" stroke-width="1" rx="5"/>

                        <!-- Decision nodes -->
                        <rect x="470" y="170" width="120" height="25" fill="#e3f2fd" stroke="#1976d2" stroke-width="1" rx="3"/>
                        <text x="530" y="187" text-anchor="middle" font-size="10" fill="#1976d2">Maps to multiple genes?</text>

                        <rect x="470" y="210" width="120" height="25" fill="#fff3e0" stroke="#f57c00" stroke-width="1" rx="3"/>
                        <text x="530" y="227" text-anchor="middle" font-size="10" fill="#f57c00">Spans exon-intron?</text>

                        <rect x="470" y="250" width="120" height="25" fill="#f3e5f5" stroke="#9c27b0" stroke-width="1" rx="3"/>
                        <text x="530" y="267" text-anchor="middle" font-size="10" fill="#9c27b0">Only in exons?</text>

                        <rect x="470" y="290" width="120" height="25" fill="#e8f5e8" stroke="#4caf50" stroke-width="1" rx="3"/>
                        <text x="530" y="307" text-anchor="middle" font-size="10" fill="#4caf50">Only in introns?</text>

                        <!-- Outcomes -->
                        <text x="620" y="187" font-size="10" fill="#666">Yes → AMBIGUOUS</text>
                        <text x="620" y="227" font-size="10" fill="#666">Yes → UNSPLICED</text>
                        <text x="620" y="267" font-size="10" fill="#666">Yes → SPLICED</text>
                        <text x="620" y="307" font-size="10" fill="#666">Yes → UNSPLICED</text>
                    </svg>
                </div>

                <h3>5.2 Count Matrix Generation</h3>
                <p>The counting process generates three matrices:</p>

                <div class="math-box">
                    <p><strong>Output Matrices:</strong></p>
                    <ul>
                        <li><strong>Spliced matrix (S):</strong> $S_{ij}$ = spliced counts for gene $i$ in cell $j$</li>
                        <li><strong>Unspliced matrix (U):</strong> $U_{ij}$ = unspliced counts for gene $i$ in cell $j$</li>
                        <li><strong>Ambiguous matrix (A):</strong> $A_{ij}$ = ambiguous counts for gene $i$ in cell $j$</li>
                    </ul>

                    <p>These matrices have dimensions: genes × cells</p>
                </div>

                <div class="code-box">
# Example velocyto CLI commands for different platforms:

# 10X Genomics data
velocyto run10x /path/to/sample/ /path/to/annotation.gtf

# Standard BAM file
velocyto run /path/to/sample.bam /path/to/annotation.gtf

# Smart-seq2 data
velocyto run_smartseq2 /path/to/bam_folder/ /path/to/annotation.gtf

# DropEst preprocessed data
velocyto run_dropest /path/to/sample.bam /path/to/annotation.gtf
                </div>
            </div>

            <!-- Section 6: Velocity Estimation -->
            <div class="section" id="velocity-estimation">
                <h2>6. Velocity Estimation</h2>

                <h3>6.1 Data Preprocessing</h3>
                <p>Before velocity estimation, several preprocessing steps are performed:</p>

                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>Normalization:</strong> Size factor normalization and log transformation
                        <div class="math-box" style="margin-top: 0.5rem;">
                            $$S_{norm} = \log_2\left(\frac{S \cdot \text{size_factor}}{1000} + 1\right)$$
                        </div>
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>Feature Selection:</strong> Identify highly variable genes
                        <div class="code-box" style="margin-top: 0.5rem;">
vlm.score_detection_levels(min_expr_counts=40, min_cells_express=30)
vlm.filter_genes(by_detection_levels=True)
vlm.score_cv_vs_mean(3000, plot=True, max_expr_avg=35)
vlm.filter_genes(by_cv_vs_mean=True)
                        </div>
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div>
                        <strong>Dimensionality Reduction:</strong> PCA for computational efficiency
                        <div class="code-box" style="margin-top: 0.5rem;">
vlm.perform_PCA()
vlm.knn_imputation(n_pca_dims=25, k=525, balanced=True)
                        </div>
                    </div>
                </div>

                <h3>6.2 Splicing Dynamics Fitting</h3>
                <p>The core of velocity estimation involves fitting the splicing dynamics model:</p>

                <div class="math-box">
                    <p><strong>Linear Regression Model:</strong></p>
                    $$u_i = \gamma_i s_i + \epsilon_i$$

                    <p>For each gene $i$, we estimate $\gamma_i$ using:</p>
                    $$\gamma_i = \arg\min_\gamma \sum_j (u_{ij} - \gamma s_{ij})^2$$

                    <p>The velocity is then:</p>
                    $$v_{ij} = u_{ij} - \gamma_i s_{ij}$$
                </div>

                <div class="svg-container">
                    <svg width="800" height="500" viewBox="0 0 800 500">
                        <!-- Background -->
                        <rect width="800" height="500" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Splicing Dynamics Fitting Process</text>

                        <!-- Left plot: Raw data -->
                        <rect x="50" y="60" width="300" height="200" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="200" y="80" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Raw U vs S Data</text>

                        <!-- Axes for left plot -->
                        <line x1="80" y1="230" x2="320" y2="230" stroke="#333" stroke-width="1"/>
                        <line x1="80" y1="230" x2="80" y2="90" stroke="#333" stroke-width="1"/>
                        <text x="200" y="250" text-anchor="middle" font-size="10" fill="#666">Spliced (S)</text>
                        <text x="65" y="160" text-anchor="middle" font-size="10" fill="#666" transform="rotate(-90 65 160)">Unspliced (U)</text>

                        <!-- Data points -->
                        <circle cx="120" cy="180" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="140" cy="170" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="160" cy="160" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="180" cy="150" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="200" cy="140" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="220" cy="130" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="240" cy="120" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="260" cy="110" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="280" cy="100" r="3" fill="#2196f3" opacity="0.7"/>

                        <!-- Scattered points -->
                        <circle cx="130" cy="200" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="150" cy="190" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="170" cy="185" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="190" cy="175" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="210" cy="165" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="230" cy="155" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="250" cy="145" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="270" cy="135" r="3" fill="#2196f3" opacity="0.7"/>

                        <!-- Right plot: Fitted model -->
                        <rect x="450" y="60" width="300" height="200" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="600" y="80" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Fitted Model</text>

                        <!-- Axes for right plot -->
                        <line x1="480" y1="230" x2="720" y2="230" stroke="#333" stroke-width="1"/>
                        <line x1="480" y1="230" x2="480" y2="90" stroke="#333" stroke-width="1"/>
                        <text x="600" y="250" text-anchor="middle" font-size="10" fill="#666">Spliced (S)</text>
                        <text x="465" y="160" text-anchor="middle" font-size="10" fill="#666" transform="rotate(-90 465 160)">Unspliced (U)</text>

                        <!-- Same data points -->
                        <circle cx="520" cy="180" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="540" cy="170" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="560" cy="160" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="580" cy="150" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="600" cy="140" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="620" cy="130" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="640" cy="120" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="660" cy="110" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="680" cy="100" r="3" fill="#2196f3" opacity="0.7"/>

                        <circle cx="530" cy="200" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="550" cy="190" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="570" cy="185" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="590" cy="175" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="610" cy="165" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="630" cy="155" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="650" cy="145" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="670" cy="135" r="3" fill="#2196f3" opacity="0.7"/>

                        <!-- Fitted line -->
                        <line x1="480" y1="220" x2="720" y2="90" stroke="#e74c3c" stroke-width="3"/>
                        <text x="650" y="110" font-size="12" fill="#e74c3c" font-weight="bold">u = γs</text>

                        <!-- Velocity vectors -->
                        <path d="M 530 200 L 530 185" stroke="#ff9800" stroke-width="2" marker-end="url(#arrowhead5)"/>
                        <path d="M 550 190 L 550 175" stroke="#ff9800" stroke-width="2" marker-end="url(#arrowhead5)"/>
                        <path d="M 570 185 L 570 170" stroke="#ff9800" stroke-width="2" marker-end="url(#arrowhead5)"/>
                        <path d="M 590 175 L 590 160" stroke="#ff9800" stroke-width="2" marker-end="url(#arrowhead5)"/>
                        <path d="M 610 165 L 610 150" stroke="#ff9800" stroke-width="2" marker-end="url(#arrowhead5)"/>

                        <text x="600" y="275" text-anchor="middle" font-size="12" fill="#ff9800" font-weight="bold">Velocity = U - γS</text>

                        <!-- Bottom: Equation -->
                        <rect x="200" y="300" width="400" height="150" fill="white" stroke="#e74c3c" stroke-width="2" rx="10"/>
                        <text x="400" y="325" text-anchor="middle" font-size="16" font-weight="bold" fill="#e74c3c">Velocity Calculation Steps</text>

                        <text x="220" y="350" font-size="12" fill="#333">1. For each gene, fit: u = γs + ε</text>
                        <text x="220" y="370" font-size="12" fill="#333">2. Estimate γ using least squares regression</text>
                        <text x="220" y="390" font-size="12" fill="#333">3. Calculate velocity: v = u - γs</text>
                        <text x="220" y="410" font-size="12" fill="#333">4. Positive v → gene expression increasing</text>
                        <text x="220" y="430" font-size="12" fill="#333">5. Negative v → gene expression decreasing</text>

                        <!-- Arrow marker definition -->
                        <defs>
                            <marker id="arrowhead5" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#ff9800"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <div class="code-box">
# Velocity estimation in velocyto.py
vlm.fit_gammas(limit_gamma=False, fit_offset=False)
vlm.predict_U()
vlm.calculate_velocity()
vlm.calculate_shift(assumption="constant_velocity")
vlm.extrapolate_cell_at_t(delta_t=1.)
                </div>
            </div>

            <!-- Section 7: Practical Implementation -->
            <div class="section" id="practical-implementation">
                <h2>7. Practical Implementation</h2>

                <h3>7.1 Complete Analysis Pipeline</h3>
                <p>Here's a step-by-step implementation of a complete velocyto analysis:</p>

                <div class="code-box">
import velocyto as vcy
import numpy as np
import matplotlib.pyplot as plt

# Step 1: Load the loom file
vlm = vcy.VelocytoLoom("sample.loom")

# Step 2: Basic filtering and normalization
vlm.normalize("S", size=True, log=True)
vlm.S_norm  # Normalized spliced matrix

# Step 3: Feature selection
vlm.score_detection_levels(min_expr_counts=40, min_cells_express=30)
vlm.filter_genes(by_detection_levels=True)

# Step 4: Highly variable genes
vlm.score_cv_vs_mean(3000, plot=True, max_expr_avg=35)
vlm.filter_genes(by_cv_vs_mean=True)

# Step 5: Dimensionality reduction
vlm.perform_PCA()

# Step 6: k-NN imputation
vlm.knn_imputation(n_pca_dims=25, k=525, balanced=True,
                   b_sight=3000, b_maxl=1500, n_jobs=16)

# Step 7: Fit splicing dynamics
vlm.fit_gammas(limit_gamma=False, fit_offset=False)

# Step 8: Predict unspliced and calculate velocity
vlm.predict_U()
vlm.calculate_velocity()
vlm.calculate_shift(assumption="constant_velocity")
vlm.extrapolate_cell_at_t(delta_t=1.)
                </div>

                <h3>7.2 Embedding and Visualization</h3>
                <p>Project velocity onto low-dimensional embeddings for visualization:</p>

                <div class="code-box">
# Perform embedding (t-SNE or UMAP)
vlm.perform_PCA()
vlm.knn_imputation(n_pca_dims=25, k=525, balanced=True)

# Calculate embedding
bh_tsne = TSNE()
vlm.ts = bh_tsne.fit_transform(vlm.pcs[:, :25])

# Estimate transition probabilities
vlm.estimate_transition_prob(hidim="Sx_sz", embed="ts",
                            transform="sqrt", psc=1,
                            n_neighbors=3000, knn_random=True,
                            sampled_fraction=0.3)

# Calculate embedding velocity
vlm.calculate_embedding_shift(sigma_corr=0.05, expression_scaling=True)

# Visualize velocity field
vlm.plot_velocity_as_flow(which_tsne="ts",
                         min_mass=24, autoscale=True,
                         scatter_kwargs_dict={"alpha":0.35, "lw":0.35,
                                             "edgecolors":"0.4", "s": 38,
                                             "rasterized":True},
                         min_arrow_len=4, arrow_scale=4,
                         max_arrow_len=14)
                </div>

                <h3>7.3 Advanced Analysis</h3>
                <p>Perform trajectory analysis and pseudotime estimation:</p>

                <div class="code-box">
# Markov chain analysis
vlm.run_markov(starting_p=np.ones(vlm.S.shape[1]), n_steps=2500)

# Gene expression trends along trajectories
vlm.plot_phase_portraits(["Gene1", "Gene2", "Gene3"])

# Pseudotime analysis
vlm.calculate_shift(assumption="constant_velocity")
vlm.extrapolate_cell_at_t(delta_t=1.)

# Identify driver genes
vlm.score_cluster_expression(cluster_ix=cluster_labels,
                            min_avg_U=0.01, min_avg_S=0.30)
                </div>
            </div>

            <!-- Section 8: Visualization and Interpretation -->
            <div class="section" id="visualization">
                <h2>8. Visualization and Interpretation</h2>

                <h3>8.1 Velocity Field Plots</h3>
                <p>The primary visualization shows velocity vectors overlaid on cell embeddings:</p>

                <div class="svg-container">
                    <svg width="800" height="500" viewBox="0 0 800 500">
                        <!-- Background -->
                        <rect width="800" height="500" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">RNA Velocity Visualization Types</text>

                        <!-- Left panel: Velocity field -->
                        <rect x="50" y="60" width="300" height="200" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="200" y="80" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Velocity Field Plot</text>

                        <!-- Cells as points -->
                        <circle cx="120" cy="120" r="4" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <circle cx="140" cy="130" r="4" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <circle cx="160" cy="140" r="4" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <circle cx="180" cy="150" r="4" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <circle cx="200" cy="160" r="4" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <circle cx="220" cy="170" r="4" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <circle cx="240" cy="180" r="4" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <circle cx="260" cy="190" r="4" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <circle cx="280" cy="200" r="4" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>

                        <!-- Additional scattered cells -->
                        <circle cx="130" cy="180" r="4" fill="#fff3e0" stroke="#f57c00" stroke-width="1"/>
                        <circle cx="150" cy="190" r="4" fill="#fff3e0" stroke="#f57c00" stroke-width="1"/>
                        <circle cx="170" cy="200" r="4" fill="#fff3e0" stroke="#f57c00" stroke-width="1"/>
                        <circle cx="190" cy="210" r="4" fill="#fff3e0" stroke="#f57c00" stroke-width="1"/>
                        <circle cx="210" cy="220" r="4" fill="#fff3e0" stroke="#f57c00" stroke-width="1"/>
                        <circle cx="230" cy="230" r="4" fill="#fff3e0" stroke="#f57c00" stroke-width="1"/>

                        <circle cx="250" cy="120" r="4" fill="#e8f5e8" stroke="#4caf50" stroke-width="1"/>
                        <circle cx="270" cy="130" r="4" fill="#e8f5e8" stroke="#4caf50" stroke-width="1"/>
                        <circle cx="290" cy="140" r="4" fill="#e8f5e8" stroke="#4caf50" stroke-width="1"/>
                        <circle cx="310" cy="150" r="4" fill="#e8f5e8" stroke="#4caf50" stroke-width="1"/>

                        <!-- Velocity arrows -->
                        <path d="M 120 120 L 135 125" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead6)"/>
                        <path d="M 140 130 L 155 135" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead6)"/>
                        <path d="M 160 140 L 175 145" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead6)"/>
                        <path d="M 180 150 L 195 155" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead6)"/>
                        <path d="M 200 160 L 215 165" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead6)"/>
                        <path d="M 220 170 L 235 175" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead6)"/>
                        <path d="M 240 180 L 255 185" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead6)"/>
                        <path d="M 260 190 L 275 195" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead6)"/>
                        <path d="M 280 200 L 295 205" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead6)"/>

                        <path d="M 130 180 L 140 195" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead6)"/>
                        <path d="M 150 190 L 160 205" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead6)"/>
                        <path d="M 170 200 L 180 215" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead6)"/>
                        <path d="M 190 210 L 200 225" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead6)"/>
                        <path d="M 210 220 L 220 235" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead6)"/>
                        <path d="M 230 230 L 240 245" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead6)"/>

                        <path d="M 250 120 L 265 115" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead6)"/>
                        <path d="M 270 130 L 285 125" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead6)"/>
                        <path d="M 290 140 L 305 135" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead6)"/>
                        <path d="M 310 150 L 325 145" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead6)"/>

                        <!-- Right panel: Stream plot -->
                        <rect x="450" y="60" width="300" height="200" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="600" y="80" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Stream Plot</text>

                        <!-- Stream lines -->
                        <path d="M 470 120 Q 520 100 570 120 Q 620 140 670 120 Q 720 100 730 120"
                              stroke="#9c27b0" stroke-width="3" fill="none" opacity="0.7"/>
                        <path d="M 470 160 Q 520 140 570 160 Q 620 180 670 160 Q 720 140 730 160"
                              stroke="#9c27b0" stroke-width="3" fill="none" opacity="0.7"/>
                        <path d="M 470 200 Q 520 180 570 200 Q 620 220 670 200 Q 720 180 730 200"
                              stroke="#9c27b0" stroke-width="3" fill="none" opacity="0.7"/>

                        <!-- Cells along streams -->
                        <circle cx="480" cy="120" r="3" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <circle cx="520" cy="100" r="3" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <circle cx="570" cy="120" r="3" fill="#fff3e0" stroke="#f57c00" stroke-width="1"/>
                        <circle cx="620" cy="140" r="3" fill="#fff3e0" stroke="#f57c00" stroke-width="1"/>
                        <circle cx="670" cy="120" r="3" fill="#e8f5e8" stroke="#4caf50" stroke-width="1"/>
                        <circle cx="720" cy="100" r="3" fill="#e8f5e8" stroke="#4caf50" stroke-width="1"/>

                        <!-- Bottom panels: Phase portraits -->
                        <rect x="50" y="280" width="200" height="150" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="150" y="300" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Phase Portrait</text>

                        <!-- Phase portrait axes -->
                        <line x1="80" y1="400" x2="220" y2="400" stroke="#333" stroke-width="1"/>
                        <line x1="80" y1="400" x2="80" y2="320" stroke="#333" stroke-width="1"/>
                        <text x="150" y="420" text-anchor="middle" font-size="9" fill="#666">Spliced</text>
                        <text x="70" y="360" text-anchor="middle" font-size="9" fill="#666" transform="rotate(-90 70 360)">Unspliced</text>

                        <!-- Phase portrait trajectory -->
                        <path d="M 90 380 Q 120 350 150 360 Q 180 370 210 340"
                              stroke="#2196f3" stroke-width="2" fill="none"/>
                        <circle cx="90" cy="380" r="3" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <circle cx="150" cy="360" r="3" fill="#fff3e0" stroke="#f57c00" stroke-width="1"/>
                        <circle cx="210" cy="340" r="3" fill="#e8f5e8" stroke="#4caf50" stroke-width="1"/>

                        <!-- Gene expression trends -->
                        <rect x="300" y="280" width="200" height="150" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="400" y="300" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Expression Trends</text>

                        <!-- Trend lines -->
                        <line x1="320" y1="410" x2="480" y2="410" stroke="#333" stroke-width="1"/>
                        <line x1="320" y1="410" x2="320" y2="320" stroke="#333" stroke-width="1"/>
                        <text x="400" y="425" text-anchor="middle" font-size="9" fill="#666">Pseudotime</text>
                        <text x="310" y="365" text-anchor="middle" font-size="9" fill="#666" transform="rotate(-90 310 365)">Expression</text>

                        <path d="M 330 390 Q 360 370 390 350 Q 420 330 450 320"
                              stroke="#e74c3c" stroke-width="2" fill="none"/>
                        <path d="M 330 380 Q 360 360 390 340 Q 420 350 450 360"
                              stroke="#2196f3" stroke-width="2" fill="none"/>

                        <text x="460" y="325" font-size="8" fill="#e74c3c">Gene A</text>
                        <text x="460" y="365" font-size="8" fill="#2196f3">Gene B</text>

                        <!-- Interpretation panel -->
                        <rect x="550" y="280" width="200" height="150" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="650" y="300" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Interpretation Guide</text>

                        <text x="570" y="320" font-size="10" fill="#333">• Arrow direction: future state</text>
                        <text x="570" y="335" font-size="10" fill="#333">• Arrow length: velocity magnitude</text>
                        <text x="570" y="350" font-size="10" fill="#333">• Stream lines: trajectories</text>
                        <text x="570" y="365" font-size="10" fill="#333">• Phase portraits: gene dynamics</text>
                        <text x="570" y="380" font-size="10" fill="#333">• Trends: temporal patterns</text>

                        <circle cx="560" cy="395" r="3" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <text x="570" y="400" font-size="9" fill="#666">Early state</text>
                        <circle cx="560" cy="405" r="3" fill="#fff3e0" stroke="#f57c00" stroke-width="1"/>
                        <text x="570" y="410" font-size="9" fill="#666">Intermediate</text>
                        <circle cx="560" cy="415" r="3" fill="#e8f5e8" stroke="#4caf50" stroke-width="1"/>
                        <text x="570" y="420" font-size="9" fill="#666">Late state</text>

                        <!-- Arrow marker definition -->
                        <defs>
                            <marker id="arrowhead6" markerWidth="6" markerHeight="4" refX="5" refY="2" orient="auto">
                                <polygon points="0 0, 6 2, 0 4" fill="#e74c3c"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>8.2 Biological Interpretation</h3>
                <p>Understanding what RNA velocity tells us about cellular processes:</p>

                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>Positive Velocity:</strong> Gene expression is increasing
                        <ul style="margin-top: 0.5rem;">
                            <li>High unspliced, low spliced mRNA</li>
                            <li>Active transcription</li>
                            <li>Cell moving toward higher expression state</li>
                        </ul>
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>Negative Velocity:</strong> Gene expression is decreasing
                        <ul style="margin-top: 0.5rem;">
                            <li>Low unspliced, high spliced mRNA</li>
                            <li>Reduced transcription</li>
                            <li>Cell moving toward lower expression state</li>
                        </ul>
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div>
                        <strong>Zero Velocity:</strong> Gene expression at steady state
                        <ul style="margin-top: 0.5rem;">
                            <li>Balanced unspliced/spliced ratio</li>
                            <li>Stable transcription rate</li>
                            <li>Cell in equilibrium state</li>
                        </ul>
                    </div>
                </div>

                <h3>8.3 Common Applications</h3>
                <div class="math-box">
                    <p><strong>RNA Velocity Applications:</strong></p>
                    <ul>
                        <li><strong>Developmental Biology:</strong> Trace cell fate decisions and lineage progression</li>
                        <li><strong>Disease Research:</strong> Understand pathological state transitions</li>
                        <li><strong>Drug Discovery:</strong> Predict cellular responses to treatments</li>
                        <li><strong>Stem Cell Biology:</strong> Map differentiation trajectories</li>
                        <li><strong>Cancer Research:</strong> Identify metastatic progression patterns</li>
                    </ul>
                </div>

                <div class="highlight" style="display: block; text-align: center; padding: 1rem; margin: 2rem 0; font-size: 1.1rem;">
                    🎯 <strong>Key Takeaway:</strong> RNA velocity provides a computational microscope to observe cellular state transitions in real-time, revealing the dynamic nature of biological systems that static snapshots cannot capture.
                </div>
            </div>
        </div>
    </div>
</body>
</html>
