::

	Usage: velocyto run [OPTIONS] BAMFILE... GTFFILE
		
		  Runs the velocity analysis outputing a loom file
		
		  BAMFILE bam file with sorted reads
		
		  GTFFILE genome annotation file
		
		Options:
		  -b, --bcfile PATH               Valid barcodes file, to filter the bam. If --bcfile is not specified all the cell barcodes will be incuded.
		                                  Cell barcodes should be specified in the bcfile as the `CB` tag for each read
		  -o, --outputfolder PATH         Output folder, if it does not exist it will be created.
		  -e, --sampleid PATH             The sample name that will be used to retrieve informations from metadatatable
		  -s, --metadatatable PATH        Table containing metadata of the various samples (csv formatted, rows are samples and cols are entries)
		  -m, --mask PATH                 .gtf file containing intervals to mask
		  -c, --onefilepercell            If this flag is used every bamfile passed is interpreted as an independent cell, otherwise multiple files are interpreted as batch of different cells to be analized together.
		                                  Important: cells reads should not be distributed over multiple bamfiles is not supported!!
		                                  (default: off)
		  -l, --logic TEXT                The logic to use for the filtering (default: Default)
		  -U, --without-umi               If this flag is used the data is assumed UMI-less and reads are counted instead of molecules (default: off)
		  -u, --umi-extension TEXT        In case UMI is too short to guarantee uniqueness (without information from the ampping) set this parameter to `chr`, `Gene` ro `[N]bp`
		                                  If set to `chr` the mapping position (binned to 10Gb intervals) will be appended to `UB` (ideal for InDrops+dropEst). If set to
		                                  `Gene` then the `GX` tag will be appended to the `UB` tag.
		                                  If set to `[N]bp` the first N bases of the sequence will be used to extend `UB` (ideal for STRT). (Default: `no`)
		  -M, --multimap                  Consider not unique mappings (not reccomended)
		  -@, --samtools-threads INTEGER  The number of threads to use to sort the bam by cellID file using samtools
		  --samtools-memory INTEGER       The number of MB used for every thread by samtools to sort the bam file
		  -d, --dump TEXT                 For debugging purposes only: it will dump a molecular mapping report to hdf5. --dump N, saves a cell every N cells. If p is prepended a more complete (but huge) pickle report is printed (default: 0)
		  -v, --verbose                   Set the vebosity level: -v (only warinings) -vv (warinings and info) -vvv (warinings, info and debug)
		  --help                          Show this message and exit.
		