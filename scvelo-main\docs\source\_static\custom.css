/* ReadTheDocs theme colors */

.wy-nav-top {
    background-color: #404040;
}
.wy-nav-content {
    max-width: 950px;
}
.wy-side-nav-search {
    background-color: transparent;
}
.wy-side-nav-search input[type="text"] {
    border-width: 0;
}

/* Custom classes */
.small {
    font-size: 40%;
}
.smaller,
.pr {
    font-size: 70%;
}

/* Custom classes with bootstrap buttons */

.tutorial,
.tutorial:visited,
.tutorial:hover {
    /* text-decoration: underline; */
    font-weight: bold;
    padding: 2px 5px;
    white-space: nowrap;
    max-width: 100%;
    background: #ef3270;
    border: solid 1px #ef3270;
    border-radius: 0.25rem;
    font-size: 75%;
    /* font-family: SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",Courier,monospace; */
    color: #404040;
    overflow-x: auto;
    box-sizing: border-box;
}

/* Formatting of RTD markup: rubrics and sidebars and admonitions */

/* rubric */
.rst-content p.rubric {
    margin-bottom: 6px;
    font-weight: normal;
}
.rst-content p.rubric::after {
    content: ":";
}

/* sidebar */
.rst-content .sidebar {
    /* margin: 0px 0px 0px 12px; */
    padding-bottom: 0px;
}
.rst-content .sidebar p {
    margin-bottom: 12px;
}
.rst-content .sidebar p,
.rst-content .sidebar ul,
.rst-content .sidebar dl {
    font-size: 13px;
}

/* less space after bullet lists in admonitions like warnings and notes */
.rst-content .section .admonition ul {
    margin-bottom: 6px;
}

/* Code: literals and links */

.rst-content tt.literal,
.rst-content code.literal {
    color: #404040;
}
/* slim font weight for non-link code */
.rst-content tt:not(.xref),
.rst-content code:not(.xref),
.rst-content *:not(a) > tt.xref,
.rst-content *:not(a) > code.xref,
.rst-content a > tt.xref,
.rst-content a > code.xref,
.rst-content dl:not(.docutils) a > tt.xref,


/* Just one box for annotation code for a less noisy look */

.rst-content .annotation {
    padding: 2px 5px;
    background-color: white;
    border: 1px solid #e1e4e5;
}
.rst-content .annotation tt,
.rst-content .annotation code {
    padding: 0 0;
    background-color: transparent;
    border: 0 solid transparent;
}

/* Parameter lists */

.rst-content dl:not(.docutils) dl dt {
    /* mimick numpydoc’s blockquote style */
    font-weight: normal;
    background: none transparent;
    border-left: none;
    margin: 0 0 12px;
    padding: 3px 0 0;
    font-size: 100%;
}

.rst-content dl:not(.docutils) dl dt code {
    font-size: 100%;
    font-weight: normal;
    background: none transparent;
    border: none;
    padding: 0 2px;
}

.rst-content dl:not(.docutils) dl dt a.reference > code {
    text-decoration: underline;
}

/* Mimick rubric style used for other headings */
.rst-content dl:not(.docutils) dl > dt {
    font-weight: bold;
    background: none transparent;
    border-left: none;
    margin: 0 0 12px;
    padding: 3px 0 0;
    font-size: 100%;
}
/* Parameters contain <strong> parts and don’t need bold font */
.rst-content dl.field-list dl > dt {
    font-weight: unset;
}
/* Add colon between return tuple element name and type */
.rst-content dl:not(.docutils) dl > dt .classifier::before {
    content: " : ";
}

/* Function headers */

.rst-content dl:not(.docutils) dt {
    background: #edf0f2;
    color: #404040;
    border-top: solid 3px #343131;
}

.rst-content .section ul li p:last-child {
    margin-bottom: 0;
    margin-top: 0;
}

/* Adjust width of navigation bar on mobile */
@media screen and (max-width: 768px) {
    .header-bar {
        display: none;
    }

    .wy-nav-content-wrap {
        margin-left: 0px;
    }

    .wy-nav-side {
        width: 300px;
    }

    .wy-nav-side.shift {
        max-width: 320px;
    }

    /* Fix sidebar adjust */
    .rst-versions {
        width: 40%;
        max-width: 320px;
    }
}

/* Handle landscape */
@media screen and (min-width: 377px) {
    .wy-nav-content-wrap.shift {
        left: 320px;
    }
}

/* make height responsive for notebook figures */
.rst-content .image-reference img {
    max-width: 100% !important;
    height: auto !important;
}
