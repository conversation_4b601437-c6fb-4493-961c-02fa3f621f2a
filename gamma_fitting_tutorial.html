<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Understanding Gamma Fitting in RNA Velocity</title>
    
    <!-- MathJax 3 for LaTeX rendering -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #e74c3c 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 1rem 0 0 0;
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border-left: 4px solid #e74c3c;
            background: #f8f9fa;
            border-radius: 0 8px 8px 0;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.8rem;
            border-bottom: 2px solid #e74c3c;
            padding-bottom: 0.5rem;
        }
        
        .section h3 {
            color: #34495e;
            margin-top: 2rem;
            font-size: 1.4rem;
        }
        
        .math-box {
            background: #fff;
            border: 2px solid #e74c3c;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            box-shadow: 0 2px 10px rgba(231, 76, 60, 0.1);
        }
        
        .code-box {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .step-number {
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
        }
        
        .workflow-step {
            display: flex;
            align-items: flex-start;
            margin: 1.5rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .svg-container {
            text-align: center;
            margin: 2rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 2px solid #17a2b8;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }
        
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 0.75rem;
            text-align: left;
        }
        
        .comparison-table th {
            background: #e74c3c;
            color: white;
            font-weight: bold;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧮 Understanding Gamma (γ) in RNA Velocity</h1>
            <p>Deep Dive into the fit_gammas() Function and Its Mathematical Foundation</p>
        </div>
        
        <div class="content">
            <!-- Section 1: What is Gamma? -->
            <div class="section">
                <h2>1. What is Gamma (γ)?</h2>
                
                <p><span class="highlight">Gamma (γ)</span> is a crucial parameter in RNA velocity analysis that represents the <strong>ratio of degradation rate to splicing rate</strong> for each gene. It's the key parameter that connects unspliced and spliced mRNA abundances at steady state.</p>
                
                <div class="math-box">
                    <p><strong>Mathematical Definition:</strong></p>
                    $$\gamma = \frac{\text{degradation rate}}{\text{splicing rate}} = \frac{\delta}{\beta}$$
                    
                    <p>At steady state, the relationship between unspliced (u) and spliced (s) mRNA is:</p>
                    $$u_{steady} = \gamma \cdot s_{steady}$$
                    
                    <p>This means gamma tells us: <em>"For every unit of spliced mRNA, how much unspliced mRNA should we expect at equilibrium?"</em></p>
                </div>
                
                <div class="info-box">
                    <strong>🔍 Key Insight:</strong> Gamma is NOT the degradation rate itself, but rather a ratio that captures the steady-state relationship between unspliced and spliced mRNA. This ratio is what we can actually estimate from single-cell data.
                </div>
                
                <div class="svg-container">
                    <svg width="800" height="400" viewBox="0 0 800 400">
                        <!-- Background -->
                        <rect width="800" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
                        
                        <!-- Title -->
                        <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Gamma Represents the Steady-State Slope</text>
                        
                        <!-- Axes -->
                        <line x1="100" y1="350" x2="700" y2="350" stroke="#333" stroke-width="2"/>
                        <line x1="100" y1="350" x2="100" y2="80" stroke="#333" stroke-width="2"/>
                        
                        <!-- Axis labels -->
                        <text x="400" y="380" text-anchor="middle" font-size="14" fill="#333">Spliced mRNA (s)</text>
                        <text x="70" y="215" text-anchor="middle" font-size="14" fill="#333" transform="rotate(-90 70 215)">Unspliced mRNA (u)</text>
                        
                        <!-- Data points scattered around the line -->
                        <circle cx="150" cy="300" r="4" fill="#3498db" opacity="0.7"/>
                        <circle cx="200" cy="280" r="4" fill="#3498db" opacity="0.7"/>
                        <circle cx="250" cy="260" r="4" fill="#3498db" opacity="0.7"/>
                        <circle cx="300" cy="240" r="4" fill="#3498db" opacity="0.7"/>
                        <circle cx="350" cy="220" r="4" fill="#3498db" opacity="0.7"/>
                        <circle cx="400" cy="200" r="4" fill="#3498db" opacity="0.7"/>
                        <circle cx="450" cy="180" r="4" fill="#3498db" opacity="0.7"/>
                        <circle cx="500" cy="160" r="4" fill="#3498db" opacity="0.7"/>
                        <circle cx="550" cy="140" r="4" fill="#3498db" opacity="0.7"/>
                        <circle cx="600" cy="120" r="4" fill="#3498db" opacity="0.7"/>
                        
                        <!-- Scattered points around the line -->
                        <circle cx="170" cy="290" r="4" fill="#3498db" opacity="0.7"/>
                        <circle cx="180" cy="310" r="4" fill="#3498db" opacity="0.7"/>
                        <circle cx="220" cy="270" r="4" fill="#3498db" opacity="0.7"/>
                        <circle cx="270" cy="250" r="4" fill="#3498db" opacity="0.7"/>
                        <circle cx="320" cy="230" r="4" fill="#3498db" opacity="0.7"/>
                        <circle cx="380" cy="210" r="4" fill="#3498db" opacity="0.7"/>
                        <circle cx="420" cy="190" r="4" fill="#3498db" opacity="0.7"/>
                        <circle cx="480" cy="170" r="4" fill="#3498db" opacity="0.7"/>
                        <circle cx="520" cy="150" r="4" fill="#3498db" opacity="0.7"/>
                        <circle cx="580" cy="130" r="4" fill="#3498db" opacity="0.7"/>
                        
                        <!-- Best fit line (gamma slope) -->
                        <line x1="100" y1="340" x2="650" y2="100" stroke="#e74c3c" stroke-width="4"/>
                        
                        <!-- Gamma annotation -->
                        <text x="500" y="130" font-size="16" fill="#e74c3c" font-weight="bold">u = γs</text>
                        <text x="500" y="150" font-size="14" fill="#e74c3c">slope = γ</text>
                        
                        <!-- Slope triangle -->
                        <line x1="400" y1="200" x2="500" y2="200" stroke="#ff9800" stroke-width="2"/>
                        <line x1="500" y1="200" x2="500" y2="160" stroke="#ff9800" stroke-width="2"/>
                        <line x1="400" y1="200" x2="500" y2="160" stroke="#ff9800" stroke-width="2" stroke-dasharray="3,3"/>
                        
                        <text x="450" y="215" text-anchor="middle" font-size="12" fill="#ff9800">Δs</text>
                        <text x="515" y="180" font-size="12" fill="#ff9800">Δu</text>
                        <text x="430" y="175" font-size="12" fill="#ff9800" font-weight="bold">γ = Δu/Δs</text>
                        
                        <!-- Legend -->
                        <rect x="50" y="50" width="200" height="80" fill="white" stroke="#ccc" stroke-width="1"/>
                        <circle cx="70" cy="70" r="4" fill="#3498db"/>
                        <text x="85" y="75" font-size="12" fill="#333">Cell measurements</text>
                        <line x1="70" y1="85" x2="90" y2="85" stroke="#e74c3c" stroke-width="3"/>
                        <text x="100" y="90" font-size="12" fill="#333">Fitted line (γ)</text>
                        <text x="70" y="110" font-size="11" fill="#666">Each gene has its own γ</text>
                    </svg>
                </div>
            </div>

            <!-- Section 2: Why Fit Gamma This Way? -->
            <div class="section">
                <h2>2. Why Fit Gamma This Way?</h2>
                
                <h3>2.1 The Biological Rationale</h3>
                <p>The gamma fitting approach is based on a fundamental assumption about RNA kinetics:</p>
                
                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>Steady-State Assumption:</strong> Many cells in a population are at or near steady state for most genes
                        <div class="math-box" style="margin-top: 0.5rem;">
                            At steady state: $\frac{du}{dt} = 0$ and $\frac{ds}{dt} = 0$
                            <br>This gives us: $u = \frac{\alpha}{\beta}$ and $s = \frac{\alpha}{\delta}$
                            <br>Therefore: $u = \frac{\delta}{\beta} \cdot s = \gamma \cdot s$
                        </div>
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>Linear Relationship:</strong> At steady state, unspliced and spliced mRNA have a linear relationship
                        <ul style="margin-top: 0.5rem;">
                            <li>The slope of this relationship is gamma (γ)</li>
                            <li>Deviations from this line indicate velocity</li>
                            <li>Points above the line → positive velocity (increasing expression)</li>
                            <li>Points below the line → negative velocity (decreasing expression)</li>
                        </ul>
                    </div>
                </div>
                
                <h3>2.2 Mathematical Justification</h3>
                <div class="math-box">
                    <p><strong>From Kinetic Equations to Linear Regression:</strong></p>
                    
                    <p>Starting from the basic kinetic model:</p>
                    $$\frac{du}{dt} = \alpha - \beta u$$
                    $$\frac{ds}{dt} = \beta u - \delta s$$
                    
                    <p>At steady state ($\frac{du}{dt} = \frac{ds}{dt} = 0$):</p>
                    $$\alpha = \beta u_{ss} \quad \text{and} \quad \beta u_{ss} = \delta s_{ss}$$
                    
                    <p>Solving for the relationship:</p>
                    $$u_{ss} = \frac{\alpha}{\beta} \quad \text{and} \quad s_{ss} = \frac{\alpha}{\delta}$$
                    
                    <p>Therefore:</p>
                    $$u_{ss} = \frac{\delta}{\beta} s_{ss} = \gamma s_{ss}$$
                    
                    <p>This justifies fitting: $u = \gamma s + \epsilon$</p>
                </div>
            </div>

            <!-- Section 3: How fit_gammas() Works -->
            <div class="section">
                <h2>3. How fit_gammas() Works</h2>

                <h3>3.1 The Fitting Process</h3>
                <p>The <code>fit_gammas()</code> function performs weighted least squares regression for each gene individually:</p>

                <div class="code-box">
# Simplified version of what fit_gammas() does:
for each_gene in genes:
    # Get unspliced and spliced data for this gene
    u = unspliced_data[gene, :]  # across all cells
    s = spliced_data[gene, :]    # across all cells

    # Fit: u = gamma * s + offset
    gamma[gene] = minimize(sum(weights * (u - gamma * s - offset)^2))
                </div>

                <h3>3.2 Key Parameters and Options</h3>

                <table class="comparison-table">
                    <tr>
                        <th>Parameter</th>
                        <th>Purpose</th>
                        <th>Default</th>
                        <th>Impact</th>
                    </tr>
                    <tr>
                        <td><code>use_imputed_data</code></td>
                        <td>Use kNN-smoothed data</td>
                        <td>True</td>
                        <td>Reduces noise, improves fit quality</td>
                    </tr>
                    <tr>
                        <td><code>fit_offset</code></td>
                        <td>Include intercept term</td>
                        <td>True</td>
                        <td>Accounts for baseline unspliced levels</td>
                    </tr>
                    <tr>
                        <td><code>weighted</code></td>
                        <td>Use weighted regression</td>
                        <td>True</td>
                        <td>Emphasizes high-expression cells</td>
                    </tr>
                    <tr>
                        <td><code>limit_gamma</code></td>
                        <td>Constrain gamma values</td>
                        <td>False</td>
                        <td>Prevents unrealistic gamma estimates</td>
                    </tr>
                </table>

                <h3>3.3 Weighting Strategies</h3>
                <p>The function supports several weighting strategies to improve fit quality:</p>

                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>"maxmin_diag" (default):</strong> Emphasizes cells with extreme expression values
                        <div class="code-box" style="margin-top: 0.5rem;">
# Weights cells at 2nd and 98th percentiles of combined S+U expression
X = normalized_S + normalized_U
weights = (X <= 2nd_percentile) | (X >= 98th_percentile)
                        </div>
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>"sum":</strong> Weights by total expression level
                        <div class="code-box" style="margin-top: 0.5rem;">
weights = (S/percentile(S,99)) + (U/percentile(U,99))
                        </div>
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div>
                        <strong>"prod":</strong> Weights by product of expression levels
                        <div class="code-box" style="margin-top: 0.5rem;">
weights = (S/percentile(S,99)) * (U/percentile(U,99))
                        </div>
                    </div>
                </div>

                <div class="svg-container">
                    <svg width="800" height="500" viewBox="0 0 800 500">
                        <!-- Background -->
                        <rect width="800" height="500" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Weighting Strategy Visualization</text>

                        <!-- Left panel: Unweighted fit -->
                        <rect x="50" y="60" width="300" height="180" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="200" y="80" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Unweighted Fit</text>

                        <!-- Axes for left plot -->
                        <line x1="80" y1="220" x2="320" y2="220" stroke="#333" stroke-width="1"/>
                        <line x1="80" y1="220" x2="80" y2="100" stroke="#333" stroke-width="1"/>

                        <!-- Data points - all same size -->
                        <circle cx="120" cy="180" r="3" fill="#3498db" opacity="0.7"/>
                        <circle cx="140" cy="170" r="3" fill="#3498db" opacity="0.7"/>
                        <circle cx="160" cy="160" r="3" fill="#3498db" opacity="0.7"/>
                        <circle cx="180" cy="150" r="3" fill="#3498db" opacity="0.7"/>
                        <circle cx="200" cy="140" r="3" fill="#3498db" opacity="0.7"/>
                        <circle cx="220" cy="130" r="3" fill="#3498db" opacity="0.7"/>
                        <circle cx="240" cy="120" r="3" fill="#3498db" opacity="0.7"/>
                        <circle cx="260" cy="110" r="3" fill="#3498db" opacity="0.7"/>

                        <!-- Noisy points -->
                        <circle cx="130" cy="200" r="3" fill="#3498db" opacity="0.7"/>
                        <circle cx="150" cy="190" r="3" fill="#3498db" opacity="0.7"/>
                        <circle cx="170" cy="185" r="3" fill="#3498db" opacity="0.7"/>
                        <circle cx="190" cy="175" r="3" fill="#3498db" opacity="0.7"/>
                        <circle cx="210" cy="165" r="3" fill="#3498db" opacity="0.7"/>
                        <circle cx="230" cy="155" r="3" fill="#3498db" opacity="0.7"/>
                        <circle cx="250" cy="145" r="3" fill="#3498db" opacity="0.7"/>

                        <!-- Poor fit line -->
                        <line x1="80" y1="210" x2="300" y2="120" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                        <text x="250" y="115" font-size="10" fill="#e74c3c">Poor fit</text>

                        <!-- Right panel: Weighted fit -->
                        <rect x="450" y="60" width="300" height="180" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="600" y="80" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Weighted Fit</text>

                        <!-- Axes for right plot -->
                        <line x1="480" y1="220" x2="720" y2="220" stroke="#333" stroke-width="1"/>
                        <line x1="480" y1="220" x2="480" y2="100" stroke="#333" stroke-width="1"/>

                        <!-- Data points - variable sizes for weights -->
                        <circle cx="520" cy="180" r="6" fill="#3498db" opacity="0.9"/>  <!-- High weight -->
                        <circle cx="540" cy="170" r="2" fill="#3498db" opacity="0.3"/>  <!-- Low weight -->
                        <circle cx="560" cy="160" r="2" fill="#3498db" opacity="0.3"/>  <!-- Low weight -->
                        <circle cx="580" cy="150" r="2" fill="#3498db" opacity="0.3"/>  <!-- Low weight -->
                        <circle cx="600" cy="140" r="2" fill="#3498db" opacity="0.3"/>  <!-- Low weight -->
                        <circle cx="620" cy="130" r="2" fill="#3498db" opacity="0.3"/>  <!-- Low weight -->
                        <circle cx="640" cy="120" r="2" fill="#3498db" opacity="0.3"/>  <!-- Low weight -->
                        <circle cx="660" cy="110" r="6" fill="#3498db" opacity="0.9"/>  <!-- High weight -->

                        <!-- Noisy points with low weights -->
                        <circle cx="530" cy="200" r="1" fill="#3498db" opacity="0.2"/>
                        <circle cx="550" cy="190" r="1" fill="#3498db" opacity="0.2"/>
                        <circle cx="570" cy="185" r="1" fill="#3498db" opacity="0.2"/>
                        <circle cx="590" cy="175" r="1" fill="#3498db" opacity="0.2"/>
                        <circle cx="610" cy="165" r="1" fill="#3498db" opacity="0.2"/>
                        <circle cx="630" cy="155" r="1" fill="#3498db" opacity="0.2"/>
                        <circle cx="650" cy="145" r="1" fill="#3498db" opacity="0.2"/>

                        <!-- Good fit line -->
                        <line x1="480" y1="200" x2="700" y2="100" stroke="#27ae60" stroke-width="3"/>
                        <text x="650" y="95" font-size="10" fill="#27ae60" font-weight="bold">Better fit</text>

                        <!-- Legend -->
                        <rect x="50" y="260" width="700" height="120" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="400" y="285" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Why Weighting Helps</text>

                        <circle cx="80" cy="310" r="6" fill="#3498db" opacity="0.9"/>
                        <text x="100" y="315" font-size="12" fill="#333">High weight (extreme expression)</text>

                        <circle cx="80" cy="330" r="2" fill="#3498db" opacity="0.3"/>
                        <text x="100" y="335" font-size="12" fill="#333">Low weight (intermediate expression)</text>

                        <circle cx="80" cy="350" r="1" fill="#3498db" opacity="0.2"/>
                        <text x="100" y="355" font-size="12" fill="#333">Very low weight (noisy/dropout)</text>

                        <text x="400" y="310" font-size="11" fill="#666">• Extreme values are more informative for steady-state relationship</text>
                        <text x="400" y="325" font-size="11" fill="#666">• Intermediate values may be transitioning (not at steady state)</text>
                        <text x="400" y="340" font-size="11" fill="#666">• Low expression values are often affected by dropout noise</text>
                        <text x="400" y="355" font-size="11" fill="#666">• Weighting improves gamma estimation accuracy</text>
                    </svg>
                </div>
            </div>

            <!-- Section 4: What Gamma Tells Us -->
            <div class="section">
                <h2>4. What Gamma Tells Us</h2>

                <h3>4.1 Biological Interpretation</h3>
                <p>The value of gamma provides insights into gene regulation dynamics:</p>

                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>High Gamma (γ > 1):</strong>
                        <ul style="margin-top: 0.5rem;">
                            <li>Slow splicing relative to degradation</li>
                            <li>More unspliced mRNA accumulates</li>
                            <li>Often seen in highly regulated genes</li>
                            <li>Example: Stress response genes, developmental regulators</li>
                        </ul>
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>Low Gamma (γ < 1):</strong>
                        <ul style="margin-top: 0.5rem;">
                            <li>Fast splicing relative to degradation</li>
                            <li>Less unspliced mRNA relative to spliced</li>
                            <li>Often seen in housekeeping genes</li>
                            <li>Example: Ribosomal proteins, metabolic enzymes</li>
                        </ul>
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div>
                        <strong>Gamma ≈ 1:</strong>
                        <ul style="margin-top: 0.5rem;">
                            <li>Balanced splicing and degradation rates</li>
                            <li>Equal amounts of unspliced and spliced mRNA</li>
                            <li>Typical for many moderately expressed genes</li>
                        </ul>
                    </div>
                </div>

                <h3>4.2 Quality Control with Gamma</h3>
                <div class="warning-box">
                    <strong>⚠️ Important:</strong> Not all genes are suitable for velocity analysis. The <code>filter_genes_by_phase_portrait()</code> function uses gamma and R² values to identify genes with reliable velocity estimates.
                </div>

                <div class="code-box">
# Quality filtering based on gamma fitting
vlm.filter_genes_by_phase_portrait(
    minR2=0.1,      # Minimum R² for good linear fit
    min_gamma=0.01, # Minimum gamma (avoid near-zero values)
    minCorr=0.1     # Minimum correlation between U and S
)

# Genes with poor fits are excluded from velocity analysis
print(f"Genes passing filter: {vlm.gammas[vlm.gammas > 0.01].shape[0]}")
                </div>

                <h3>4.3 Gamma in Velocity Calculation</h3>
                <p>Once gamma is fitted, it's used to calculate velocity for each cell:</p>

                <div class="math-box">
                    <p><strong>Velocity Calculation:</strong></p>
                    $$v_{ij} = u_{ij} - \gamma_i s_{ij}$$

                    <p>Where:</p>
                    <ul>
                        <li>$v_{ij}$ = velocity of gene $i$ in cell $j$</li>
                        <li>$u_{ij}$ = observed unspliced count</li>
                        <li>$\gamma_i$ = fitted gamma for gene $i$</li>
                        <li>$s_{ij}$ = observed spliced count</li>
                    </ul>

                    <p><strong>Interpretation:</strong></p>
                    <ul>
                        <li>$v_{ij} > 0$: Gene expression increasing in cell $j$</li>
                        <li>$v_{ij} < 0$: Gene expression decreasing in cell $j$</li>
                        <li>$v_{ij} ≈ 0$: Gene at steady state in cell $j$</li>
                    </ul>
                </div>
            </div>

            <!-- Section 5: Practical Implementation -->
            <div class="section">
                <h2>5. Practical Implementation</h2>

                <h3>5.1 Step-by-Step Gamma Fitting</h3>
                <p>Here's how to properly fit gammas in a real analysis:</p>

                <div class="code-box">
import velocyto as vcy
import numpy as np
import matplotlib.pyplot as plt

# Load data
vlm = vcy.VelocytoLoom("sample.loom")

# 1. Preprocessing (essential for good gamma fits)
vlm.normalize("S", size=True, log=True)
vlm.filter_genes_good_detection()
vlm.score_cv_vs_mean(3000, plot=True)
vlm.filter_genes(by_cv_vs_mean=True)

# 2. Prepare for gamma fitting with kNN smoothing
vlm.perform_PCA()
vlm.knn_imputation(n_pca_dims=25, k=525, balanced=True)

# 3. Fit gammas with different options
vlm.fit_gammas(
    use_imputed_data=True,    # Use smoothed data
    fit_offset=True,          # Include intercept
    weighted=True,            # Use weighted regression
    weights="maxmin_diag",    # Weight extreme values
    limit_gamma=False         # Don't constrain gamma range
)

# 4. Quality control
print(f"Gamma range: {vlm.gammas.min():.3f} to {vlm.gammas.max():.3f}")
print(f"Median R²: {np.median(vlm.R2):.3f}")

# 5. Filter genes with poor fits
vlm.filter_genes_by_phase_portrait(minR2=0.1, min_gamma=0.01)
print(f"Genes after filtering: {np.sum(vlm.gammas > 0)}")
                </div>

                <h3>5.2 Visualizing Gamma Fits</h3>
                <p>Always inspect your gamma fits visually:</p>

                <div class="code-box">
# Plot phase portraits for specific genes
vlm.plot_phase_portraits(["Gene1", "Gene2", "Gene3"])

# This shows:
# - Scatter plot of unspliced vs spliced
# - Fitted line with slope = gamma
# - R² value and gamma estimate
# - Velocity vectors for individual cells
                </div>

                <div class="svg-container">
                    <svg width="800" height="400" viewBox="0 0 800 400">
                        <!-- Background -->
                        <rect width="800" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Phase Portrait Examples</text>

                        <!-- Good fit example -->
                        <rect x="50" y="60" width="200" height="150" fill="white" stroke="#27ae60" stroke-width="2"/>
                        <text x="150" y="80" text-anchor="middle" font-size="12" font-weight="bold" fill="#27ae60">Good Fit (R² = 0.85)</text>

                        <!-- Axes -->
                        <line x1="70" y1="190" x2="230" y2="190" stroke="#333" stroke-width="1"/>
                        <line x1="70" y1="190" x2="70" y2="100" stroke="#333" stroke-width="1"/>

                        <!-- Data points tightly around line -->
                        <circle cx="90" cy="170" r="2" fill="#3498db"/>
                        <circle cx="110" cy="160" r="2" fill="#3498db"/>
                        <circle cx="130" cy="150" r="2" fill="#3498db"/>
                        <circle cx="150" cy="140" r="2" fill="#3498db"/>
                        <circle cx="170" cy="130" r="2" fill="#3498db"/>
                        <circle cx="190" cy="120" r="2" fill="#3498db"/>
                        <circle cx="210" cy="110" r="2" fill="#3498db"/>

                        <!-- Tight scatter -->
                        <circle cx="95" cy="165" r="2" fill="#3498db"/>
                        <circle cx="115" cy="155" r="2" fill="#3498db"/>
                        <circle cx="135" cy="145" r="2" fill="#3498db"/>
                        <circle cx="155" cy="135" r="2" fill="#3498db"/>
                        <circle cx="175" cy="125" r="2" fill="#3498db"/>
                        <circle cx="195" cy="115" r="2" fill="#3498db"/>

                        <!-- Fit line -->
                        <line x1="70" y1="180" x2="220" y2="105" stroke="#27ae60" stroke-width="2"/>
                        <text x="180" y="100" font-size="10" fill="#27ae60">γ = 0.65</text>

                        <!-- Poor fit example -->
                        <rect x="300" y="60" width="200" height="150" fill="white" stroke="#e74c3c" stroke-width="2"/>
                        <text x="400" y="80" text-anchor="middle" font-size="12" font-weight="bold" fill="#e74c3c">Poor Fit (R² = 0.15)</text>

                        <!-- Axes -->
                        <line x1="320" y1="190" x2="480" y2="190" stroke="#333" stroke-width="1"/>
                        <line x1="320" y1="190" x2="320" y2="100" stroke="#333" stroke-width="1"/>

                        <!-- Scattered data points -->
                        <circle cx="340" cy="180" r="2" fill="#3498db"/>
                        <circle cx="360" cy="120" r="2" fill="#3498db"/>
                        <circle cx="380" cy="170" r="2" fill="#3498db"/>
                        <circle cx="400" cy="110" r="2" fill="#3498db"/>
                        <circle cx="420" cy="160" r="2" fill="#3498db"/>
                        <circle cx="440" cy="130" r="2" fill="#3498db"/>
                        <circle cx="460" cy="150" r="2" fill="#3498db"/>

                        <!-- More scattered points -->
                        <circle cx="350" cy="140" r="2" fill="#3498db"/>
                        <circle cx="370" cy="180" r="2" fill="#3498db"/>
                        <circle cx="390" cy="100" r="2" fill="#3498db"/>
                        <circle cx="410" cy="170" r="2" fill="#3498db"/>
                        <circle cx="430" cy="120" r="2" fill="#3498db"/>
                        <circle cx="450" cy="180" r="2" fill="#3498db"/>

                        <!-- Poor fit line -->
                        <line x1="320" y1="170" x2="470" y2="140" stroke="#e74c3c" stroke-width="2" stroke-dasharray="3,3"/>
                        <text x="430" y="135" font-size="10" fill="#e74c3c">γ = 0.12</text>

                        <!-- No relationship example -->
                        <rect x="550" y="60" width="200" height="150" fill="white" stroke="#95a5a6" stroke-width="2"/>
                        <text x="650" y="80" text-anchor="middle" font-size="12" font-weight="bold" fill="#95a5a6">No Relationship (R² = 0.02)</text>

                        <!-- Axes -->
                        <line x1="570" y1="190" x2="730" y2="190" stroke="#333" stroke-width="1"/>
                        <line x1="570" y1="190" x2="570" y2="100" stroke="#333" stroke-width="1"/>

                        <!-- Random scattered points -->
                        <circle cx="590" cy="150" r="2" fill="#3498db"/>
                        <circle cx="610" cy="180" r="2" fill="#3498db"/>
                        <circle cx="630" cy="110" r="2" fill="#3498db"/>
                        <circle cx="650" cy="170" r="2" fill="#3498db"/>
                        <circle cx="670" cy="120" r="2" fill="#3498db"/>
                        <circle cx="690" cy="160" r="2" fill="#3498db"/>
                        <circle cx="710" cy="140" r="2" fill="#3498db"/>

                        <circle cx="600" cy="130" r="2" fill="#3498db"/>
                        <circle cx="620" cy="160" r="2" fill="#3498db"/>
                        <circle cx="640" cy="180" r="2" fill="#3498db"/>
                        <circle cx="660" cy="100" r="2" fill="#3498db"/>
                        <circle cx="680" cy="170" r="2" fill="#3498db"/>
                        <circle cx="700" cy="150" r="2" fill="#3498db"/>

                        <!-- Flat line -->
                        <line x1="570" y1="145" x2="720" y2="145" stroke="#95a5a6" stroke-width="2" stroke-dasharray="3,3"/>
                        <text x="680" y="140" font-size="10" fill="#95a5a6">γ ≈ 0</text>

                        <!-- Bottom explanation -->
                        <rect x="50" y="230" width="700" height="120" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="400" y="255" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Interpreting Phase Portraits</text>

                        <text x="70" y="280" font-size="12" fill="#27ae60" font-weight="bold">✓ Good Fit:</text>
                        <text x="70" y="295" font-size="11" fill="#666">• Clear linear relationship</text>
                        <text x="70" y="310" font-size="11" fill="#666">• High R² (> 0.3)</text>
                        <text x="70" y="325" font-size="11" fill="#666">• Reliable velocity estimates</text>

                        <text x="270" y="280" font-size="12" fill="#e74c3c" font-weight="bold">✗ Poor Fit:</text>
                        <text x="270" y="295" font-size="11" fill="#666">• Weak linear relationship</text>
                        <text x="270" y="310" font-size="11" fill="#666">• Low R² (< 0.1)</text>
                        <text x="270" y="325" font-size="11" fill="#666">• Unreliable velocities</text>

                        <text x="470" y="280" font-size="12" fill="#95a5a6" font-weight="bold">✗ No Relationship:</text>
                        <text x="470" y="295" font-size="11" fill="#666">• Random scatter</text>
                        <text x="470" y="310" font-size="11" fill="#666">• Very low R² (< 0.05)</text>
                        <text x="470" y="325" font-size="11" fill="#666">• Exclude from analysis</text>
                    </svg>
                </div>
            </div>

            <!-- Section 6: Common Issues and Solutions -->
            <div class="section">
                <h2>6. Common Issues and Solutions</h2>

                <h3>6.1 Troubleshooting Gamma Fits</h3>

                <div class="warning-box">
                    <strong>Problem:</strong> Many genes have very low R² values
                    <br><strong>Solutions:</strong>
                    <ul>
                        <li>Increase kNN smoothing (higher k parameter)</li>
                        <li>Use more PCA dimensions for kNN</li>
                        <li>Try different weighting strategies</li>
                        <li>Check data quality (dropout rates, sequencing depth)</li>
                    </ul>
                </div>

                <div class="warning-box">
                    <strong>Problem:</strong> Gamma values are unrealistically high (> 10)
                    <br><strong>Solutions:</strong>
                    <ul>
                        <li>Set <code>limit_gamma=True</code></li>
                        <li>Check for outlier cells or genes</li>
                        <li>Improve normalization</li>
                        <li>Consider using <code>fit_offset=True</code></li>
                    </ul>
                </div>

                <div class="warning-box">
                    <strong>Problem:</strong> Too few genes pass quality filtering
                    <br><strong>Solutions:</strong>
                    <ul>
                        <li>Lower filtering thresholds (minR2, min_gamma)</li>
                        <li>Improve preprocessing (better gene filtering)</li>
                        <li>Check if data has sufficient dynamic range</li>
                        <li>Consider different experimental conditions</li>
                    </ul>
                </div>

                <h3>6.2 Advanced Options</h3>

                <div class="code-box">
# Advanced gamma fitting with custom parameters
vlm.fit_gammas(
    steady_state_bool=steady_state_mask,  # Only use specific cells
    use_imputed_data=True,
    use_size_norm=True,
    fit_offset=True,
    weighted=True,
    weights="maxmin_weighted",            # Smoother weighting
    limit_gamma=True,                     # Constrain gamma range
    maxmin_perc=[5, 95],                 # Different percentiles
    maxmin_weighted_pow=10               # Weighting power
)

# Custom weighting matrix
custom_weights = np.ones((n_genes, n_cells))
# ... customize weights based on your criteria ...
vlm.fit_gammas(weights=custom_weights)
                </div>

                <h3>6.3 Biological Considerations</h3>

                <div class="info-box">
                    <strong>🧬 Remember:</strong> Gamma fitting assumes that many cells are at steady state. This works well for:
                    <ul>
                        <li>Homeostatic tissues</li>
                        <li>Cell culture systems</li>
                        <li>Gradual developmental processes</li>
                    </ul>

                    But may be less reliable for:
                    <ul>
                        <li>Rapidly changing systems</li>
                        <li>Stress responses</li>
                        <li>Synchronized cell cycles</li>
                    </ul>
                </div>
            </div>

            <!-- Section 7: Summary -->
            <div class="section">
                <h2>7. Summary</h2>

                <div class="math-box">
                    <p><strong>Key Takeaways about Gamma Fitting:</strong></p>

                    <ol>
                        <li><strong>Gamma (γ) represents the steady-state ratio</strong> of unspliced to spliced mRNA</li>
                        <li><strong>It's estimated by linear regression:</strong> $u = \gamma s + \epsilon$</li>
                        <li><strong>Weighting improves fits</strong> by emphasizing informative data points</li>
                        <li><strong>Quality control is essential</strong> - filter genes with poor fits</li>
                        <li><strong>Gamma enables velocity calculation:</strong> $v = u - \gamma s$</li>
                    </ol>
                </div>

                <div class="highlight" style="display: block; text-align: center; padding: 1rem; margin: 2rem 0; font-size: 1.1rem;">
                    🎯 <strong>Bottom Line:</strong> Gamma fitting is the foundation of RNA velocity analysis. Understanding how it works helps you interpret results correctly and troubleshoot issues when they arise.
                </div>

                <div class="code-box">
# Complete workflow reminder:
vlm = vcy.VelocytoLoom("data.loom")
vlm.normalize("S", size=True, log=True)
vlm.filter_genes_good_detection()
vlm.perform_PCA()
vlm.knn_imputation(n_pca_dims=25, k=525)
vlm.fit_gammas()                          # ← The key step!
vlm.filter_genes_by_phase_portrait()
vlm.predict_U()
vlm.calculate_velocity()
                </div>
            </div>
        </div>
    </div>
</body>
</html>
