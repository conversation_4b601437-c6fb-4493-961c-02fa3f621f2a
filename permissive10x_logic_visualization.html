<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Permissive10X Logic Visualization</title>
    
    <!-- MathJax 3 for LaTeX rendering -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.8rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 1rem 0 0 0;
            font-size: 1.3rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
            border-radius: 0 8px 8px 0;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.8rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        
        .section h3 {
            color: #34495e;
            margin-top: 2rem;
            font-size: 1.4rem;
        }
        
        .code-box {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .svg-container {
            text-align: center;
            margin: 2rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .info-box {
            background: #d1ecf1;
            border: 2px solid #17a2b8;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .success-box {
            background: #d4edda;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .flag-box {
            display: inline-block;
            padding: 0.3rem 0.8rem;
            margin: 0.2rem;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: bold;
        }
        
        .flag-true {
            background: #d4edda;
            color: #155724;
            border: 1px solid #28a745;
        }
        
        .flag-false {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #dc3545;
        }
        
        .spliced {
            background: #d4edda;
            color: #155724;
            border: 2px solid #28a745;
        }
        
        .unspliced {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffc107;
        }
        
        .ambiguous {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #dc3545;
        }
        
        .decision-node {
            padding: 0.8rem 1.2rem;
            border-radius: 8px;
            margin: 0.5rem;
            font-weight: bold;
            text-align: center;
            min-width: 150px;
        }
        
        .decision-check {
            background: #fff3e0;
            border: 2px solid #f57c00;
            color: #e65100;
        }
        
        .result-spliced {
            background: #d4edda;
            border: 2px solid #28a745;
            color: #155724;
        }
        
        .result-unspliced {
            background: #fff3cd;
            border: 2px solid #ffc107;
            color: #856404;
        }
        
        .result-ambiguous {
            background: #f8d7da;
            border: 2px solid #dc3545;
            color: #721c24;
        }
        
        .flow-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .flow-row {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 2rem;
            flex-wrap: wrap;
        }
        
        .arrow {
            font-size: 1.5rem;
            color: #666;
            margin: 0 0.5rem;
        }
        
        .flag-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            font-size: 0.9rem;
        }
        
        .flag-table th,
        .flag-table td {
            border: 1px solid #ddd;
            padding: 0.5rem;
            text-align: center;
        }
        
        .flag-table th {
            background: #3498db;
            color: white;
            font-weight: bold;
        }
        
        .flag-table tr:nth-child(even) {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧬 Permissive10X Logic Visualization</h1>
            <p>Complete Decision Flow for RNA Velocity Read Classification</p>
        </div>
        
        <div class="content">
            <!-- Overview Section -->
            <div class="section">
                <h2>Overview: Permissive10X Classification Logic</h2>
                
                <div class="info-box">
                    <strong>🎯 Purpose:</strong> The Permissive10X class implements the default, most permissive logic for classifying reads in 10X Genomics single-cell RNA-seq data for RNA velocity analysis.
                </div>
                
                <h3>Core Classification Rules</h3>
                <div class="flow-container">
                    <div class="flow-row">
                        <div class="decision-node result-spliced">
                            <strong>SPLICED</strong><br>
                            Exonic reads only
                        </div>
                        <div class="decision-node result-unspliced">
                            <strong>UNSPLICED</strong><br>
                            Intronic or spanning reads
                        </div>
                        <div class="decision-node result-ambiguous">
                            <strong>AMBIGUOUS</strong><br>
                            Multi-gene or unclear
                        </div>
                    </div>
                </div>
                
                <div class="code-box">
# Return codes from the count() method:
# 0 = Successfully classified (spliced/unspliced/ambiguous)
# 1 = Multi-gene mapping (not counted)
# 2 = No gene mapping (not counted)  
# 3 = Multiple genes detected
# 4 = Unhandled edge case
                </div>
            </div>

            <!-- Main Decision Flow -->
            <div class="section">
                <h2>Complete Decision Flow Diagram</h2>

                <div class="svg-container">
                    <svg width="1400" height="1200" viewBox="0 0 1400 1200">
                        <!-- Background -->
                        <rect width="1400" height="1200" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="700" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">Permissive10X Classification Decision Tree</text>

                        <!-- Start Node -->
                        <ellipse cx="700" cy="80" rx="100" ry="30" fill="#e3f2fd" stroke="#1976d2" stroke-width="3"/>
                        <text x="700" y="88" text-anchor="middle" font-size="14" font-weight="bold" fill="#1976d2">Input: molitem</text>

                        <!-- First Check: Has mappings? -->
                        <rect x="620" y="130" width="160" height="50" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="8"/>
                        <text x="700" y="150" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">len(mappings_record)</text>
                        <text x="700" y="165" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">== 0?</text>

                        <!-- No mappings result -->
                        <rect x="200" y="220" width="120" height="40" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="5"/>
                        <text x="260" y="245" text-anchor="middle" font-size="11" font-weight="bold" fill="#721c24">Return 2</text>
                        <text x="260" y="275" text-anchor="middle" font-size="9" fill="#721c24">(No gene mapping)</text>

                        <!-- Second Check: Single gene? -->
                        <rect x="620" y="220" width="160" height="50" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="8"/>
                        <text x="700" y="240" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">Single gene?</text>
                        <text x="700" y="255" text-anchor="middle" font-size="11" fill="#f57c00">len(set(geneids)) == 1</text>

                        <!-- Multi-gene result -->
                        <rect x="1000" y="220" width="120" height="40" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="5"/>
                        <text x="1060" y="245" text-anchor="middle" font-size="11" font-weight="bold" fill="#721c24">Return 3</text>
                        <text x="1060" y="275" text-anchor="middle" font-size="9" fill="#721c24">(Multiple genes)</text>

                        <!-- Feature Analysis Box -->
                        <rect x="500" y="320" width="400" height="120" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="8"/>
                        <text x="700" y="345" text-anchor="middle" font-size="14" font-weight="bold" fill="#2e7d32">Feature Analysis Phase</text>
                        <text x="520" y="370" font-size="11" fill="#2e7d32">For each transcript model:</text>
                        <text x="530" y="390" font-size="10" fill="#666">• Analyze segment mappings</text>
                        <text x="530" y="405" font-size="10" fill="#666">• Check exon/intron overlaps</text>
                        <text x="530" y="420" font-size="10" fill="#666">• Validate intron evidence</text>

                        <!-- Boolean Flags -->
                        <rect x="100" y="480" width="1200" height="100" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="700" y="505" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Boolean Classification Flags</text>

                        <text x="120" y="530" font-size="11" fill="#666">has_onlyexo_model</text>
                        <text x="300" y="530" font-size="11" fill="#666">has_onlyintron_model</text>
                        <text x="500" y="530" font-size="11" fill="#666">has_mixed_model</text>
                        <text x="700" y="530" font-size="11" fill="#666">has_validated_intron</text>
                        <text x="900" y="530" font-size="11" fill="#666">has_exin_intron_span</text>
                        <text x="1100" y="530" font-size="11" fill="#666">multi_gene</text>

                        <text x="120" y="550" font-size="11" fill="#666">has_onlyintron_and_valid_model</text>
                        <text x="400" y="550" font-size="11" fill="#666">has_valid_mixed_model</text>
                        <text x="650" y="550" font-size="11" fill="#666">has_invalid_mixed_model</text>
                        <text x="900" y="550" font-size="11" fill="#666">has_only_span_exin_model</text>

                        <!-- Classification Logic Tree -->
                        <text x="700" y="620" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Classification Decision Tree</text>

                        <!-- Multi-gene check -->
                        <rect x="620" y="650" width="160" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="700" y="675" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">multi_gene?</text>

                        <rect x="200" y="720" width="120" height="40" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="5"/>
                        <text x="260" y="745" text-anchor="middle" font-size="11" font-weight="bold" fill="#721c24">Return 1</text>

                        <!-- Exon-only check -->
                        <rect x="620" y="720" width="160" height="50" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="700" y="740" text-anchor="middle" font-size="10" font-weight="bold" fill="#f57c00">has_onlyexo_model &amp;&amp;</text>
                        <text x="700" y="755" text-anchor="middle" font-size="10" font-weight="bold" fill="#f57c00">!has_onlyintron_model &amp;&amp;</text>
                        <text x="700" y="770" text-anchor="middle" font-size="10" font-weight="bold" fill="#f57c00">!has_mixed_model?</text>

                        <rect x="1000" y="720" width="120" height="40" fill="#d4edda" stroke="#28a745" stroke-width="2" rx="5"/>
                        <text x="1060" y="745" text-anchor="middle" font-size="11" font-weight="bold" fill="#155724">SPLICED</text>

                        <!-- Spanning check -->
                        <rect x="620" y="800" width="160" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="700" y="825" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">has_only_span_exin_model?</text>

                        <rect x="1000" y="800" width="120" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5"/>
                        <text x="1060" y="825" text-anchor="middle" font-size="11" font-weight="bold" fill="#856404">UNSPLICED</text>

                        <!-- Validated intron check -->
                        <rect x="620" y="880" width="160" height="50" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="700" y="900" text-anchor="middle" font-size="10" font-weight="bold" fill="#f57c00">has_onlyintron_and_valid_model</text>
                        <text x="700" y="915" text-anchor="middle" font-size="10" font-weight="bold" fill="#f57c00">&amp;&amp; !has_mixed_model?</text>

                        <rect x="1000" y="880" width="120" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5"/>
                        <text x="1060" y="905" text-anchor="middle" font-size="11" font-weight="bold" fill="#856404">UNSPLICED</text>

                        <!-- Additional cases -->
                        <rect x="620" y="960" width="160" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="700" y="985" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">More complex cases...</text>

                        <rect x="1000" y="960" width="120" height="40" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="5"/>
                        <text x="1060" y="985" text-anchor="middle" font-size="11" font-weight="bold" fill="#721c24">AMBIGUOUS</text>

                        <!-- Arrows -->
                        <path d="M 700 110 L 700 130" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                        <path d="M 650 155 L 260 220" stroke="#dc3545" stroke-width="2" marker-end="url(#arrow)"/>
                        <text x="400" y="185" text-anchor="middle" font-size="10" fill="#dc3545">YES</text>

                        <path d="M 700 180 L 700 220" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                        <text x="720" y="200" font-size="10" fill="#666">NO</text>

                        <path d="M 750 245 L 1060 220" stroke="#dc3545" stroke-width="2" marker-end="url(#arrow)"/>
                        <text x="900" y="230" text-anchor="middle" font-size="10" fill="#dc3545">NO</text>

                        <path d="M 700 270 L 700 320" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                        <text x="720" y="295" font-size="10" fill="#666">YES</text>

                        <path d="M 700 440 L 700 480" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                        <path d="M 700 580 L 700 650" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>

                        <path d="M 650 670 L 260 720" stroke="#dc3545" stroke-width="2" marker-end="url(#arrow)"/>
                        <text x="400" y="690" text-anchor="middle" font-size="10" fill="#dc3545">YES</text>

                        <path d="M 700 690 L 700 720" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                        <text x="720" y="705" font-size="10" fill="#666">NO</text>

                        <path d="M 780 745 L 1000 740" stroke="#28a745" stroke-width="2" marker-end="url(#arrow)"/>
                        <text x="890" y="735" text-anchor="middle" font-size="10" fill="#28a745">YES</text>

                        <path d="M 700 770 L 700 800" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                        <text x="720" y="785" font-size="10" fill="#666">NO</text>

                        <path d="M 780 820 L 1000 820" stroke="#ffc107" stroke-width="2" marker-end="url(#arrow)"/>
                        <text x="890" y="815" text-anchor="middle" font-size="10" fill="#ffc107">YES</text>

                        <path d="M 700 840 L 700 880" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                        <text x="720" y="860" font-size="10" fill="#666">NO</text>

                        <path d="M 780 905 L 1000 905" stroke="#ffc107" stroke-width="2" marker-end="url(#arrow)"/>
                        <text x="890" y="900" text-anchor="middle" font-size="10" fill="#ffc107">YES</text>

                        <path d="M 700 930 L 700 960" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                        <text x="720" y="945" font-size="10" fill="#666">NO</text>

                        <path d="M 780 980 L 1000 980" stroke="#dc3545" stroke-width="2" marker-end="url(#arrow)"/>
                        <text x="890" y="975" text-anchor="middle" font-size="10" fill="#dc3545">Various</text>

                        <!-- Arrow marker definition -->
                        <defs>
                            <marker id="arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                            </marker>
                        </defs>

                        <!-- Legend -->
                        <rect x="50" y="1050" width="300" height="120" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="200" y="1075" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Return Codes</text>

                        <rect x="70" y="1090" width="15" height="15" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                        <text x="95" y="1102" font-size="11" fill="#666">0 + SPLICED count</text>

                        <rect x="70" y="1110" width="15" height="15" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                        <text x="95" y="1122" font-size="11" fill="#666">0 + UNSPLICED count</text>

                        <rect x="70" y="1130" width="15" height="15" fill="#f8d7da" stroke="#dc3545" stroke-width="1"/>
                        <text x="95" y="1142" font-size="11" fill="#666">0 + AMBIGUOUS count</text>

                        <rect x="70" y="1150" width="15" height="15" fill="#e0e0e0" stroke="#666" stroke-width="1"/>
                        <text x="95" y="1162" font-size="11" fill="#666">1,2,3,4 = Not counted</text>
                    </svg>
                </div>
            </div>

            <!-- Detailed Classification Scenarios -->
            <div class="section">
                <h2>Detailed Classification Scenarios</h2>

                <h3>All Classification Cases from the Code</h3>
                <p>The Permissive10X logic handles 11 distinct classification scenarios:</p>

                <table class="flag-table">
                    <tr>
                        <th>Scenario</th>
                        <th>Conditions</th>
                        <th>Result</th>
                        <th>Comments</th>
                    </tr>
                    <tr>
                        <td><strong>1. Exon-only</strong></td>
                        <td>
                            <span class="flag-box flag-true">has_onlyexo_model</span><br>
                            <span class="flag-box flag-false">!has_onlyintron_model</span><br>
                            <span class="flag-box flag-false">!has_mixed_model</span>
                        </td>
                        <td><span class="decision-node result-spliced">SPLICED</span></td>
                        <td>Most common case - normal exonic read</td>
                    </tr>
                    <tr>
                        <td><strong>2. Exon-intron spanning</strong></td>
                        <td>
                            <span class="flag-box flag-true">has_only_span_exin_model</span>
                        </td>
                        <td><span class="decision-node result-unspliced">UNSPLICED</span></td>
                        <td>All compatible models span boundaries</td>
                    </tr>
                    <tr>
                        <td><strong>3. Validated intron (singleton)</strong></td>
                        <td>
                            <span class="flag-box flag-true">has_onlyintron_and_valid_model</span><br>
                            <span class="flag-box flag-false">!has_mixed_model</span><br>
                            <span class="flag-box flag-false">!has_onlyexo_model</span><br>
                            <span class="flag-box flag-true">len(segments_list) == 1</span>
                        </td>
                        <td><span class="decision-node result-unspliced">UNSPLICED</span></td>
                        <td>Single segment in validated intron</td>
                    </tr>
                    <tr>
                        <td><strong>4. Validated intron (non-singleton)</strong></td>
                        <td>
                            <span class="flag-box flag-true">has_onlyintron_and_valid_model</span><br>
                            <span class="flag-box flag-false">!has_mixed_model</span><br>
                            <span class="flag-box flag-false">!has_onlyexo_model</span><br>
                            <span class="flag-box flag-true">len(segments_list) > 1</span>
                        </td>
                        <td><span class="decision-node result-unspliced">UNSPLICED</span></td>
                        <td>Multiple segments in validated intron</td>
                    </tr>
                    <tr>
                        <td><strong>5. Non-validated intron (singleton)</strong></td>
                        <td>
                            <span class="flag-box flag-true">has_onlyintron_model</span><br>
                            <span class="flag-box flag-false">!has_onlyintron_and_valid_model</span><br>
                            <span class="flag-box flag-false">!has_mixed_model</span><br>
                            <span class="flag-box flag-false">!has_onlyexo_model</span><br>
                            <span class="flag-box flag-true">len(segments_list) == 1</span>
                        </td>
                        <td><span class="decision-node result-unspliced">UNSPLICED</span></td>
                        <td>Single segment in non-validated intron</td>
                    </tr>
                    <tr>
                        <td><strong>6. Non-validated intron (non-singleton)</strong></td>
                        <td>
                            <span class="flag-box flag-true">has_onlyintron_model</span><br>
                            <span class="flag-box flag-false">!has_onlyintron_and_valid_model</span><br>
                            <span class="flag-box flag-false">!has_mixed_model</span><br>
                            <span class="flag-box flag-false">!has_onlyexo_model</span><br>
                            <span class="flag-box flag-true">len(segments_list) > 1</span>
                        </td>
                        <td><span class="decision-node result-unspliced">UNSPLICED</span></td>
                        <td>Multiple segments in non-validated intron</td>
                    </tr>
                    <tr>
                        <td><strong>7. Invalid mixed model</strong></td>
                        <td>
                            <span class="flag-box flag-true">has_invalid_mixed_model</span><br>
                            <span class="flag-box flag-false">!has_valid_mixed_model</span><br>
                            <span class="flag-box flag-false">!has_onlyintron_model</span><br>
                            <span class="flag-box flag-false">!has_onlyexo_model</span><br>
                            <span class="flag-box flag-false">!has_only_span_exin_model</span>
                        </td>
                        <td><span class="decision-node result-unspliced">UNSPLICED</span></td>
                        <td>Rare in 10X, count anyway</td>
                    </tr>
                    <tr>
                        <td><strong>8. Valid mixed model</strong></td>
                        <td>
                            <span class="flag-box flag-true">has_valid_mixed_model</span><br>
                            <span class="flag-box flag-false">!has_onlyintron_model</span><br>
                            <span class="flag-box flag-false">!has_onlyexo_model</span><br>
                            <span class="flag-box flag-false">!has_only_span_exin_model</span>
                        </td>
                        <td><span class="decision-node result-unspliced">UNSPLICED</span></td>
                        <td>Validated mixed mapping</td>
                    </tr>
                    <tr>
                        <td><strong>9. Intron + Exon ambiguity</strong></td>
                        <td>
                            <span class="flag-box flag-true">has_onlyintron_model</span><br>
                            <span class="flag-box flag-true">has_onlyexo_model</span><br>
                            <span class="flag-box flag-false">!has_mixed_model</span>
                        </td>
                        <td><span class="decision-node result-ambiguous">AMBIGUOUS</span></td>
                        <td>Most common ambiguous case</td>
                    </tr>
                    <tr>
                        <td><strong>10. Complex ambiguity cases</strong></td>
                        <td>
                            Various combinations with mixed models
                        </td>
                        <td><span class="decision-node result-ambiguous">AMBIGUOUS</span></td>
                        <td>Multiple ambiguous scenarios</td>
                    </tr>
                    <tr>
                        <td><strong>11. Edge case</strong></td>
                        <td>
                            None of the above conditions match
                        </td>
                        <td><span class="decision-node" style="background: #e0e0e0; border: 2px solid #666; color: #333;">Return 4</span></td>
                        <td>Unhandled edge case</td>
                    </tr>
                </table>
            </div>

            <!-- Boolean Flag Analysis -->
            <div class="section">
                <h2>Boolean Flag Analysis</h2>

                <h3>Flag Computation Logic</h3>
                <p>The classification relies on analyzing each transcript model and setting boolean flags:</p>

                <div class="code-box">
# For each transcript model, analyze segments:
for segment_match in segments_list:
    if segment_match.maps_to_intron:
        has_introns = 1
        if segment_match.feature.is_validated:
            has_validated_intron = 1
            # Check for exon-intron spanning
            if segment_match.feature.end_overlaps_with_part_of(segment_match.segment):
                downstream_exon = segment_match.feature.get_downstream_exon()
                if downstream_exon.start_overlaps_with_part_of(segment_match.segment):
                    has_exin_intron_span = 1
            # Similar check for upstream exon...
    elif segment_match.maps_to_exon:
        has_exons = 1

# Set model-level flags based on segment analysis:
if has_validated_intron and not has_exons:
    has_onlyintron_and_valid_model = 1
if has_introns and not has_exons:
    has_onlyintron_model = 1
if has_exons and not has_introns:
    has_onlyexo_model = 1
# ... etc for mixed models
                </div>

                <h3>Flag Definitions</h3>
                <div class="info-box">
                    <strong>📋 Key Boolean Flags:</strong>
                    <ul>
                        <li><strong>has_onlyexo_model:</strong> At least one model maps only to exons</li>
                        <li><strong>has_onlyintron_model:</strong> At least one model maps only to introns</li>
                        <li><strong>has_mixed_model:</strong> At least one model maps to both exons and introns</li>
                        <li><strong>has_validated_intron:</strong> At least one model maps to a validated intron</li>
                        <li><strong>has_exin_intron_span:</strong> At least one segment spans exon-intron boundaries</li>
                        <li><strong>has_only_span_exin_model:</strong> All models span exon-intron boundaries</li>
                        <li><strong>multi_gene:</strong> Read maps to multiple genes</li>
                    </ul>
                </div>
            </div>

            <!-- Performance and Usage -->
            <div class="section">
                <h2>Performance and Usage Notes</h2>

                <h3>Why "Permissive"?</h3>
                <div class="success-box">
                    <strong>✅ Permissive Characteristics:</strong>
                    <ul>
                        <li><strong>Counts most reads:</strong> Even non-validated intronic reads are counted as unspliced</li>
                        <li><strong>Minimal filtering:</strong> Only multi-gene and no-gene mappings are excluded</li>
                        <li><strong>10X optimized:</strong> Designed for the specific characteristics of 10X Genomics data</li>
                        <li><strong>Conservative ambiguous:</strong> Only truly ambiguous cases are marked as such</li>
                    </ul>
                </div>

                <h3>Typical Classification Distribution</h3>
                <div class="warning-box">
                    <strong>⚠️ Expected Proportions in 10X Data:</strong>
                    <ul>
                        <li><strong>Spliced (60-80%):</strong> Most reads from mature mRNA</li>
                        <li><strong>Unspliced (15-30%):</strong> Pre-mRNA and nascent transcripts</li>
                        <li><strong>Ambiguous (5-15%):</strong> Unclear mappings</li>
                        <li><strong>Not counted (1-5%):</strong> Multi-gene or no-gene mappings</li>
                    </ul>
                </div>

                <div class="highlight" style="display: block; text-align: center; padding: 1rem; margin: 2rem 0; font-size: 1.1rem;">
                    🎯 <strong>Key Insight:</strong> The Permissive10X logic prioritizes sensitivity over specificity, ensuring maximum information extraction from 10X single-cell RNA-seq data for velocity analysis.
                </div>
            </div>
        </div>
    </div>
</body>
</html>
