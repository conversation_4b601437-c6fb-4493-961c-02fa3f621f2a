<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Velocyto Feature Indexing with GTF Files</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        header {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        h1 {
            margin-top: 0;
            font-size: 2.5rem;
        }
        h2 {
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
            margin-top: 2rem;
        }
        .section {
            background: white;
            margin-bottom: 2rem;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .code-block {
            background-color: #f4f4f4;
            border-left: 4px solid #3498db;
            padding: 1rem;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
            border-radius: 0 4px 4px 0;
        }
        .comparison {
            display: flex;
            justify-content: space-between;
            gap: 2rem;
            margin: 2rem 0;
        }
        .comparison-panel {
            flex: 1;
            background: #e8f4fc;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #3498db;
        }
        .highlight {
            background-color: #fffacd;
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
        }
        .accent-blue {
            color: #2980b9;
            font-weight: bold;
        }
        .accent-green {
            color: #27ae60;
            font-weight: bold;
        }
        .accent-purple {
            color: #8e44ad;
            font-weight: bold;
        }
        .workflow {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 2rem 0;
        }
        .formula {
            text-align: center;
            padding: 1rem;
            background: #e8f8f5;
            border-radius: 8px;
            margin: 1rem 0;
        }
        footer {
            text-align: center;
            margin-top: 2rem;
            padding: 1rem;
            color: #7f8c8d;
            font-size: 0.9rem;
        }
        .key-point {
            background: #fef9e7;
            border-left: 4px solid #f1c40f;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 4px 4px 0;
        }
    </style>
</head>
<body>
    <header>
        <h1>Velocyto Feature Indexing with GTF Files</h1>
        <p>Understanding how velocyto builds per-chromosome feature indexes from GTF annotations</p>
    </header>

    <div class="section">
        <h2>Introduction</h2>
        <p>Velocyto implements functionality similar to building per-chromosome feature indexes with GTF files, though with a more sophisticated approach. This document explains how velocyto handles feature indexing and compares it to the reference code pattern.</p>
        
        <div class="key-point">
            <p><span class="accent-blue">Key Insight:</span> While the exact code structure differs, velocyto creates feature indexes organized by chromosome+strand combinations that serve the same purpose as per-chromosome feature indexes.</p>
        </div>
    </div>

    <div class="section">
        <h2>Reference Code Pattern</h2>
        <p>The user asked about functionality similar to this code pattern:</p>
        <div class="code-block">
# 3. Build per-chromosome feature indexes<br>
feature_indexes = {}<br>
for chrom in chromosomes:<br>
&nbsp;&nbsp;&nbsp;&nbsp;features = collect_features_for_chromosome(chrom, transcript_models)<br>
&nbsp;&nbsp;&nbsp;&nbsp;feature_indexes[chrom] = FeatureIndex(sorted(features))
        </div>
    </div>

    <div class="section">
        <h2>Velocyto's Approach to Feature Indexing</h2>
        <p>Velocyto implements a more structured approach to feature indexing with several key components:</p>
        
        <h3>Core Components</h3>
        <ul>
            <li><span class="accent-blue">FeatureIndex class</span> - Provides efficient search capabilities for genomic features</li>
            <li><span class="accent-blue">TranscriptModel class</span> - Represents transcript models with exons and introns</li>
            <li><span class="accent-blue">Feature class</span> - Represents individual genomic features (exons, introns)</li>
            <li><span class="accent-blue">ExInCounter class</span> - Main class for counting introns and exons</li>
        </ul>
        
        <div class="workflow">
            <h3>Velocyto Feature Indexing Workflow</h3>
            <!-- SVG workflow diagram will be inserted here -->
        </div>
    </div>

    <div class="section">
        <h2>Key Implementation Details</h2>
        
        <h3>1. GTF File Processing</h3>
        <p>The <span class="accent-green">read_transcriptmodels</span> method in <span class="accent-green">ExInCounter</span> processes GTF files:</p>
        <div class="code-block">
# In velocyto/counter.py<br>
def read_transcriptmodels(self, gtf_file: str) -> Dict[str, Dict[str, vcy.TranscriptModel]]:<br>
&nbsp;&nbsp;&nbsp;&nbsp;"""Reads transcript models from a sorted .gtf file"""<br>
&nbsp;&nbsp;&nbsp;&nbsp;# ... parsing logic ...<br>
&nbsp;&nbsp;&nbsp;&nbsp;return self.annotations_by_chrm_strand
        </div>
        
        <h3>2. Feature Index Creation</h3>
        <p>Feature indexes are created from transcript models:</p>
        <div class="code-block">
# In velocyto/counter.py<br>
self.feature_indexes: DefaultDict[str, vcy.FeatureIndex] = defaultdict(vcy.FeatureIndex)<br>
for chromstrand_key, annotions_ordered_dict in self.annotations_by_chrm_strand.items():<br>
&nbsp;&nbsp;&nbsp;&nbsp;self.feature_indexes[chromstrand_key] = vcy.FeatureIndex(sorted(chain.from_iterable(annotions_ordered_dict.values())))
        </div>
        
        <h3>3. FeatureIndex Class</h3>
        <p>The <span class="accent-green">FeatureIndex</span> class provides efficient search methods:</p>
        <div class="code-block">
# In velocyto/indexes.py<br>
class FeatureIndex:<br>
&nbsp;&nbsp;&nbsp;&nbsp;"""Search help class used to find the intervals that a read is spanning"""<br>
&nbsp;&nbsp;&nbsp;&nbsp;def __init__(self, ivls: List[vcy.Feature]=[]) -> None:<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.ivls = ivls<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.ivls.sort()<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.iidx = 0  # index of the current interval<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;self.maxiidx = len(ivls) - 1
        </div>
    </div>

    <div class="section">
        <h2>Comparison of Approaches</h2>
        <div class="comparison">
            <div class="comparison-panel">
                <h3>User's Reference Code</h3>
                <ul>
                    <li>Simple dictionary of feature indexes by chromosome</li>
                    <li>Direct collection of features per chromosome</li>
                    <li>Generic FeatureIndex class</li>
                </ul>
            </div>
            <div class="comparison-panel">
                <h3>Velocyto's Implementation</h3>
                <ul>
                    <li>Organization by chromosome+strand combinations</li>
                    <li>Structured transcript models with exons/introns</li>
                    <li>Specialized FeatureIndex with search methods</li>
                    <li>Integration with read processing pipeline</li>
                </ul>
            </div>
        </div>
        
        <div class="key-point">
            <p><span class="accent-purple">Key Difference:</span> Velocyto's approach is more sophisticated, organizing data by chromosome+strand rather than just chromosome, which is important for strand-specific RNA-seq analysis.</p>
        </div>
    </div>

    <div class="section">
        <h2>Mathematical Foundation</h2>
        <div class="formula">
            <p>Feature overlap calculation in velocyto uses interval arithmetic:</p>
            <p>$$\text{overlap}(r, f) = \max(0, \min(r_{end}, f_{end}) - \max(r_{start}, f_{start}) + 1)$$</p>
            <p>Where \(r\) is a read segment and \(f\) is a genomic feature.</p>
        </div>
    </div>

    <div class="section">
        <h2>Conclusion</h2>
        <p>Velocyto does implement functionality equivalent to per-chromosome feature indexing with GTF files, but with these enhancements:</p>
        <ol>
            <li><span class="accent-blue">Strand-aware organization</span> - Features are organized by chromosome+strand combinations</li>
            <li><span class="accent-blue">Structured data model</span> - Transcript models contain both exons and introns as features</li>
            <li><span class="accent-blue">Efficient search algorithms</span> - FeatureIndex provides optimized overlap queries</li>
            <li><span class="accent-blue">Integration with counting pipeline</span> - Indexes are used directly in read counting operations</li>
        </ol>
        
        <p>The core workflow in velocyto matches the reference pattern:</p>
        <div class="code-block">
# 1. Parse GTF file<br>
annotations = exincounter.read_transcriptmodels(gtffile)<br><br>
# 2. Build feature indexes (equivalent to the reference code)<br>
feature_indexes = {}<br>
for chromstrand_key, annotions_ordered_dict in annotations.items():<br>
&nbsp;&nbsp;&nbsp;&nbsp;features = list(chain.from_iterable(annotions_ordered_dict.values()))<br>
&nbsp;&nbsp;&nbsp;&nbsp;feature_indexes[chromstrand_key] = FeatureIndex(sorted(features))
        </div>
    </div>

    <footer>
        <p>Velocyto Feature Indexing Documentation | Created from analysis of velocyto.py codebase</p>
    </footer>

    <script>
        // SVG workflow diagram
        document.addEventListener('DOMContentLoaded', function() {
            const workflowContainer = document.querySelector('.workflow');
            if (workflowContainer) {
                workflowContainer.innerHTML += `
                    <svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
                        <!-- Background -->
                        <rect width="100%" height="100%" fill="#f8f9fa"/>
                        
                        <!-- Step 1: GTF File -->
                        <rect x="50" y="50" width="120" height="60" rx="10" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
                        <text x="110" y="80" font-family="Arial" font-size="14" fill="white" text-anchor="middle">GTF File</text>
                        
                        <!-- Arrow 1 -->
                        <line x1="170" y1="80" x2="250" y2="80" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                        
                        <!-- Step 2: Transcript Models -->
                        <rect x="250" y="50" width="150" height="60" rx="10" fill="#9b59b6" stroke="#8e44ad" stroke-width="2"/>
                        <text x="325" y="75" font-family="Arial" font-size="14" fill="white" text-anchor="middle">Transcript</text>
                        <text x="325" y="95" font-family="Arial" font-size="14" fill="white" text-anchor="middle">Models</text>
                        
                        <!-- Arrow 2 -->
                        <line x1="400" y1="80" x2="480" y2="80" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                        
                        <!-- Step 3: Feature Indexes -->
                        <rect x="480" y="50" width="150" height="60" rx="10" fill="#27ae60" stroke="#229954" stroke-width="2"/>
                        <text x="555" y="75" font-family="Arial" font-size="14" fill="white" text-anchor="middle">Feature</text>
                        <text x="555" y="95" font-family="Arial" font-size="14" fill="white" text-anchor="middle">Indexes</text>
                        
                        <!-- Arrow 3 -->
                        <line x1="630" y1="80" x2="710" y2="80" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                        
                        <!-- Step 4: Read Counting -->
                        <rect x="710" y="50" width="120" height="60" rx="10" fill="#e74c3c" stroke="#c0392b" stroke-width="2"/>
                        <text x="770" y="80" font-family="Arial" font-size="14" fill="white" text-anchor="middle">Counting</text>
                        
                        <!-- Step 5: BAM File -->
                        <rect x="150" y="150" width="120" height="60" rx="10" fill="#f39c12" stroke="#d35400" stroke-width="2"/>
                        <text x="210" y="180" font-family="Arial" font-size="14" fill="white" text-anchor="middle">BAM File</text>
                        
                        <!-- Arrow 4 -->
                        <line x1="210" y1="210" x2="210" y2="270" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <line x1="210" y1="270" x2="555" y2="270" stroke="#2c3e50" stroke-width="2"/>
                        <line x1="555" y1="270" x2="555" y2="110" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                        
                        <!-- Arrow 5 -->
                        <line x1="555" y1="270" x2="770" y2="270" stroke="#2c3e50" stroke-width="2"/>
                        <line x1="770" y1="270" x2="770" y2="110" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                        
                        <!-- Arrow definitions -->
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                            </marker>
                        </defs>
                        
                        <!-- Labels -->
                        <text x="210" y="40" font-family="Arial" font-size="12" fill="#2c3e50" text-anchor="middle">Parse GTF</text>
                        <text x="325" y="40" font-family="Arial" font-size="12" fill="#2c3e50" text-anchor="middle">Create Models</text>
                        <text x="555" y="40" font-family="Arial" font-size="12" fill="#2c3e50" text-anchor="middle">Build Indexes</text>
                        <text x="770" y="40" font-family="Arial" font-size="12" fill="#2c3e50" text-anchor="middle">Count Reads</text>
                        <text x="120" y="140" font-family="Arial" font-size="12" fill="#2c3e50" text-anchor="middle">Read</text>
                        <text x="555" y="290" font-family="Arial" font-size="12" fill="#2c3e50" text-anchor="middle">Query Feature Indexes</text>
                    </svg>
                `;
            }
        });
    </script>
</body>
</html>