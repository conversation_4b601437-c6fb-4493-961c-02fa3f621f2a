<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Velocyto Analysis.py: Complete Guide</title>
    
    <!-- MathJax 3 for LaTeX rendering -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 1rem 0 0 0;
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
            border-radius: 0 8px 8px 0;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.8rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        
        .section h3 {
            color: #34495e;
            margin-top: 2rem;
            font-size: 1.4rem;
        }
        
        .math-box {
            background: #fff;
            border: 2px solid #3498db;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            box-shadow: 0 2px 10px rgba(52, 152, 219, 0.1);
        }
        
        .code-box {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
        }
        
        .workflow-step {
            display: flex;
            align-items: flex-start;
            margin: 1.5rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .svg-container {
            text-align: center;
            margin: 2rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .function-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            font-size: 0.9rem;
        }
        
        .function-table th,
        .function-table td {
            border: 1px solid #ddd;
            padding: 0.75rem;
            text-align: left;
            vertical-align: top;
        }
        
        .function-table th {
            background: #3498db;
            color: white;
            font-weight: bold;
        }
        
        .function-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .function-table .category {
            background: #e3f2fd;
            font-weight: bold;
            color: #1976d2;
        }
        
        .toc {
            background: #34495e;
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .toc h3 {
            margin-top: 0;
            color: #ecf0f1;
        }
        
        .toc ul {
            list-style: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 0.5rem 0;
            padding-left: 1rem;
        }
        
        .toc a {
            color: #3498db;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .toc a:hover {
            color: #5dade2;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 2px solid #17a2b8;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Velocyto Analysis.py Deep Dive</h1>
            <p>Complete Guide to the VelocytoLoom Class and RNA Velocity Analysis Pipeline</p>
        </div>
        
        <div class="content">
            <!-- Table of Contents -->
            <div class="toc">
                <h3>📚 Table of Contents</h3>
                <ul>
                    <li><a href="#overview">1. Overview and Architecture</a></li>
                    <li><a href="#data-structure">2. Data Structure and Initialization</a></li>
                    <li><a href="#preprocessing">3. Preprocessing Pipeline</a></li>
                    <li><a href="#velocity-estimation">4. Velocity Estimation</a></li>
                    <li><a href="#embedding-projection">5. Embedding and Projection</a></li>
                    <li><a href="#visualization">6. Visualization Functions</a></li>
                    <li><a href="#advanced-analysis">7. Advanced Analysis</a></li>
                    <li><a href="#function-relationships">8. Function Relationships</a></li>
                </ul>
            </div>

            <!-- Section 1: Overview -->
            <div class="section" id="overview">
                <h2>1. Overview and Architecture</h2>
                
                <p>The <span class="highlight">analysis.py</span> file is the heart of velocyto.py, containing the <code>VelocytoLoom</code> class that orchestrates the entire RNA velocity analysis pipeline. This class provides a comprehensive interface for loading, processing, analyzing, and visualizing single-cell RNA velocity data.</p>
                
                <div class="svg-container">
                    <svg width="1200" height="600" viewBox="0 0 1200 600">
                        <!-- Background -->
                        <rect width="1200" height="600" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
                        
                        <!-- Title -->
                        <text x="600" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">VelocytoLoom Class Architecture</text>
                        
                        <!-- Data Layer -->
                        <rect x="50" y="60" width="1100" height="80" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="10"/>
                        <text x="600" y="85" text-anchor="middle" font-size="16" font-weight="bold" fill="#1976d2">Data Layer</text>
                        <text x="150" y="110" font-size="12" fill="#333">S (Spliced)</text>
                        <text x="250" y="110" font-size="12" fill="#333">U (Unspliced)</text>
                        <text x="350" y="110" font-size="12" fill="#333">A (Ambiguous)</text>
                        <text x="450" y="110" font-size="12" fill="#333">ca (Cell Attrs)</text>
                        <text x="550" y="110" font-size="12" fill="#333">ra (Gene Attrs)</text>
                        <text x="650" y="110" font-size="12" fill="#333">Normalized Data</text>
                        <text x="750" y="110" font-size="12" fill="#333">Smoothed Data</text>
                        <text x="850" y="110" font-size="12" fill="#333">PCA Components</text>
                        <text x="950" y="110" font-size="12" fill="#333">Embeddings</text>
                        <text x="1050" y="110" font-size="12" fill="#333">Velocity</text>
                        
                        <!-- Preprocessing Layer -->
                        <rect x="50" y="160" width="350" height="120" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="10"/>
                        <text x="225" y="185" text-anchor="middle" font-size="14" font-weight="bold" fill="#f57c00">Preprocessing</text>
                        <rect x="70" y="200" width="80" height="25" fill="#ffcc02" stroke="#f57c00" stroke-width="1" rx="3"/>
                        <text x="110" y="217" text-anchor="middle" font-size="10" fill="#333">normalize()</text>
                        <rect x="160" y="200" width="80" height="25" fill="#ffcc02" stroke="#f57c00" stroke-width="1" rx="3"/>
                        <text x="200" y="217" text-anchor="middle" font-size="10" fill="#333">filter_genes()</text>
                        <rect x="250" y="200" width="80" height="25" fill="#ffcc02" stroke="#f57c00" stroke-width="1" rx="3"/>
                        <text x="290" y="217" text-anchor="middle" font-size="10" fill="#333">score_cv_vs_mean()</text>
                        <rect x="70" y="235" width="80" height="25" fill="#ffcc02" stroke="#f57c00" stroke-width="1" rx="3"/>
                        <text x="110" y="252" text-anchor="middle" font-size="10" fill="#333">perform_PCA()</text>
                        <rect x="160" y="235" width="80" height="25" fill="#ffcc02" stroke="#f57c00" stroke-width="1" rx="3"/>
                        <text x="200" y="252" text-anchor="middle" font-size="10" fill="#333">knn_imputation()</text>
                        
                        <!-- Velocity Estimation Layer -->
                        <rect x="420" y="160" width="350" height="120" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10"/>
                        <text x="595" y="185" text-anchor="middle" font-size="14" font-weight="bold" fill="#4caf50">Velocity Estimation</text>
                        <rect x="440" y="200" width="80" height="25" fill="#a5d6a7" stroke="#4caf50" stroke-width="1" rx="3"/>
                        <text x="480" y="217" text-anchor="middle" font-size="10" fill="#333">fit_gammas()</text>
                        <rect x="530" y="200" width="80" height="25" fill="#a5d6a7" stroke="#4caf50" stroke-width="1" rx="3"/>
                        <text x="570" y="217" text-anchor="middle" font-size="10" fill="#333">predict_U()</text>
                        <rect x="620" y="200" width="80" height="25" fill="#a5d6a7" stroke="#4caf50" stroke-width="1" rx="3"/>
                        <text x="660" y="217" text-anchor="middle" font-size="10" fill="#333">calculate_velocity()</text>
                        <rect x="440" y="235" width="80" height="25" fill="#a5d6a7" stroke="#4caf50" stroke-width="1" rx="3"/>
                        <text x="480" y="252" text-anchor="middle" font-size="10" fill="#333">calculate_shift()</text>
                        <rect x="530" y="235" width="80" height="25" fill="#a5d6a7" stroke="#4caf50" stroke-width="1" rx="3"/>
                        <text x="570" y="252" text-anchor="middle" font-size="10" fill="#333">extrapolate_cell_at_t()</text>
                        
                        <!-- Analysis Layer -->
                        <rect x="790" y="160" width="360" height="120" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="10"/>
                        <text x="970" y="185" text-anchor="middle" font-size="14" font-weight="bold" fill="#9c27b0">Advanced Analysis</text>
                        <rect x="810" y="200" width="100" height="25" fill="#ce93d8" stroke="#9c27b0" stroke-width="1" rx="3"/>
                        <text x="860" y="217" text-anchor="middle" font-size="10" fill="#333">estimate_transition_prob()</text>
                        <rect x="920" y="200" width="80" height="25" fill="#ce93d8" stroke="#9c27b0" stroke-width="1" rx="3"/>
                        <text x="960" y="217" text-anchor="middle" font-size="10" fill="#333">run_markov()</text>
                        <rect x="1010" y="200" width="80" height="25" fill="#ce93d8" stroke="#9c27b0" stroke-width="1" rx="3"/>
                        <text x="1050" y="217" text-anchor="middle" font-size="10" fill="#333">calculate_embedding_shift()</text>
                        <rect x="810" y="235" width="80" height="25" fill="#ce93d8" stroke="#9c27b0" stroke-width="1" rx="3"/>
                        <text x="850" y="252" text-anchor="middle" font-size="10" fill="#333">gene_knn_imputation()</text>
                        <rect x="900" y="235" width="80" height="25" fill="#ce93d8" stroke="#9c27b0" stroke-width="1" rx="3"/>
                        <text x="940" y="252" text-anchor="middle" font-size="10" fill="#333">cluster_analysis()</text>
                        
                        <!-- Visualization Layer -->
                        <rect x="50" y="300" width="1100" height="120" fill="#ffebee" stroke="#e91e63" stroke-width="2" rx="10"/>
                        <text x="600" y="325" text-anchor="middle" font-size="14" font-weight="bold" fill="#e91e63">Visualization Functions</text>
                        <rect x="70" y="340" width="100" height="25" fill="#f8bbd9" stroke="#e91e63" stroke-width="1" rx="3"/>
                        <text x="120" y="357" text-anchor="middle" font-size="10" fill="#333">plot_phase_portraits()</text>
                        <rect x="180" y="340" width="100" height="25" fill="#f8bbd9" stroke="#e91e63" stroke-width="1" rx="3"/>
                        <text x="230" y="357" text-anchor="middle" font-size="10" fill="#333">plot_velocity_as_flow()</text>
                        <rect x="290" y="340" width="100" height="25" fill="#f8bbd9" stroke="#e91e63" stroke-width="1" rx="3"/>
                        <text x="340" y="357" text-anchor="middle" font-size="10" fill="#333">plot_grid_arrows()</text>
                        <rect x="400" y="340" width="100" height="25" fill="#f8bbd9" stroke="#e91e63" stroke-width="1" rx="3"/>
                        <text x="450" y="357" text-anchor="middle" font-size="10" fill="#333">plot_arrows_embedding()</text>
                        <rect x="510" y="340" width="100" height="25" fill="#f8bbd9" stroke="#e91e63" stroke-width="1" rx="3"/>
                        <text x="560" y="357" text-anchor="middle" font-size="10" fill="#333">plot_velocity_as_color()</text>
                        <rect x="620" y="340" width="100" height="25" fill="#f8bbd9" stroke="#e91e63" stroke-width="1" rx="3"/>
                        <text x="670" y="357" text-anchor="middle" font-size="10" fill="#333">plot_expression_as_color()</text>
                        <rect x="730" y="340" width="100" height="25" fill="#f8bbd9" stroke="#e91e63" stroke-width="1" rx="3"/>
                        <text x="780" y="357" text-anchor="middle" font-size="10" fill="#333">plot_cell_transitions()</text>
                        <rect x="840" y="340" width="100" height="25" fill="#f8bbd9" stroke="#e91e63" stroke-width="1" rx="3"/>
                        <text x="890" y="357" text-anchor="middle" font-size="10" fill="#333">plot_fractions()</text>
                        <rect x="950" y="340" width="100" height="25" fill="#f8bbd9" stroke="#e91e63" stroke-width="1" rx="3"/>
                        <text x="1000" y="357" text-anchor="middle" font-size="10" fill="#333">plot_pca()</text>
                        
                        <!-- Data flow arrows -->
                        <path d="M 225 140 L 225 160" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <path d="M 595 140 L 595 160" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <path d="M 970 140 L 970 160" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <path d="M 400 220 L 420 220" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <path d="M 770 220 L 790 220" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <path d="M 600 280 L 600 300" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
                        
                        <!-- External Dependencies -->
                        <rect x="50" y="450" width="1100" height="100" fill="#f5f5f5" stroke="#999" stroke-width="1" rx="10"/>
                        <text x="600" y="475" text-anchor="middle" font-size="14" font-weight="bold" fill="#666">External Dependencies</text>
                        <text x="150" y="500" font-size="11" fill="#666">• numpy (numerical operations)</text>
                        <text x="150" y="515" font-size="11" fill="#666">• scipy (sparse matrices, optimization)</text>
                        <text x="150" y="530" font-size="11" fill="#666">• sklearn (PCA, t-SNE, SVR)</text>
                        <text x="400" y="500" font-size="11" fill="#666">• matplotlib (visualization)</text>
                        <text x="400" y="515" font-size="11" fill="#666">• loompy (data I/O)</text>
                        <text x="400" y="530" font-size="11" fill="#666">• numba (JIT compilation)</text>
                        <text x="650" y="500" font-size="11" fill="#666">• velocyto.neighbors (kNN operations)</text>
                        <text x="650" y="515" font-size="11" fill="#666">• velocyto.estimation (gamma fitting)</text>
                        <text x="650" y="530" font-size="11" fill="#666">• velocyto.diffusion (Markov chains)</text>
                        
                        <!-- Arrow marker definition -->
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>
                
                <h3>1.1 Key Design Principles</h3>
                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>State Management:</strong> All intermediate results are stored as object attributes, allowing for flexible analysis workflows and easy inspection of intermediate steps.
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>Method Chaining:</strong> Functions modify the object in-place and return None, enabling sequential method calls without explicit variable assignments.
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div>
                        <strong>Modular Design:</strong> Each analysis step is encapsulated in separate methods, allowing users to customize their analysis pipeline.
                    </div>
                </div>
            </div>

            <!-- Section 2: Data Structure -->
            <div class="section" id="data-structure">
                <h2>2. Data Structure and Initialization</h2>
                
                <h3>2.1 Core Data Attributes</h3>
                <p>The <code>VelocytoLoom</code> class manages multiple data matrices and their transformations:</p>
                
                <table class="function-table">
                    <tr>
                        <th>Attribute</th>
                        <th>Type</th>
                        <th>Description</th>
                        <th>Shape</th>
                    </tr>
                    <tr class="category">
                        <td colspan="4">Raw Count Data</td>
                    </tr>
                    <tr>
                        <td><code>S</code></td>
                        <td>np.ndarray</td>
                        <td>Spliced molecule counts</td>
                        <td>(genes, cells)</td>
                    </tr>
                    <tr>
                        <td><code>U</code></td>
                        <td>np.ndarray</td>
                        <td>Unspliced molecule counts</td>
                        <td>(genes, cells)</td>
                    </tr>
                    <tr>
                        <td><code>A</code></td>
                        <td>np.ndarray</td>
                        <td>Ambiguous molecule counts</td>
                        <td>(genes, cells)</td>
                    </tr>
                    <tr class="category">
                        <td colspan="4">Normalized Data</td>
                    </tr>
                    <tr>
                        <td><code>S_norm</code></td>
                        <td>np.ndarray</td>
                        <td>Log-normalized spliced counts</td>
                        <td>(genes, cells)</td>
                    </tr>
                    <tr>
                        <td><code>U_norm</code></td>
                        <td>np.ndarray</td>
                        <td>Log-normalized unspliced counts</td>
                        <td>(genes, cells)</td>
                    </tr>
                    <tr class="category">
                        <td colspan="4">Smoothed Data</td>
                    </tr>
                    <tr>
                        <td><code>Sx</code></td>
                        <td>np.ndarray</td>
                        <td>kNN-smoothed spliced counts</td>
                        <td>(genes, cells)</td>
                    </tr>
                    <tr>
                        <td><code>Ux</code></td>
                        <td>np.ndarray</td>
                        <td>kNN-smoothed unspliced counts</td>
                        <td>(genes, cells)</td>
                    </tr>
                    <tr class="category">
                        <td colspan="4">Velocity-Related</td>
                    </tr>
                    <tr>
                        <td><code>gammas</code></td>
                        <td>np.ndarray</td>
                        <td>Fitted gamma parameters</td>
                        <td>(genes,)</td>
                    </tr>
                    <tr>
                        <td><code>velocity</code></td>
                        <td>np.ndarray</td>
                        <td>RNA velocity estimates</td>
                        <td>(genes, cells)</td>
                    </tr>
                    <tr>
                        <td><code>Upred</code></td>
                        <td>np.ndarray</td>
                        <td>Predicted unspliced counts</td>
                        <td>(genes, cells)</td>
                    </tr>
                </table>
                
                <h3>2.2 Initialization Process</h3>
                <div class="code-box">
class VelocytoLoom:
    def __init__(self, loom_filepath: str) -> None:
        # Connect to loom file
        ds = loompy.connect(self.loom_filepath)
        
        # Load count matrices
        self.S = ds.layer["spliced"][:, :]      # Spliced counts
        self.U = ds.layer["unspliced"][:, :]    # Unspliced counts  
        self.A = ds.layer["ambiguous"][:, :]    # Ambiguous counts
        
        # Load metadata
        self.ca = dict(ds.col_attrs.items())    # Cell attributes
        self.ra = dict(ds.row_attrs.items())    # Gene attributes
        
        # Calculate initial statistics
        self.initial_cell_size = self.S.sum(0)     # Total spliced per cell
        self.initial_Ucell_size = self.U.sum(0)    # Total unspliced per cell
                </div>
                
                <div class="math-box">
                    <p><strong>Mathematical Foundation:</strong></p>
                    <p>The data structure represents the fundamental RNA velocity model:</p>
                    $$\frac{du}{dt} = \alpha(t) - \beta u$$
                    $$\frac{ds}{dt} = \beta u - \gamma s$$
                    
                    <p>Where each matrix element represents:</p>
                    <ul>
                        <li>$S_{ij}$ = spliced mRNA count for gene $i$ in cell $j$</li>
                        <li>$U_{ij}$ = unspliced mRNA count for gene $i$ in cell $j$</li>
                        <li>$\alpha_i(t)$ = transcription rate for gene $i$ at time $t$</li>
                        <li>$\beta_i$ = splicing rate for gene $i$</li>
                        <li>$\gamma_i$ = degradation rate for gene $i$</li>
                    </ul>
                </div>
            </div>

            <!-- Section 3: Preprocessing Pipeline -->
            <div class="section" id="preprocessing">
                <h2>3. Preprocessing Pipeline</h2>

                <h3>3.1 Normalization Functions</h3>
                <p>The preprocessing pipeline transforms raw count data into analysis-ready matrices:</p>

                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>normalize():</strong> Main normalization interface
                        <div class="math-box" style="margin-top: 0.5rem;">
                            $$S_{norm} = \log_2\left(\frac{S \cdot \text{size_factor}}{\text{cell_size}} + 1\right)$$
                            <p>Where size_factor normalizes to median library size</p>
                        </div>
                        <div class="code-box" style="margin-top: 0.5rem;">
# Normalize spliced and unspliced data
vlm.normalize("both", size=True, log=True)

# Creates: S_norm, U_norm, S_sz, U_sz
# S_sz: size-normalized counts
# S_norm: log-transformed normalized counts
                        </div>
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>normalize_by_total():</strong> Alternative normalization using total molecule counts
                        <div class="code-box" style="margin-top: 0.5rem;">
vlm.normalize_by_total(min_perc_U=0.5, same_size_UnS=False)

# Normalizes based on initial_cell_size and initial_Ucell_size
# Useful for maintaining S/U ratio relationships
                        </div>
                    </div>
                </div>

                <h3>3.2 Gene Filtering and Selection</h3>

                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>score_detection_levels():</strong> Basic gene filtering
                        <div class="code-box" style="margin-top: 0.5rem;">
vlm.score_detection_levels(
    min_expr_counts=50,      # Minimum total counts
    min_cells_express=20     # Minimum expressing cells
)
# Creates: detection_level_selected boolean array
                        </div>
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>score_cv_vs_mean():</strong> Highly variable gene selection
                        <div class="math-box" style="margin-top: 0.5rem;">
                            <p>Uses Support Vector Regression to model CV vs mean relationship:</p>
                            $$CV = f(\text{mean}) + \epsilon$$
                            <p>Genes with high residuals are considered highly variable</p>
                        </div>
                        <div class="code-box" style="margin-top: 0.5rem;">
vlm.score_cv_vs_mean(
    N=3000,                  # Number of genes to select
    max_expr_avg=20,         # Maximum mean expression
    svr_gamma=None,          # SVR kernel parameter
    plot=True                # Show diagnostic plot
)
# Creates: cv_vs_mean_selected boolean array
                        </div>
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div>
                        <strong>filter_genes():</strong> Apply filtering criteria
                        <div class="code-box" style="margin-top: 0.5rem;">
vlm.filter_genes(
    by_detection_levels=True,
    by_cv_vs_mean=True,
    by_cluster_expression=False
)
# Filters all data matrices (S, U, A) and metadata (ra)
                        </div>
                    </div>
                </div>

                <h3>3.3 Dimensionality Reduction</h3>

                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>perform_PCA():</strong> Principal Component Analysis
                        <div class="math-box" style="margin-top: 0.5rem;">
                            $$X_{PCA} = X \cdot V$$
                            <p>Where $V$ contains the principal component vectors</p>
                        </div>
                        <div class="code-box" style="margin-top: 0.5rem;">
vlm.perform_PCA(
    which="S_norm",          # Data matrix to use
    n_components=None,       # Keep all components
    div_by_std=False         # Don't standardize
)
# Creates: pcs (cell × PC matrix), pca (sklearn PCA object)
                        </div>
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>knn_imputation():</strong> k-Nearest Neighbor smoothing
                        <div class="math-box" style="margin-top: 0.5rem;">
                            $$S_x = W \cdot S$$
                            $$U_x = W \cdot U$$
                            <p>Where $W$ is the kNN weight matrix</p>
                        </div>
                        <div class="code-box" style="margin-top: 0.5rem;">
vlm.knn_imputation(
    k=525,                   # Number of neighbors
    pca_space=True,          # Use PCA space for kNN
    n_pca_dims=25,           # PCA dimensions to use
    balanced=True,           # Use balanced kNN
    b_sight=3000,            # Balanced kNN parameter
    metric="euclidean"       # Distance metric
)
# Creates: Sx, Ux (smoothed matrices), knn_smoothing_w (weights)
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section 4: Velocity Estimation -->
            <div class="section" id="velocity-estimation">
                <h2>4. Velocity Estimation</h2>

                <h3>4.1 Gamma Fitting Process</h3>
                <p>The core of velocity estimation involves fitting the splicing dynamics model:</p>

                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>fit_gammas():</strong> Estimate steady-state parameters
                        <div class="math-box" style="margin-top: 0.5rem;">
                            <p>For each gene $i$, fit the linear model:</p>
                            $$u_i = \gamma_i s_i + q_i + \epsilon_i$$
                            <p>Using weighted least squares:</p>
                            $$\gamma_i = \arg\min_\gamma \sum_j w_{ij}(u_{ij} - \gamma s_{ij} - q_i)^2$$
                        </div>
                        <div class="code-box" style="margin-top: 0.5rem;">
vlm.fit_gammas(
    use_imputed_data=True,   # Use smoothed data (Sx, Ux)
    fit_offset=True,         # Include intercept term
    weighted=True,           # Use weighted regression
    weights="maxmin_diag",   # Weighting strategy
    limit_gamma=False        # Don't constrain gamma range
)
# Creates: gammas, q (offsets), R2 (fit quality)
                        </div>
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>predict_U():</strong> Calculate expected unspliced levels
                        <div class="math-box" style="margin-top: 0.5rem;">
                            $$U_{pred} = \gamma \cdot S + q$$
                        </div>
                        <div class="code-box" style="margin-top: 0.5rem;">
vlm.predict_U(
    which_gamma="gammas",    # Gamma parameter to use
    which_S="Sx_sz",         # Spliced data to use
    which_offset="q"         # Offset parameter
)
# Creates: Upred (predicted unspliced counts)
                        </div>
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div>
                        <strong>calculate_velocity():</strong> Compute RNA velocity
                        <div class="math-box" style="margin-top: 0.5rem;">
                            $$v_{ij} = u_{ij} - u_{pred,ij}$$
                            <p>Positive velocity → increasing expression</p>
                            <p>Negative velocity → decreasing expression</p>
                        </div>
                        <div class="code-box" style="margin-top: 0.5rem;">
vlm.calculate_velocity(
    kind="residual",         # Velocity calculation method
    eps=None                 # Threshold for small velocities
)
# Creates: velocity (genes × cells matrix)
                        </div>
                    </div>
                </div>

                <h3>4.2 Temporal Extrapolation</h3>

                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>calculate_shift():</strong> Predict expression changes
                        <div class="math-box" style="margin-top: 0.5rem;">
                            <p>Model I (constant velocity):</p>
                            $$\Delta S = v \cdot \Delta t$$
                            <p>Model II (constant unspliced):</p>
                            $$\Delta S = \frac{v}{\gamma} \cdot \Delta t$$
                        </div>
                        <div class="code-box" style="margin-top: 0.5rem;">
vlm.calculate_shift(
    assumption="constant_velocity",  # or "constant_unspliced"
    delta_t=1                        # Time step
)
# Creates: deltaS (predicted expression changes)
                        </div>
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>extrapolate_cell_at_t():</strong> Project future cell states
                        <div class="math-box" style="margin-top: 0.5rem;">
                            $$S(t + \Delta t) = S(t) + \Delta S$$
                        </div>
                        <div class="code-box" style="margin-top: 0.5rem;">
vlm.extrapolate_cell_at_t(
    delta_t=1,               # Time step for extrapolation
    clip=True                # Clip negative values to zero
)
# Creates: Sx_sz_t (extrapolated expression matrix)
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section 5: Embedding and Projection -->
            <div class="section" id="embedding-projection">
                <h2>5. Embedding and Projection</h2>

                <h3>5.1 Low-Dimensional Embeddings</h3>
                <p>Velocity vectors are projected onto low-dimensional embeddings for visualization:</p>

                <div class="code-box">
# Example: t-SNE embedding
from sklearn.manifold import TSNE
bh_tsne = TSNE(n_components=2, perplexity=30)
vlm.ts = bh_tsne.fit_transform(vlm.pcs[:, :25])

# Example: UMAP embedding
import umap
reducer = umap.UMAP(n_components=2, n_neighbors=15)
vlm.embedding = reducer.fit_transform(vlm.pcs[:, :25])
                </div>

                <h3>5.2 Transition Probability Estimation</h3>

                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>estimate_transition_prob():</strong> Calculate cell-to-cell transition probabilities
                        <div class="math-box" style="margin-top: 0.5rem;">
                            <p>For each cell $i$, calculate correlation with neighbors:</p>
                            $$\rho_{ij} = \text{corr}(\Delta S_i, S_j - S_i)$$
                            <p>Convert to transition probability:</p>
                            $$T_{ij} = \frac{\max(0, \rho_{ij})}{\sum_k \max(0, \rho_{ik})}$$
                        </div>
                        <div class="code-box" style="margin-top: 0.5rem;">
vlm.estimate_transition_prob(
    hidim="Sx_sz",           # High-dim data for correlation
    embed="ts",              # Embedding for neighborhoods
    transform="sqrt",        # Transform for correlation
    knn_random=True,         # Use randomized control
    sampled_fraction=0.3     # Fraction for correlation calc
)
# Creates: tr (transition probability matrix)
                        </div>
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>calculate_embedding_shift():</strong> Project velocity to embedding
                        <div class="math-box" style="margin-top: 0.5rem;">
                            $$\Delta E = \sum_j T_{ij} (E_j - E_i)$$
                            <p>Where $E$ is the embedding coordinates</p>
                        </div>
                        <div class="code-box" style="margin-top: 0.5rem;">
vlm.calculate_embedding_shift(
    sigma_corr=0.05,         # Correlation threshold
    expression_scaling=True   # Scale by expression levels
)
# Creates: delta_embedding (velocity in embedding space)
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section 6: Visualization Functions -->
            <div class="section" id="visualization">
                <h2>6. Visualization Functions</h2>

                <h3>6.1 Core Visualization Methods</h3>

                <table class="function-table">
                    <tr>
                        <th>Function</th>
                        <th>Purpose</th>
                        <th>Key Parameters</th>
                        <th>Output</th>
                    </tr>
                    <tr>
                        <td><code>plot_phase_portraits()</code></td>
                        <td>Show U vs S scatter plots with fitted lines</td>
                        <td>genes: List[str]</td>
                        <td>Phase portrait plots</td>
                    </tr>
                    <tr>
                        <td><code>plot_velocity_as_flow()</code></td>
                        <td>Streamline plot of velocity field</td>
                        <td>which_tsne, min_mass, autoscale</td>
                        <td>Flow field visualization</td>
                    </tr>
                    <tr>
                        <td><code>plot_grid_arrows()</code></td>
                        <td>Grid-based velocity arrows</td>
                        <td>quiver_scale, min_mass</td>
                        <td>Arrow grid plot</td>
                    </tr>
                    <tr>
                        <td><code>plot_arrows_embedding()</code></td>
                        <td>Cell-wise velocity arrows on embedding</td>
                        <td>choice, quiver_scale, color_arrow</td>
                        <td>Individual cell arrows</td>
                    </tr>
                    <tr>
                        <td><code>plot_velocity_as_color()</code></td>
                        <td>Color cells by gene velocity</td>
                        <td>gene_name, cmap, which_tsne</td>
                        <td>Color-coded embedding</td>
                    </tr>
                    <tr>
                        <td><code>plot_expression_as_color()</code></td>
                        <td>Color cells by gene expression</td>
                        <td>gene_name, imputed, cmap</td>
                        <td>Expression heatmap</td>
                    </tr>
                </table>

                <h3>6.2 Visualization Examples</h3>

                <div class="code-box">
# 1. Phase portraits for specific genes
vlm.plot_phase_portraits(["Pdgfra", "Igfbpl1", "Klf4"])

# 2. Velocity field as streamlines
vlm.plot_velocity_as_flow(
    which_tsne="ts",
    min_mass=24,
    autoscale=True,
    scatter_kwargs_dict={"alpha": 0.35, "s": 38}
)

# 3. Grid-based velocity arrows
vlm.plot_grid_arrows(
    quiver_scale="auto",
    min_mass=1,
    plot_random=True
)

# 4. Individual cell velocity arrows
vlm.plot_arrows_embedding(
    choice=1000,             # Number of cells to show
    quiver_scale="auto",
    color_arrow="cluster"
)

# 5. Gene velocity as color
vlm.plot_velocity_as_color(
    gene_name="Pdgfra",
    cmap=plt.cm.RdBu_r,
    which_tsne="ts"
)
                </div>

                <div class="svg-container">
                    <svg width="1000" height="400" viewBox="0 0 1000 400">
                        <!-- Background -->
                        <rect width="1000" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="500" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Visualization Function Outputs</text>

                        <!-- Phase Portrait -->
                        <rect x="50" y="60" width="180" height="120" fill="white" stroke="#e91e63" stroke-width="2"/>
                        <text x="140" y="80" text-anchor="middle" font-size="12" font-weight="bold" fill="#e91e63">Phase Portrait</text>
                        <line x1="70" y1="160" x2="210" y2="160" stroke="#333" stroke-width="1"/>
                        <line x1="70" y1="160" x2="70" y2="90" stroke="#333" stroke-width="1"/>
                        <circle cx="90" cy="140" r="2" fill="#3498db"/>
                        <circle cx="110" cy="130" r="2" fill="#3498db"/>
                        <circle cx="130" cy="120" r="2" fill="#3498db"/>
                        <circle cx="150" cy="110" r="2" fill="#3498db"/>
                        <circle cx="170" cy="100" r="2" fill="#3498db"/>
                        <line x1="70" y1="150" x2="200" y2="95" stroke="#e74c3c" stroke-width="2"/>
                        <text x="140" y="175" text-anchor="middle" font-size="8" fill="#666">Spliced →</text>

                        <!-- Flow Field -->
                        <rect x="260" y="60" width="180" height="120" fill="white" stroke="#e91e63" stroke-width="2"/>
                        <text x="350" y="80" text-anchor="middle" font-size="12" font-weight="bold" fill="#e91e63">Flow Field</text>
                        <path d="M 280 100 Q 320 90 360 100 Q 400 110 420 100" stroke="#9c27b0" stroke-width="2" fill="none"/>
                        <path d="M 280 130 Q 320 120 360 130 Q 400 140 420 130" stroke="#9c27b0" stroke-width="2" fill="none"/>
                        <path d="M 280 160 Q 320 150 360 160 Q 400 170 420 160" stroke="#9c27b0" stroke-width="2" fill="none"/>
                        <circle cx="290" cy="100" r="2" fill="#3498db"/>
                        <circle cx="330" cy="95" r="2" fill="#3498db"/>
                        <circle cx="370" cy="105" r="2" fill="#3498db"/>
                        <circle cx="410" cy="100" r="2" fill="#3498db"/>

                        <!-- Grid Arrows -->
                        <rect x="470" y="60" width="180" height="120" fill="white" stroke="#e91e63" stroke-width="2"/>
                        <text x="560" y="80" text-anchor="middle" font-size="12" font-weight="bold" fill="#e91e63">Grid Arrows</text>
                        <path d="M 490 100 L 505 105" stroke="#ff9800" stroke-width="2" marker-end="url(#arrow1)"/>
                        <path d="M 520 100 L 535 105" stroke="#ff9800" stroke-width="2" marker-end="url(#arrow1)"/>
                        <path d="M 550 100 L 565 105" stroke="#ff9800" stroke-width="2" marker-end="url(#arrow1)"/>
                        <path d="M 580 100 L 595 105" stroke="#ff9800" stroke-width="2" marker-end="url(#arrow1)"/>
                        <path d="M 490 130 L 505 135" stroke="#ff9800" stroke-width="2" marker-end="url(#arrow1)"/>
                        <path d="M 520 130 L 535 135" stroke="#ff9800" stroke-width="2" marker-end="url(#arrow1)"/>
                        <path d="M 550 130 L 565 135" stroke="#ff9800" stroke-width="2" marker-end="url(#arrow1)"/>
                        <path d="M 580 130 L 595 135" stroke="#ff9800" stroke-width="2" marker-end="url(#arrow1)"/>

                        <!-- Color Mapping -->
                        <rect x="680" y="60" width="180" height="120" fill="white" stroke="#e91e63" stroke-width="2"/>
                        <text x="770" y="80" text-anchor="middle" font-size="12" font-weight="bold" fill="#e91e63">Color Mapping</text>
                        <circle cx="710" cy="100" r="8" fill="#ff4444"/>
                        <circle cx="730" cy="110" r="8" fill="#ff8888"/>
                        <circle cx="750" cy="120" r="8" fill="#ffffff"/>
                        <circle cx="770" cy="130" r="8" fill="#8888ff"/>
                        <circle cx="790" cy="140" r="8" fill="#4444ff"/>
                        <circle cx="810" cy="150" r="8" fill="#0000ff"/>
                        <text x="770" y="175" text-anchor="middle" font-size="8" fill="#666">Velocity/Expression</text>

                        <!-- Bottom: Mathematical relationships -->
                        <rect x="50" y="200" width="900" height="150" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="500" y="225" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Mathematical Relationships in Visualizations</text>

                        <text x="70" y="250" font-size="12" fill="#333" font-weight="bold">Phase Portrait:</text>
                        <text x="70" y="265" font-size="11" fill="#666">u = γs + q (fitted line)</text>
                        <text x="70" y="280" font-size="11" fill="#666">Points above line: positive velocity</text>
                        <text x="70" y="295" font-size="11" fill="#666">Points below line: negative velocity</text>

                        <text x="300" y="250" font-size="12" fill="#333" font-weight="bold">Flow Field:</text>
                        <text x="300" y="265" font-size="11" fill="#666">Streamlines follow ∇·v = 0</text>
                        <text x="300" y="280" font-size="11" fill="#666">Shows trajectory paths</text>
                        <text x="300" y="295" font-size="11" fill="#666">Density indicates cell flow</text>

                        <text x="530" y="250" font-size="12" fill="#333" font-weight="bold">Grid Arrows:</text>
                        <text x="530" y="265" font-size="11" fill="#666">v̄ = Σ wᵢvᵢ (weighted average)</text>
                        <text x="530" y="280" font-size="11" fill="#666">Arrow length ∝ |v̄|</text>
                        <text x="530" y="295" font-size="11" fill="#666">Direction shows velocity</text>

                        <text x="750" y="250" font-size="12" fill="#333" font-weight="bold">Color Mapping:</text>
                        <text x="750" y="265" font-size="11" fill="#666">Color = f(velocity) or f(expression)</text>
                        <text x="750" y="280" font-size="11" fill="#666">Divergent colormap for velocity</text>
                        <text x="750" y="295" font-size="11" fill="#666">Sequential colormap for expression</text>

                        <!-- Arrow marker definition -->
                        <defs>
                            <marker id="arrow1" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#ff9800"/>
                            </marker>
                        </defs>
                    </svg>
                </div>
            </div>

            <!-- Section 7: Advanced Analysis -->
            <div class="section" id="advanced-analysis">
                <h2>7. Advanced Analysis</h2>

                <h3>7.1 Markov Chain Analysis</h3>

                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>run_markov():</strong> Simulate cell state evolution
                        <div class="math-box" style="margin-top: 0.5rem;">
                            <p>Markov chain evolution:</p>
                            $$p(t+1) = T \cdot p(t)$$
                            <p>Where $T$ is the transition probability matrix and $p(t)$ is the state distribution</p>
                        </div>
                        <div class="code-box" style="margin-top: 0.5rem;">
vlm.run_markov(
    starting_p=None,         # Initial distribution (uniform if None)
    n_steps=2500,            # Number of simulation steps
    mode="time_evolution"    # Simulation mode
)
# Creates: diffused (final probability distribution)
                        </div>
                    </div>
                </div>

                <h3>7.2 Gene-Level Analysis</h3>

                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>gene_knn_imputation():</strong> Gene-wise smoothing
                        <div class="code-box" style="margin-top: 0.5rem;">
vlm.gene_knn_imputation(
    k=15,                    # Number of gene neighbors
    pca_space=False,         # Use expression space
    metric="correlation"     # Distance metric for genes
)
# Smooths genes based on expression similarity
                        </div>
                    </div>
                </div>

                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>score_cluster_expression():</strong> Cluster-based gene filtering
                        <div class="code-box" style="margin-top: 0.5rem;">
vlm.score_cluster_expression(
    min_avg_U=0.02,          # Minimum unspliced expression
    min_avg_S=0.08           # Minimum spliced expression
)
# Requires: cluster_labels attribute
# Creates: clu_avg_selected boolean array
                        </div>
                    </div>
                </div>

                <h3>7.3 Quality Control and Filtering</h3>

                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>filter_genes_by_phase_portrait():</strong> Quality-based filtering
                        <div class="code-box" style="margin-top: 0.5rem;">
vlm.filter_genes_by_phase_portrait(
    minR2=0.1,               # Minimum R² for gamma fit
    min_gamma=0.01,          # Minimum gamma value
    minCorr=0.1              # Minimum U-S correlation
)
# Filters genes with poor velocity estimates
                        </div>
                    </div>
                </div>

                <div class="info-box">
                    <strong>💡 Pro Tip:</strong> Always inspect phase portraits before proceeding with velocity analysis. Genes with poor linear relationships (low R²) will produce unreliable velocity estimates.
                </div>
            </div>

            <!-- Section 8: Function Relationships -->
            <div class="section" id="function-relationships">
                <h2>8. Function Relationships and Dependencies</h2>

                <h3>8.1 Typical Analysis Workflow</h3>

                <div class="svg-container">
                    <svg width="1000" height="700" viewBox="0 0 1000 700">
                        <!-- Background -->
                        <rect width="1000" height="700" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="500" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Complete Analysis Workflow</text>

                        <!-- Step 1: Data Loading -->
                        <rect x="50" y="60" width="150" height="40" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
                        <text x="125" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#1976d2">VelocytoLoom()</text>

                        <!-- Step 2: Basic Filtering -->
                        <rect x="50" y="120" width="150" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="125" y="145" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">score_detection_levels()</text>

                        <!-- Step 3: Normalization -->
                        <rect x="250" y="120" width="150" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="325" y="145" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">normalize()</text>

                        <!-- Step 4: Feature Selection -->
                        <rect x="450" y="120" width="150" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="525" y="145" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">score_cv_vs_mean()</text>

                        <!-- Step 5: Gene Filtering -->
                        <rect x="650" y="120" width="150" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="725" y="145" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">filter_genes()</text>

                        <!-- Step 6: PCA -->
                        <rect x="50" y="200" width="150" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="125" y="225" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">perform_PCA()</text>

                        <!-- Step 7: kNN Imputation -->
                        <rect x="250" y="200" width="150" height="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="325" y="225" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">knn_imputation()</text>

                        <!-- Step 8: Gamma Fitting -->
                        <rect x="450" y="200" width="150" height="40" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="525" y="225" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">fit_gammas()</text>

                        <!-- Step 9: Quality Filtering -->
                        <rect x="650" y="200" width="150" height="40" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="725" y="225" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">filter_genes_by_phase_portrait()</text>

                        <!-- Step 10: Predict U -->
                        <rect x="50" y="280" width="150" height="40" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="125" y="305" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">predict_U()</text>

                        <!-- Step 11: Calculate Velocity -->
                        <rect x="250" y="280" width="150" height="40" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="325" y="305" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">calculate_velocity()</text>

                        <!-- Step 12: Calculate Shift -->
                        <rect x="450" y="280" width="150" height="40" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="525" y="305" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">calculate_shift()</text>

                        <!-- Step 13: Extrapolate -->
                        <rect x="650" y="280" width="150" height="40" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="725" y="305" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">extrapolate_cell_at_t()</text>

                        <!-- Step 14: Embedding -->
                        <rect x="150" y="360" width="150" height="40" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="5"/>
                        <text x="225" y="385" text-anchor="middle" font-size="12" font-weight="bold" fill="#9c27b0">Create Embedding</text>

                        <!-- Step 15: Transition Prob -->
                        <rect x="350" y="360" width="150" height="40" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="5"/>
                        <text x="425" y="385" text-anchor="middle" font-size="12" font-weight="bold" fill="#9c27b0">estimate_transition_prob()</text>

                        <!-- Step 16: Embedding Shift -->
                        <rect x="550" y="360" width="150" height="40" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="5"/>
                        <text x="625" y="385" text-anchor="middle" font-size="12" font-weight="bold" fill="#9c27b0">calculate_embedding_shift()</text>

                        <!-- Visualization Options -->
                        <rect x="50" y="440" width="120" height="30" fill="#ffebee" stroke="#e91e63" stroke-width="2" rx="5"/>
                        <text x="110" y="460" text-anchor="middle" font-size="10" font-weight="bold" fill="#e91e63">plot_phase_portraits()</text>

                        <rect x="190" y="440" width="120" height="30" fill="#ffebee" stroke="#e91e63" stroke-width="2" rx="5"/>
                        <text x="250" y="460" text-anchor="middle" font-size="10" font-weight="bold" fill="#e91e63">plot_velocity_as_flow()</text>

                        <rect x="330" y="440" width="120" height="30" fill="#ffebee" stroke="#e91e63" stroke-width="2" rx="5"/>
                        <text x="390" y="460" text-anchor="middle" font-size="10" font-weight="bold" fill="#e91e63">plot_grid_arrows()</text>

                        <rect x="470" y="440" width="120" height="30" fill="#ffebee" stroke="#e91e63" stroke-width="2" rx="5"/>
                        <text x="530" y="460" text-anchor="middle" font-size="10" font-weight="bold" fill="#e91e63">plot_arrows_embedding()</text>

                        <rect x="610" y="440" width="120" height="30" fill="#ffebee" stroke="#e91e63" stroke-width="2" rx="5"/>
                        <text x="670" y="460" text-anchor="middle" font-size="10" font-weight="bold" fill="#e91e63">plot_velocity_as_color()</text>

                        <!-- Arrows showing workflow -->
                        <path d="M 125 100 L 125 120" stroke="#666" stroke-width="2" marker-end="url(#arrow2)"/>
                        <path d="M 200 140 L 250 140" stroke="#666" stroke-width="2" marker-end="url(#arrow2)"/>
                        <path d="M 400 140 L 450 140" stroke="#666" stroke-width="2" marker-end="url(#arrow2)"/>
                        <path d="M 600 140 L 650 140" stroke="#666" stroke-width="2" marker-end="url(#arrow2)"/>
                        <path d="M 125 160 L 125 200" stroke="#666" stroke-width="2" marker-end="url(#arrow2)"/>
                        <path d="M 200 220 L 250 220" stroke="#666" stroke-width="2" marker-end="url(#arrow2)"/>
                        <path d="M 400 220 L 450 220" stroke="#666" stroke-width="2" marker-end="url(#arrow2)"/>
                        <path d="M 600 220 L 650 220" stroke="#666" stroke-width="2" marker-end="url(#arrow2)"/>
                        <path d="M 125 240 L 125 280" stroke="#666" stroke-width="2" marker-end="url(#arrow2)"/>
                        <path d="M 200 300 L 250 300" stroke="#666" stroke-width="2" marker-end="url(#arrow2)"/>
                        <path d="M 400 300 L 450 300" stroke="#666" stroke-width="2" marker-end="url(#arrow2)"/>
                        <path d="M 600 300 L 650 300" stroke="#666" stroke-width="2" marker-end="url(#arrow2)"/>
                        <path d="M 225 320 L 225 360" stroke="#666" stroke-width="2" marker-end="url(#arrow2)"/>
                        <path d="M 300 380 L 350 380" stroke="#666" stroke-width="2" marker-end="url(#arrow2)"/>
                        <path d="M 500 380 L 550 380" stroke="#666" stroke-width="2" marker-end="url(#arrow2)"/>
                        <path d="M 400 400 L 400 440" stroke="#666" stroke-width="2" marker-end="url(#arrow2)"/>

                        <!-- Legend -->
                        <rect x="50" y="500" width="700" height="150" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="400" y="525" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Function Dependencies and Data Flow</text>

                        <rect x="70" y="540" width="15" height="15" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                        <text x="95" y="552" font-size="11" fill="#333">Data Loading</text>

                        <rect x="200" y="540" width="15" height="15" fill="#fff3e0" stroke="#f57c00" stroke-width="1"/>
                        <text x="225" y="552" font-size="11" fill="#333">Preprocessing</text>

                        <rect x="320" y="540" width="15" height="15" fill="#e8f5e8" stroke="#4caf50" stroke-width="1"/>
                        <text x="345" y="552" font-size="11" fill="#333">Velocity Estimation</text>

                        <rect x="470" y="540" width="15" height="15" fill="#f3e5f5" stroke="#9c27b0" stroke-width="1"/>
                        <text x="495" y="552" font-size="11" fill="#333">Advanced Analysis</text>

                        <rect x="600" y="540" width="15" height="15" fill="#ffebee" stroke="#e91e63" stroke-width="1"/>
                        <text x="625" y="552" font-size="11" fill="#333">Visualization</text>

                        <text x="70" y="580" font-size="11" fill="#333" font-weight="bold">Key Dependencies:</text>
                        <text x="70" y="595" font-size="10" fill="#666">• normalize() requires raw count data (S, U)</text>
                        <text x="70" y="610" font-size="10" fill="#666">• knn_imputation() requires PCA results (pcs)</text>
                        <text x="70" y="625" font-size="10" fill="#666">• fit_gammas() requires normalized/smoothed data</text>

                        <text x="400" y="580" font-size="11" fill="#333" font-weight="bold">Critical Checkpoints:</text>
                        <text x="400" y="595" font-size="10" fill="#666">• Always inspect phase portraits after fit_gammas()</text>
                        <text x="400" y="610" font-size="10" fill="#666">• Filter genes with poor fits before velocity calculation</text>
                        <text x="400" y="625" font-size="10" fill="#666">• Validate embedding quality before projection</text>

                        <!-- Arrow marker definition -->
                        <defs>
                            <marker id="arrow2" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>8.2 External Package Integration</h3>

                <div class="code-box">
# Key external dependencies and their roles:

# 1. NumPy - Core numerical operations
import numpy as np
# Used for: matrix operations, statistical functions, array manipulations

# 2. SciPy - Scientific computing
from scipy import sparse, optimize, stats
# Used for: sparse matrices, optimization (gamma fitting), statistical tests

# 3. Scikit-learn - Machine learning tools
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
from sklearn.svm import SVR
from sklearn.neighbors import NearestNeighbors
# Used for: PCA, t-SNE, SVR (CV vs mean), kNN

# 4. Matplotlib - Visualization
import matplotlib.pyplot as plt
# Used for: all plotting functions

# 5. Loompy - Data I/O
import loompy
# Used for: reading .loom files

# 6. Internal velocyto modules
from .neighbors import BalancedKNN, convolve_by_sparse_weights
from .estimation import fit_slope, fit_slope_weighted
from .diffusion import Diffusion
# Used for: specialized RNA velocity operations
                </div>

                <div class="highlight" style="display: block; text-align: center; padding: 1rem; margin: 2rem 0; font-size: 1.1rem;">
                    🎯 <strong>Key Takeaway:</strong> The VelocytoLoom class orchestrates a complex analysis pipeline through carefully designed method dependencies. Understanding these relationships is crucial for customizing analyses and troubleshooting issues.
                </div>
            </div>
        </div>
    </div>
</body>
</html>
