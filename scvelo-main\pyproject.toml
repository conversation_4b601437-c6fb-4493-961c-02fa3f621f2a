[build-system]
requires = ["setuptools>=61", "setuptools-scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "scvelo"
dynamic = ["version"]
description = "RNA velocity generalized through dynamical modeling"
readme = {file = "README.md", content-type="text/markdown"}
requires-python = ">=3.8"
license = {file = "LICENSE"}
authors = [
    {name = "Volker Bergen"},
    {name = "<PERSON>"}
]
maintainers = [
    {name = "<PERSON>", email = "<EMAIL>"},
]
keywords=[
    "RNA",
    "velocity",
    "single cell",
    "transcriptomics",
    "stochastic",
    "dynamical",
]
classifiers=[
    "License :: OSI Approved :: BSD License",
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Science/Research",
    "Natural Language :: English",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Topic :: Scientific/Engineering :: Bio-Informatics",
    "Topic :: Scientific/Engineering :: Visualization",
]
urls.Documentation = "https://scvelo.readthedocs.io/"
urls.Source = "https://github.com/theislab/scvelo"
urls.Home-page = "https://github.com/theislab/scvelo"
dependencies = [
    "anndata>=0.7.5",
    "scanpy>=1.5",
    "loompy>=2.0.12",
    "umap-learn>=0.3.10",
    "numba>=0.41.0",
    "numpy>=1.17",
    "pandas>=1.1.1, !=1.4.0",
    "scipy>=1.4.1",
    "scikit-learn>=0.21.2",
    "matplotlib>=3.3.0"
]

[project.optional-dependencies]
louvain = [
    "igraph",
    "louvain"
]
hnswlib = [
    "pybind11",
    "hnswlib"
]
test = [
    "pytest",
    "pytest-cov",
]
dev = [
    "ruff",
    "black",
    "isort",
    "hnswlib",
    "hypothesis",
    "louvain",
    "pre-commit>=2.9.0",
    "pybind11",
    "pytest-cov",
    "igraph",
    "setuptools_scm",
]
docs = [
    # Just until rtd.org understands pyproject.toml
    "scanpy",
    "setuptools",
    "setuptools_scm",
    "importlib_metadata",
    "sphinx_rtd_theme>=0.3",
    "sphinx_autodoc_typehints>=1.10.3",
    "sphinxcontrib-bibtex>=2.3.0",
    # converting notebooks to html
    "ipykernel",
    "sphinx>=1.7,<8.0",
    "nbsphinx>=0.7,<0.8.7"
]

[tool.setuptools]
include-package-data = true

[tool.setuptools_scm]

[tool.coverage.run]
source = ["scvelo"]
omit = [
    "**/test_*.py",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
xfail_strict = true
filterwarnings = [
    "ignore:invalid value encountered in:RuntimeWarning",
    "ignore:overflow encountered in:RuntimeWarning",
    "ignore::scipy.sparse.SparseEfficiencyWarning",
    "ignore:X.dtype being converted:FutureWarning",
    "ignore::anndata.OldFormatWarning",
    "ignore::anndata.ImplicitModificationWarning",
]

[tool.black]
include = '\.pyi?$'
exclude = '''
(
  /(
      \.eggs
    | \.git
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | _build
    | buck-out
    | build
    | dist
  )/
)
'''

[tool.isort]
profile = "black"
use_parentheses = true
known_num = "networkx,numpy,pandas,scipy,sklearn,statmodels"
known_plot = "matplotlib,mpl_toolkits,seaborn"
known_bio = "anndata,scanpy"
sections = "FUTURE,STDLIB,THIRDPARTY,NUM,PLOT,BIO,FIRSTPARTY,LOCALFOLDER"
no_lines_before = "LOCALFOLDER"
balanced_wrapping = true
length_sort = "0"
indent = "    "
float_to_top = true
order_by_type = false

[tool.ruff]
src = ["."]
line-length = 119
target-version = "py38"
select = [
    "F",  # Errors detected by Pyflakes
    "E",  # Error detected by Pycodestyle
    "W",  # Warning detected by Pycodestyle
    "D",  # pydocstyle
    "B",  # flake8-bugbear
    "TID",  # flake8-tidy-imports
    "C4",  # flake8-comprehensions
    "BLE",  # flake8-blind-except
    "UP",  # pyupgrade
    "RUF100",  # Report unused noqa directives
]
ignore = [
    # line too long -> we accept long comment lines; black gets rid of long code lines
    "E501",
    # Do not assign a lambda expression, use a def -> lambda expression assignments are convenient
    "E731",
    # allow I, O, l as variable names -> I is the identity matrix
    "E741",
    # Missing docstring in public package
    "D104",
    # Missing docstring in public module
    "D100",
    # Missing docstring in __init__
    "D107",
    # Errors from function calls in argument defaults. These are fine when the result is immutable.
    "B008",
    # __magic__ methods are are often self-explanatory, allow missing docstrings
    "D105",
    # first line should end with a period [Bug: doesn't work with single-line docstrings]
    "D400",
    # First line should be in imperative mood; try rephrasing
    "D401",
    ## Disable one in each pair of mutually incompatible rules
    # We don’t want a blank line before a class docstring
    "D203",
    # We want docstrings to start immediately after the opening triple quote
    "D213",
    # Missing argument description in the docstring TODO: enable
    "D417",
    # Unable to detect undefined names
    "F403",
    # Underfined, or defined from star imports: module
    "F405",
    # Within an except clause, raise exceptions with `raise ... from err`
    "B904",
]

[tool.ruff.per-file-ignores]
"docs/*" = ["BLE001"]
"tests/*" = ["D"]
"*/__init__.py" = ["F401"]

[tool.jupytext]
formats = "ipynb,md"

[tool.cruft]
skip = [
    "tests",
    "src/**/__init__.py",
    "src/**/basic.py",
    "docs/api.md",
    "docs/changelog.md",
    "docs/references.bib",
    "docs/references.md",
    "docs/notebooks/example.ipynb"
]
