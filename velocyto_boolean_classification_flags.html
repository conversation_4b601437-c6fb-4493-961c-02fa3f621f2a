<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Velocyto Boolean Classification Flags</title>
    
    <!-- MathJax 3 for LaTeX rendering -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.8rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 1rem 0 0 0;
            font-size: 1.3rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
            border-radius: 0 8px 8px 0;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.8rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        
        .section h3 {
            color: #34495e;
            margin-top: 2rem;
            font-size: 1.4rem;
        }
        
        .code-box {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            font-size: 0.9rem;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .svg-container {
            text-align: center;
            margin: 2rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-x: auto;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 2px solid #17a2b8;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .success-box {
            background: #d4edda;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .flag-box {
            background: white;
            border: 2px solid #3498db;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            position: relative;
        }
        
        .flag-name {
            background: #3498db;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            position: absolute;
            top: -15px;
            left: 20px;
        }
        
        .toc {
            background: #34495e;
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .toc h3 {
            margin-top: 0;
            color: #ecf0f1;
        }
        
        .toc ul {
            list-style: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 0.5rem 0;
            padding-left: 1rem;
        }
        
        .toc a {
            color: #3498db;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .toc a:hover {
            color: #5dade2;
        }
        
        .flag-category {
            padding: 0.5rem 1rem;
            border-radius: 15px;
            font-weight: bold;
            margin: 0.2rem;
            display: inline-block;
        }
        
        .basic-flags {
            background: #d4edda;
            color: #155724;
            border: 1px solid #28a745;
        }
        
        .validation-flags {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffc107;
        }
        
        .complex-flags {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #dc3545;
        }
        
        .spanning-flags {
            background: #e3f2fd;
            color: #0d47a1;
            border: 1px solid #1976d2;
        }
        
        .decision-tree {
            background: #f8f9fa;
            border: 2px solid #6c757d;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .decision-node {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 0.5rem;
            margin: 0.5rem 0;
            text-align: center;
        }
        
        .true-path {
            border-left: 4px solid #28a745;
            background: #d4edda;
        }
        
        .false-path {
            border-left: 4px solid #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Velocyto Boolean Classification Flags</h1>
            <p>Understanding the Logic Behind Read Classification Decisions</p>
        </div>
        
        <div class="content">
            <!-- Table of Contents -->
            <div class="toc">
                <h3>📚 Table of Contents</h3>
                <ul>
                    <li><a href="#overview">1. Flag System Overview</a></li>
                    <li><a href="#basic-flags">2. Basic Mapping Flags</a></li>
                    <li><a href="#validation-flags">3. Validation Flags</a></li>
                    <li><a href="#complex-flags">4. Complex Model Flags</a></li>
                    <li><a href="#spanning-flags">5. Spanning Flags</a></li>
                    <li><a href="#decision-logic">6. Decision Logic Flow</a></li>
                    <li><a href="#examples">7. Classification Examples</a></li>
                    <li><a href="#comparison">8. Flag Comparison Matrix</a></li>
                </ul>
            </div>

            <!-- Section 1: Overview -->
            <div class="section" id="overview">
                <h2>1. Flag System Overview</h2>
                
                <div class="info-box">
                    <strong>🎯 Purpose:</strong> Boolean classification flags in velocyto provide a systematic way to analyze read mapping patterns and make accurate spliced/unspliced/ambiguous classification decisions.
                </div>
                
                <h3>1.1 Flag Categories</h3>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem; margin: 1rem 0;">
                    <div class="basic-flags">
                        <strong>Basic Mapping Flags</strong><br>
                        <small>• has_onlyexo_model<br>
                        • has_onlyintron_model<br>
                        • has_mixed_model<br>
                        • multi_gene</small>
                    </div>
                    <div class="validation-flags">
                        <strong>Validation Flags</strong><br>
                        <small>• has_validated_intron<br>
                        • has_onlyintron_and_valid_model<br>
                        • has_valid_mixed_model<br>
                        • has_invalid_mixed_model</small>
                    </div>
                    <div class="spanning-flags">
                        <strong>Spanning Flags</strong><br>
                        <small>• has_exin_intron_span<br>
                        • has_only_span_exin_model</small>
                    </div>
                    <div class="complex-flags">
                        <strong>Complex Logic</strong><br>
                        <small>Combinations of multiple flags<br>
                        determine final classification</small>
                    </div>
                </div>
                
                <h3>1.2 Flag Calculation Process</h3>
                <div class="svg-container">
                    <svg width="1400" height="300" viewBox="0 0 1400 300">
                        <!-- Background -->
                        <rect width="1400" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
                        
                        <!-- Title -->
                        <text x="700" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Flag Calculation Process</text>
                        
                        <!-- Step 1: Initialize -->
                        <rect x="50" y="70" width="200" height="80" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
                        <text x="150" y="95" text-anchor="middle" font-size="12" font-weight="bold" fill="#1976d2">1. Initialize Flags</text>
                        <text x="60" y="115" font-size="9" fill="#666">All flags = 0</text>
                        <text x="60" y="130" font-size="9" fill="#666">has_only_span_exin_model = 1</text>
                        <text x="60" y="145" font-size="9" fill="#666">(Assume spanning until proven otherwise)</text>
                        
                        <!-- Step 2: Analyze -->
                        <rect x="300" y="70" width="200" height="80" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="400" y="95" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">2. Analyze Mappings</text>
                        <text x="310" y="115" font-size="9" fill="#666">For each transcript model:</text>
                        <text x="320" y="130" font-size="9" fill="#666">• Check segment types</text>
                        <text x="320" y="145" font-size="9" fill="#666">• Validate introns</text>
                        
                        <!-- Step 3: Set Flags -->
                        <rect x="550" y="70" width="200" height="80" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="650" y="95" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">3. Set Model Flags</text>
                        <text x="560" y="115" font-size="9" fill="#666">Based on analysis:</text>
                        <text x="570" y="130" font-size="9" fill="#666">• Set appropriate flags</text>
                        <text x="570" y="145" font-size="9" fill="#666">• Update spanning status</text>
                        
                        <!-- Step 4: Decision -->
                        <rect x="800" y="70" width="200" height="80" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="5"/>
                        <text x="900" y="95" text-anchor="middle" font-size="12" font-weight="bold" fill="#9c27b0">4. Make Decision</text>
                        <text x="810" y="115" font-size="9" fill="#666">Use flag combinations</text>
                        <text x="810" y="130" font-size="9" fill="#666">in decision tree</text>
                        <text x="810" y="145" font-size="9" fill="#666">→ S/U/A classification</text>
                        
                        <!-- Flow arrows -->
                        <path d="M 250 110 L 300 110" stroke="#666" stroke-width="3" marker-end="url(#arrow)"/>
                        <path d="M 500 110 L 550 110" stroke="#666" stroke-width="3" marker-end="url(#arrow)"/>
                        <path d="M 750 110 L 800 110" stroke="#666" stroke-width="3" marker-end="url(#arrow)"/>
                        
                        <!-- Key insight -->
                        <text x="700" y="200" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Key Insight: Hierarchical Flag System</text>
                        <text x="700" y="220" text-anchor="middle" font-size="12" fill="#666">Flags are calculated per transcript model, then combined to make final decisions</text>
                        <text x="700" y="240" text-anchor="middle" font-size="12" fill="#666">Priority: Spanning > Exon-only > Validated Intron > Mixed > Ambiguous</text>
                        
                        <!-- Arrow marker -->
                        <defs>
                            <marker id="arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>
            </div>

            <!-- Section 2: Basic Flags -->
            <div class="section" id="basic-flags">
                <h2>2. Basic Mapping Flags</h2>

                <h3>2.1 Core Mapping Type Flags</h3>

                <div class="flag-box">
                    <div class="flag-name">has_onlyexo_model</div>
                    <p><strong>Condition:</strong> <code>has_exons and not has_introns</code></p>
                    <p><strong>Meaning:</strong> At least one transcript model maps ONLY to exons (no intronic segments)</p>

                    <div class="svg-container">
                        <svg width="800" height="150" viewBox="0 0 800 150">
                            <!-- Background -->
                            <rect width="800" height="150" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>

                            <!-- Gene structure -->
                            <line x1="100" y1="50" x2="600" y2="50" stroke="#333" stroke-width="2"/>

                            <!-- Exons -->
                            <rect x="150" y="40" width="80" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="190" y="55" text-anchor="middle" font-size="9" fill="#155724">Exon 1</text>

                            <rect x="350" y="40" width="80" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="390" y="55" text-anchor="middle" font-size="9" fill="#155724">Exon 2</text>

                            <!-- Intron -->
                            <rect x="240" y="65" width="100" height="15" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                            <text x="290" y="77" text-anchor="middle" font-size="8" fill="#856404">Intron</text>

                            <!-- Read mapping -->
                            <rect x="160" y="90" width="60" height="10" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
                            <text x="190" y="100" text-anchor="middle" font-size="8" fill="#1976d2">Read</text>

                            <rect x="360" y="90" width="60" height="10" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
                            <text x="390" y="100" text-anchor="middle" font-size="8" fill="#1976d2">Read</text>

                            <!-- Result -->
                            <text x="400" y="130" text-anchor="middle" font-size="12" fill="#28a745" font-weight="bold">has_onlyexo_model = 1</text>
                            <text x="400" y="145" text-anchor="middle" font-size="10" fill="#666">Read maps only to exons → SPLICED</text>
                        </svg>
                    </div>

                    <div class="code-box">
# Flag calculation
if has_exons and not has_introns:
    has_onlyexo_model = 1
                    </div>

                    <div class="success-box">
                        <strong>✅ Classification Impact:</strong> Primary condition for SPLICED reads. Most common scenario (~60-80% of reads).
                    </div>
                </div>

                <div class="flag-box">
                    <div class="flag-name">has_onlyintron_model</div>
                    <p><strong>Condition:</strong> <code>has_introns and not has_exons</code></p>
                    <p><strong>Meaning:</strong> At least one transcript model maps ONLY to introns (no exonic segments)</p>

                    <div class="svg-container">
                        <svg width="800" height="150" viewBox="0 0 800 150">
                            <!-- Background -->
                            <rect width="800" height="150" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>

                            <!-- Gene structure -->
                            <line x1="100" y1="50" x2="600" y2="50" stroke="#333" stroke-width="2"/>

                            <!-- Exons -->
                            <rect x="150" y="40" width="80" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="190" y="55" text-anchor="middle" font-size="9" fill="#155724">Exon 1</text>

                            <rect x="350" y="40" width="80" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="390" y="55" text-anchor="middle" font-size="9" fill="#155724">Exon 2</text>

                            <!-- Intron -->
                            <rect x="240" y="65" width="100" height="15" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                            <text x="290" y="77" text-anchor="middle" font-size="8" fill="#856404">Intron</text>

                            <!-- Read mapping -->
                            <rect x="260" y="90" width="60" height="10" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
                            <text x="290" y="100" text-anchor="middle" font-size="8" fill="#1976d2">Read</text>

                            <!-- Result -->
                            <text x="400" y="130" text-anchor="middle" font-size="12" fill="#ffc107" font-weight="bold">has_onlyintron_model = 1</text>
                            <text x="400" y="145" text-anchor="middle" font-size="10" fill="#666">Read maps only to introns → UNSPLICED (if validated)</text>
                        </svg>
                    </div>

                    <div class="code-box">
# Flag calculation
if has_introns and not has_exons:
    has_onlyintron_model = 1
                    </div>

                    <div class="warning-box">
                        <strong>⚠️ Validation Required:</strong> Intron-only reads require validation to distinguish from genomic DNA contamination.
                    </div>
                </div>

                <div class="flag-box">
                    <div class="flag-name">has_mixed_model</div>
                    <p><strong>Condition:</strong> <code>has_exons and has_introns</code></p>
                    <p><strong>Meaning:</strong> At least one transcript model maps to BOTH exons and introns</p>

                    <div class="svg-container">
                        <svg width="800" height="150" viewBox="0 0 800 150">
                            <!-- Background -->
                            <rect width="800" height="150" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>

                            <!-- Gene structure -->
                            <line x1="100" y1="50" x2="600" y2="50" stroke="#333" stroke-width="2"/>

                            <!-- Exons -->
                            <rect x="150" y="40" width="80" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="190" y="55" text-anchor="middle" font-size="9" fill="#155724">Exon 1</text>

                            <rect x="350" y="40" width="80" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="390" y="55" text-anchor="middle" font-size="9" fill="#155724">Exon 2</text>

                            <!-- Intron -->
                            <rect x="240" y="65" width="100" height="15" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                            <text x="290" y="77" text-anchor="middle" font-size="8" fill="#856404">Intron</text>

                            <!-- Read mapping to both -->
                            <rect x="160" y="90" width="60" height="10" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
                            <text x="190" y="100" text-anchor="middle" font-size="8" fill="#1976d2">Seg1</text>

                            <rect x="260" y="90" width="60" height="10" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
                            <text x="290" y="100" text-anchor="middle" font-size="8" fill="#1976d2">Seg2</text>

                            <!-- Result -->
                            <text x="400" y="130" text-anchor="middle" font-size="12" fill="#9c27b0" font-weight="bold">has_mixed_model = 1</text>
                            <text x="400" y="145" text-anchor="middle" font-size="10" fill="#666">Read maps to both exons and introns → Complex logic</text>
                        </svg>
                    </div>

                    <div class="code-box">
# Flag calculation
if has_exons and has_introns:
    has_mixed_model = 1
    # Further subdivided into valid/invalid based on validation
                    </div>
                </div>

                <div class="flag-box">
                    <div class="flag-name">multi_gene</div>
                    <p><strong>Condition:</strong> <code>len(gene_check) > 1</code></p>
                    <p><strong>Meaning:</strong> Read maps to multiple different genes</p>

                    <div class="info-box">
                        <strong>🚫 Exclusion Criterion:</strong> Reads mapping to multiple genes are typically excluded from velocity analysis to avoid ambiguous gene assignment.
                    </div>

                    <div class="code-box">
# Flag calculation
gene_check: Set[str] = set()
for transcript_model, segments_list in molitem.mappings_record.items():
    gene_check.add(transcript_model.geneid)
    if len(gene_check) > 1:
        multi_gene = 1
                    </div>
                </div>
            </div>

            <!-- Section 3: Validation Flags -->
            <div class="section" id="validation-flags">
                <h2>3. Validation Flags</h2>

                <h3>3.1 Intron Validation System</h3>
                <div class="warning-box">
                    <strong>⚠️ Critical Quality Control:</strong> Validation flags ensure only genuine introns (with spanning read evidence) are used for unspliced classification, preventing false positives from genomic DNA contamination.
                </div>

                <div class="flag-box">
                    <div class="flag-name">has_validated_intron</div>
                    <p><strong>Condition:</strong> <code>segment_match.feature.is_validated</code></p>
                    <p><strong>Meaning:</strong> At least one intron mapping has been validated by spanning reads</p>

                    <div class="svg-container">
                        <svg width="1000" height="200" viewBox="0 0 1000 200">
                            <!-- Background -->
                            <rect width="1000" height="200" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>

                            <!-- Title -->
                            <text x="500" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Intron Validation Process</text>

                            <!-- Gene structure -->
                            <line x1="100" y1="70" x2="700" y2="70" stroke="#333" stroke-width="2"/>

                            <!-- Exons -->
                            <rect x="150" y="60" width="80" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="190" y="75" text-anchor="middle" font-size="9" fill="#155724">Exon 1</text>

                            <rect x="350" y="60" width="80" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="390" y="75" text-anchor="middle" font-size="9" fill="#155724">Exon 2</text>

                            <!-- Intron -->
                            <rect x="240" y="85" width="100" height="15" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                            <text x="290" y="97" text-anchor="middle" font-size="8" fill="#856404">Intron (validated)</text>

                            <!-- Spanning reads -->
                            <rect x="210" y="110" width="60" height="10" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
                            <text x="240" y="120" text-anchor="middle" font-size="8" fill="#1976d2">Spanning Read 1</text>
                            <text x="240" y="135" text-anchor="middle" font-size="7" fill="#666">Exon1 → Intron</text>

                            <rect x="320" y="110" width="60" height="10" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2"/>
                            <text x="350" y="120" text-anchor="middle" font-size="8" fill="#9c27b0">Spanning Read 2</text>
                            <text x="350" y="135" text-anchor="middle" font-size="7" fill="#666">Intron → Exon2</text>

                            <!-- Validation arrows -->
                            <path d="M 240 122 L 290 100" stroke="#e74c3c" stroke-width="2" marker-end="url(#val-arrow)"/>
                            <path d="M 350 122 L 290 100" stroke="#e74c3c" stroke-width="2" marker-end="url(#val-arrow)"/>

                            <!-- Result -->
                            <rect x="750" y="60" width="200" height="80" fill="#d4edda" stroke="#28a745" stroke-width="2" rx="5"/>
                            <text x="850" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#155724">has_validated_intron = 1</text>
                            <text x="850" y="105" text-anchor="middle" font-size="10" fill="#666">Intron confirmed by</text>
                            <text x="850" y="120" text-anchor="middle" font-size="10" fill="#666">spanning reads</text>
                            <text x="850" y="135" text-anchor="middle" font-size="10" fill="#666">→ Safe for UNSPLICED</text>

                            <!-- Arrow marker -->
                            <defs>
                                <marker id="val-arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                    <polygon points="0 0, 8 3, 0 6" fill="#e74c3c"/>
                                </marker>
                            </defs>
                        </svg>
                    </div>

                    <div class="code-box">
# Flag calculation
for segment_match in segments_list:
    if segment_match.maps_to_intron:
        has_introns = 1
        if segment_match.feature.is_validated:
            has_validated_intron = 1
                    </div>
                </div>

                <div class="flag-box">
                    <div class="flag-name">has_onlyintron_and_valid_model</div>
                    <p><strong>Condition:</strong> <code>has_validated_intron and not has_exons</code></p>
                    <p><strong>Meaning:</strong> Transcript model maps only to validated introns (no exonic segments)</p>

                    <div class="success-box">
                        <strong>✅ High Confidence UNSPLICED:</strong> This represents the most confident unspliced reads - purely intronic with validation evidence.
                    </div>

                    <div class="code-box">
# Flag calculation
if has_validated_intron and not has_exons:
    has_onlyintron_and_valid_model = 1
                    </div>
                </div>

                <div class="flag-box">
                    <div class="flag-name">has_valid_mixed_model</div>
                    <p><strong>Condition:</strong> <code>has_exons and has_introns and has_validated_intron and not has_exin_intron_span</code></p>
                    <p><strong>Meaning:</strong> Mixed model with validated introns but no spanning evidence</p>

                    <div class="code-box">
# Flag calculation
if has_exons and has_introns and has_validated_intron and not has_exin_intron_span:
    has_valid_mixed_model = 1
    has_mixed_model = 1
                    </div>
                </div>

                <div class="flag-box">
                    <div class="flag-name">has_invalid_mixed_model</div>
                    <p><strong>Condition:</strong> <code>has_exons and has_introns and not has_validated_intron and not has_exin_intron_span</code></p>
                    <p><strong>Meaning:</strong> Mixed model with unvalidated introns and no spanning evidence</p>

                    <div class="warning-box">
                        <strong>⚠️ Quality Concern:</strong> These reads map to both exons and introns but lack validation evidence, making classification ambiguous.
                    </div>

                    <div class="code-box">
# Flag calculation
if has_exons and has_introns and not has_validated_intron and not has_exin_intron_span:
    has_invalid_mixed_model = 1
    has_mixed_model = 1
                    </div>
                </div>
            </div>

            <!-- Section 4: Spanning Flags -->
            <div class="section" id="spanning-flags">
                <h2>5. Spanning Flags</h2>

                <h3>5.1 Exon-Intron Boundary Spanning</h3>
                <div class="info-box">
                    <strong>🔗 Boundary Detection:</strong> Spanning flags identify reads that cross exon-intron boundaries, providing direct evidence of pre-mRNA molecules.
                </div>

                <div class="flag-box">
                    <div class="flag-name">has_exin_intron_span</div>
                    <p><strong>Condition:</strong> Read segment spans validated exon-intron boundary</p>
                    <p><strong>Meaning:</strong> Direct evidence of unspliced pre-mRNA molecule</p>

                    <div class="svg-container">
                        <svg width="1000" height="250" viewBox="0 0 1000 250">
                            <!-- Background -->
                            <rect width="1000" height="250" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>

                            <!-- Title -->
                            <text x="500" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Exon-Intron Spanning Detection</text>

                            <!-- Gene structure -->
                            <line x1="100" y1="70" x2="700" y2="70" stroke="#333" stroke-width="2"/>

                            <!-- Exons -->
                            <rect x="150" y="60" width="80" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="190" y="75" text-anchor="middle" font-size="9" fill="#155724">Exon 1</text>

                            <rect x="350" y="60" width="80" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="390" y="75" text-anchor="middle" font-size="9" fill="#155724">Exon 2</text>

                            <!-- Intron -->
                            <rect x="240" y="85" width="100" height="15" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                            <text x="290" y="97" text-anchor="middle" font-size="8" fill="#856404">Intron (validated)</text>

                            <!-- Spanning read -->
                            <rect x="210" y="110" width="100" height="12" fill="#e3f2fd" stroke="#1976d2" stroke-width="3"/>
                            <text x="260" y="122" text-anchor="middle" font-size="9" fill="#1976d2" font-weight="bold">Spanning Read</text>

                            <!-- Boundary indicators -->
                            <line x1="230" y1="105" x2="230" y2="130" stroke="#e74c3c" stroke-width="3"/>
                            <text x="230" y="145" text-anchor="middle" font-size="8" fill="#e74c3c">Boundary 1</text>

                            <line x1="350" y1="105" x2="350" y2="130" stroke="#e74c3c" stroke-width="3"/>
                            <text x="350" y="145" text-anchor="middle" font-size="8" fill="#e74c3c">Boundary 2</text>

                            <!-- Detection logic -->
                            <rect x="750" y="60" width="200" height="120" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
                            <text x="850" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#1976d2">Detection Logic</text>

                            <text x="760" y="105" font-size="9" fill="#666">1. Intron end overlaps segment</text>
                            <text x="760" y="120" font-size="9" fill="#666">2. Downstream exon start overlaps</text>
                            <text x="760" y="135" font-size="9" fill="#666">OR</text>
                            <text x="760" y="150" font-size="9" fill="#666">3. Intron start overlaps segment</text>
                            <text x="760" y="165" font-size="9" fill="#666">4. Upstream exon end overlaps</text>

                            <!-- Result -->
                            <text x="500" y="200" text-anchor="middle" font-size="12" fill="#1976d2" font-weight="bold">has_exin_intron_span = 1</text>
                            <text x="500" y="220" text-anchor="middle" font-size="10" fill="#666">Direct evidence of pre-mRNA → UNSPLICED</text>
                        </svg>
                    </div>

                    <div class="code-box">
# Flag calculation
if segment_match.feature.is_validated:
    # Check left boundary: intron end → downstream exon start
    if segment_match.feature.end_overlaps_with_part_of(segment_match.segment):
        downstream_exon = segment_match.feature.get_downstream_exon()
        if downstream_exon.start_overlaps_with_part_of(segment_match.segment):
            has_exin_intron_span = 1

    # Check right boundary: upstream exon end → intron start
    if segment_match.feature.start_overlaps_with_part_of(segment_match.segment):
        upstream_exon = segment_match.feature.get_upstream_exon()
        if upstream_exon.end_overlaps_with_part_of(segment_match.segment):
            has_exin_intron_span = 1
                    </div>
                </div>

                <div class="flag-box">
                    <div class="flag-name">has_only_span_exin_model</div>
                    <p><strong>Condition:</strong> Initially set to 1, becomes 0 if any non-spanning model found</p>
                    <p><strong>Meaning:</strong> ALL compatible transcript models show spanning evidence</p>

                    <div class="success-box">
                        <strong>✅ Highest Priority UNSPLICED:</strong> When all models show spanning, this provides the strongest evidence for unspliced classification.
                    </div>

                    <div class="code-box">
# Flag calculation (starts as 1, gets reset to 0)
has_only_span_exin_model = 1  # Initialize as True

# For each transcript model:
if not has_exin_intron_span:
    has_only_span_exin_model = 0  # Reset if any model lacks spanning
                    </div>
                </div>
            </div>

            <!-- Section 6: Decision Logic -->
            <div class="section" id="decision-logic">
                <h2>6. Decision Logic Flow</h2>

                <h3>6.1 Classification Decision Tree</h3>
                <div class="info-box">
                    <strong>🌳 Hierarchical Decision Making:</strong> Flags are evaluated in priority order to make final spliced/unspliced/ambiguous classifications.
                </div>

                <div class="svg-container">
                    <svg width="1400" height="600" viewBox="0 0 1400 600">
                        <!-- Background -->
                        <rect width="1400" height="600" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="700" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Velocyto Classification Decision Tree</text>

                        <!-- Root decision -->
                        <rect x="600" y="60" width="200" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5"/>
                        <text x="700" y="85" text-anchor="middle" font-size="12" fill="#856404" font-weight="bold">multi_gene?</text>

                        <!-- Multi-gene branch -->
                        <rect x="100" y="140" width="150" height="40" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="5"/>
                        <text x="175" y="165" text-anchor="middle" font-size="11" fill="#721c24" font-weight="bold">NOT COUNTED</text>

                        <!-- Single gene branch -->
                        <rect x="600" y="140" width="200" height="40" fill="#d1ecf1" stroke="#17a2b8" stroke-width="2" rx="5"/>
                        <text x="700" y="165" text-anchor="middle" font-size="12" fill="#0c5460" font-weight="bold">has_only_span_exin_model?</text>

                        <!-- Spanning branch -->
                        <rect x="400" y="220" width="150" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5"/>
                        <text x="475" y="245" text-anchor="middle" font-size="11" fill="#856404" font-weight="bold">UNSPLICED</text>

                        <!-- Non-spanning branch -->
                        <rect x="650" y="220" width="200" height="40" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
                        <text x="750" y="245" text-anchor="middle" font-size="12" fill="#0d47a1" font-weight="bold">has_onlyexo_model?</text>

                        <!-- Exon-only branch -->
                        <rect x="500" y="300" width="150" height="40" fill="#d4edda" stroke="#28a745" stroke-width="2" rx="5"/>
                        <text x="575" y="325" text-anchor="middle" font-size="11" fill="#155724" font-weight="bold">SPLICED</text>

                        <!-- Mixed/intron branch -->
                        <rect x="750" y="300" width="250" height="40" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="5"/>
                        <text x="875" y="325" text-anchor="middle" font-size="12" fill="#7b1fa2" font-weight="bold">has_onlyintron_and_valid_model?</text>

                        <!-- Validated intron-only -->
                        <rect x="650" y="380" width="150" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5"/>
                        <text x="725" y="405" text-anchor="middle" font-size="11" fill="#856404" font-weight="bold">UNSPLICED</text>

                        <!-- Complex mixed -->
                        <rect x="850" y="380" width="200" height="40" fill="#e2e3e5" stroke="#6c757d" stroke-width="2" rx="5"/>
                        <text x="950" y="405" text-anchor="middle" font-size="12" fill="#495057" font-weight="bold">has_valid_mixed_model?</text>

                        <!-- Valid mixed -->
                        <rect x="750" y="460" width="150" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5"/>
                        <text x="825" y="485" text-anchor="middle" font-size="11" fill="#856404" font-weight="bold">UNSPLICED</text>

                        <!-- Ambiguous -->
                        <rect x="950" y="460" width="150" height="40" fill="#e2e3e5" stroke="#6c757d" stroke-width="2" rx="5"/>
                        <text x="1025" y="485" text-anchor="middle" font-size="11" fill="#495057" font-weight="bold">AMBIGUOUS</text>

                        <!-- Decision arrows with labels -->
                        <path d="M 650 100 L 200 140" stroke="#dc3545" stroke-width="2" marker-end="url(#decision-arrow)"/>
                        <text x="400" y="115" text-anchor="middle" font-size="10" fill="#dc3545">Yes</text>

                        <path d="M 700 100 L 700 140" stroke="#17a2b8" stroke-width="2" marker-end="url(#decision-arrow)"/>
                        <text x="720" y="125" font-size="10" fill="#17a2b8">No</text>

                        <path d="M 650 180 L 475 220" stroke="#ffc107" stroke-width="2" marker-end="url(#decision-arrow)"/>
                        <text x="550" y="195" text-anchor="middle" font-size="10" fill="#ffc107">Yes</text>

                        <path d="M 750 180 L 750 220" stroke="#1976d2" stroke-width="2" marker-end="url(#decision-arrow)"/>
                        <text x="770" y="205" font-size="10" fill="#1976d2">No</text>

                        <path d="M 700 260 L 575 300" stroke="#28a745" stroke-width="2" marker-end="url(#decision-arrow)"/>
                        <text x="625" y="275" text-anchor="middle" font-size="10" fill="#28a745">Yes</text>

                        <path d="M 800 260 L 875 300" stroke="#9c27b0" stroke-width="2" marker-end="url(#decision-arrow)"/>
                        <text x="850" y="275" text-anchor="middle" font-size="10" fill="#9c27b0">No</text>

                        <path d="M 825 340 L 725 380" stroke="#ffc107" stroke-width="2" marker-end="url(#decision-arrow)"/>
                        <text x="765" y="355" text-anchor="middle" font-size="10" fill="#ffc107">Yes</text>

                        <path d="M 925 340 L 950 380" stroke="#6c757d" stroke-width="2" marker-end="url(#decision-arrow)"/>
                        <text x="950" y="355" text-anchor="middle" font-size="10" fill="#6c757d">No</text>

                        <path d="M 900 420 L 825 460" stroke="#ffc107" stroke-width="2" marker-end="url(#decision-arrow)"/>
                        <text x="850" y="435" text-anchor="middle" font-size="10" fill="#ffc107">Yes</text>

                        <path d="M 1000 420 L 1025 460" stroke="#6c757d" stroke-width="2" marker-end="url(#decision-arrow)"/>
                        <text x="1025" y="435" text-anchor="middle" font-size="10" fill="#6c757d">No</text>

                        <!-- Priority indicators -->
                        <text x="1200" y="100" font-size="12" font-weight="bold" fill="#333">Priority Order:</text>
                        <text x="1200" y="125" font-size="10" fill="#666">1. Multi-gene exclusion</text>
                        <text x="1200" y="145" font-size="10" fill="#666">2. Spanning evidence</text>
                        <text x="1200" y="165" font-size="10" fill="#666">3. Exon-only (spliced)</text>
                        <text x="1200" y="185" font-size="10" fill="#666">4. Validated intron-only</text>
                        <text x="1200" y="205" font-size="10" fill="#666">5. Valid mixed models</text>
                        <text x="1200" y="225" font-size="10" fill="#666">6. Ambiguous cases</text>

                        <!-- Statistics -->
                        <text x="1200" y="280" font-size="12" font-weight="bold" fill="#333">Typical Distribution:</text>
                        <text x="1200" y="305" font-size="10" fill="#666">SPLICED: ~60-80%</text>
                        <text x="1200" y="325" font-size="10" fill="#666">UNSPLICED: ~15-30%</text>
                        <text x="1200" y="345" font-size="10" fill="#666">AMBIGUOUS: ~5-15%</text>
                        <text x="1200" y="365" font-size="10" fill="#666">NOT COUNTED: ~1-5%</text>

                        <!-- Arrow marker -->
                        <defs>
                            <marker id="decision-arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <h3>6.2 Flag Priority System</h3>
                <div class="decision-tree">
                    <div class="decision-node">
                        <strong>Highest Priority:</strong> <span class="spanning-flags">has_only_span_exin_model</span>
                        <br><small>Direct evidence of pre-mRNA → UNSPLICED</small>
                    </div>

                    <div class="decision-node">
                        <strong>High Priority:</strong> <span class="basic-flags">has_onlyexo_model</span>
                        <br><small>Mature mRNA evidence → SPLICED</small>
                    </div>

                    <div class="decision-node">
                        <strong>Medium Priority:</strong> <span class="validation-flags">has_onlyintron_and_valid_model</span>
                        <br><small>Validated intronic reads → UNSPLICED</small>
                    </div>

                    <div class="decision-node">
                        <strong>Lower Priority:</strong> <span class="validation-flags">has_valid_mixed_model</span>
                        <br><small>Mixed with validation → UNSPLICED</small>
                    </div>

                    <div class="decision-node">
                        <strong>Lowest Priority:</strong> <span class="complex-flags">has_invalid_mixed_model</span>
                        <br><small>Ambiguous cases → AMBIGUOUS</small>
                    </div>
                </div>
            </div>

            <!-- Section 7: Examples -->
            <div class="section" id="examples">
                <h2>7. Classification Examples</h2>

                <h3>7.1 Real-World Scenarios</h3>

                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem; margin: 1rem 0;">
                    <div class="flag-box">
                        <div class="flag-name">Example 1: Typical Spliced Read</div>
                        <div class="code-box">
Read maps to: Exon1, Exon2, Exon3
Flags:
  has_onlyexo_model = 1
  has_onlyintron_model = 0
  has_mixed_model = 0
  has_validated_intron = 0
  has_exin_intron_span = 0
  multi_gene = 0
  has_only_span_exin_model = 0

Classification: SPLICED
                        </div>
                        <div class="success-box">
                            <strong>✅ Clear Case:</strong> Only exonic mapping → mature mRNA
                        </div>
                    </div>

                    <div class="flag-box">
                        <div class="flag-name">Example 2: Spanning Unspliced Read</div>
                        <div class="code-box">
Read spans: Exon1-Intron1 boundary
Flags:
  has_onlyexo_model = 0
  has_onlyintron_model = 0
  has_mixed_model = 1
  has_validated_intron = 1
  has_exin_intron_span = 1
  multi_gene = 0
  has_only_span_exin_model = 1

Classification: UNSPLICED
                        </div>
                        <div class="success-box">
                            <strong>✅ Highest Priority:</strong> Direct spanning evidence → pre-mRNA
                        </div>
                    </div>

                    <div class="flag-box">
                        <div class="flag-name">Example 3: Validated Intronic Read</div>
                        <div class="code-box">
Read maps to: Validated Intron1
Flags:
  has_onlyexo_model = 0
  has_onlyintron_model = 1
  has_mixed_model = 0
  has_validated_intron = 1
  has_exin_intron_span = 0
  multi_gene = 0
  has_onlyintron_and_valid_model = 1

Classification: UNSPLICED
                        </div>
                        <div class="success-box">
                            <strong>✅ High Confidence:</strong> Validated intron-only → unspliced
                        </div>
                    </div>

                    <div class="flag-box">
                        <div class="flag-name">Example 4: Ambiguous Case</div>
                        <div class="code-box">
Read maps to: Exon1, Unvalidated Intron1
Flags:
  has_onlyexo_model = 0
  has_onlyintron_model = 0
  has_mixed_model = 1
  has_validated_intron = 0
  has_exin_intron_span = 0
  multi_gene = 0
  has_invalid_mixed_model = 1

Classification: AMBIGUOUS
                        </div>
                        <div class="warning-box">
                            <strong>⚠️ Uncertain:</strong> Mixed mapping without validation
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section 8: Comparison Matrix -->
            <div class="section" id="comparison">
                <h2>8. Flag Comparison Matrix</h2>

                <h3>8.1 Complete Flag Interaction Table</h3>
                <div class="svg-container">
                    <svg width="1400" height="500" viewBox="0 0 1400 500">
                        <!-- Background -->
                        <rect width="1400" height="500" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                        <!-- Title -->
                        <text x="700" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Boolean Flag Interaction Matrix</text>

                        <!-- Table headers -->
                        <rect x="50" y="50" width="1300" height="30" fill="#34495e" stroke="#2c3e50" stroke-width="1"/>
                        <text x="70" y="70" font-size="10" fill="white" font-weight="bold">Scenario</text>
                        <text x="200" y="70" font-size="9" fill="white" font-weight="bold">onlyexo</text>
                        <text x="270" y="70" font-size="9" fill="white" font-weight="bold">onlyintron</text>
                        <text x="350" y="70" font-size="9" fill="white" font-weight="bold">mixed</text>
                        <text x="420" y="70" font-size="9" fill="white" font-weight="bold">validated</text>
                        <text x="500" y="70" font-size="9" fill="white" font-weight="bold">spanning</text>
                        <text x="580" y="70" font-size="9" fill="white" font-weight="bold">multi_gene</text>
                        <text x="660" y="70" font-size="9" fill="white" font-weight="bold">only_valid</text>
                        <text x="750" y="70" font-size="9" fill="white" font-weight="bold">valid_mixed</text>
                        <text x="850" y="70" font-size="9" fill="white" font-weight="bold">invalid_mixed</text>
                        <text x="970" y="70" font-size="9" fill="white" font-weight="bold">only_span</text>
                        <text x="1100" y="70" font-size="9" fill="white" font-weight="bold">Classification</text>

                        <!-- Row 1: Typical Spliced -->
                        <rect x="50" y="80" width="1300" height="25" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                        <text x="70" y="97" font-size="9" fill="#155724">Exon-only read</text>
                        <text x="210" y="97" font-size="9" fill="#155724" font-weight="bold">1</text>
                        <text x="280" y="97" font-size="9" fill="#666">0</text>
                        <text x="360" y="97" font-size="9" fill="#666">0</text>
                        <text x="430" y="97" font-size="9" fill="#666">0</text>
                        <text x="510" y="97" font-size="9" fill="#666">0</text>
                        <text x="590" y="97" font-size="9" fill="#666">0</text>
                        <text x="670" y="97" font-size="9" fill="#666">0</text>
                        <text x="760" y="97" font-size="9" fill="#666">0</text>
                        <text x="870" y="97" font-size="9" fill="#666">0</text>
                        <text x="980" y="97" font-size="9" fill="#666">0</text>
                        <text x="1110" y="97" font-size="9" fill="#155724" font-weight="bold">SPLICED</text>

                        <!-- Row 2: Spanning Read -->
                        <rect x="50" y="105" width="1300" height="25" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                        <text x="70" y="122" font-size="9" fill="#856404">Spanning read</text>
                        <text x="210" y="122" font-size="9" fill="#666">0</text>
                        <text x="280" y="122" font-size="9" fill="#666">0</text>
                        <text x="360" y="122" font-size="9" fill="#666">1</text>
                        <text x="430" y="122" font-size="9" fill="#666">1</text>
                        <text x="510" y="122" font-size="9" fill="#856404" font-weight="bold">1</text>
                        <text x="590" y="122" font-size="9" fill="#666">0</text>
                        <text x="670" y="122" font-size="9" fill="#666">0</text>
                        <text x="760" y="122" font-size="9" fill="#666">0</text>
                        <text x="870" y="122" font-size="9" fill="#666">0</text>
                        <text x="980" y="122" font-size="9" fill="#856404" font-weight="bold">1</text>
                        <text x="1110" y="122" font-size="9" fill="#856404" font-weight="bold">UNSPLICED</text>

                        <!-- Row 3: Validated Intron -->
                        <rect x="50" y="130" width="1300" height="25" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                        <text x="70" y="147" font-size="9" fill="#856404">Validated intron</text>
                        <text x="210" y="147" font-size="9" fill="#666">0</text>
                        <text x="280" y="147" font-size="9" fill="#856404" font-weight="bold">1</text>
                        <text x="360" y="147" font-size="9" fill="#666">0</text>
                        <text x="430" y="147" font-size="9" fill="#856404" font-weight="bold">1</text>
                        <text x="510" y="147" font-size="9" fill="#666">0</text>
                        <text x="590" y="147" font-size="9" fill="#666">0</text>
                        <text x="670" y="147" font-size="9" fill="#856404" font-weight="bold">1</text>
                        <text x="760" y="147" font-size="9" fill="#666">0</text>
                        <text x="870" y="147" font-size="9" fill="#666">0</text>
                        <text x="980" y="147" font-size="9" fill="#666">0</text>
                        <text x="1110" y="147" font-size="9" fill="#856404" font-weight="bold">UNSPLICED</text>

                        <!-- Row 4: Valid Mixed -->
                        <rect x="50" y="155" width="1300" height="25" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                        <text x="70" y="172" font-size="9" fill="#856404">Valid mixed</text>
                        <text x="210" y="172" font-size="9" fill="#666">0</text>
                        <text x="280" y="172" font-size="9" fill="#666">0</text>
                        <text x="360" y="172" font-size="9" fill="#856404" font-weight="bold">1</text>
                        <text x="430" y="172" font-size="9" fill="#856404" font-weight="bold">1</text>
                        <text x="510" y="172" font-size="9" fill="#666">0</text>
                        <text x="590" y="172" font-size="9" fill="#666">0</text>
                        <text x="670" y="172" font-size="9" fill="#666">0</text>
                        <text x="760" y="172" font-size="9" fill="#856404" font-weight="bold">1</text>
                        <text x="870" y="172" font-size="9" fill="#666">0</text>
                        <text x="980" y="172" font-size="9" fill="#666">0</text>
                        <text x="1110" y="172" font-size="9" fill="#856404" font-weight="bold">UNSPLICED</text>

                        <!-- Row 5: Invalid Mixed -->
                        <rect x="50" y="180" width="1300" height="25" fill="#e2e3e5" stroke="#6c757d" stroke-width="1"/>
                        <text x="70" y="197" font-size="9" fill="#495057">Invalid mixed</text>
                        <text x="210" y="197" font-size="9" fill="#666">0</text>
                        <text x="280" y="197" font-size="9" fill="#666">0</text>
                        <text x="360" y="197" font-size="9" fill="#495057" font-weight="bold">1</text>
                        <text x="430" y="197" font-size="9" fill="#666">0</text>
                        <text x="510" y="197" font-size="9" fill="#666">0</text>
                        <text x="590" y="197" font-size="9" fill="#666">0</text>
                        <text x="670" y="197" font-size="9" fill="#666">0</text>
                        <text x="760" y="197" font-size="9" fill="#666">0</text>
                        <text x="870" y="197" font-size="9" fill="#495057" font-weight="bold">1</text>
                        <text x="980" y="197" font-size="9" fill="#666">0</text>
                        <text x="1110" y="197" font-size="9" fill="#495057" font-weight="bold">AMBIGUOUS</text>

                        <!-- Row 6: Multi-gene -->
                        <rect x="50" y="205" width="1300" height="25" fill="#f8d7da" stroke="#dc3545" stroke-width="1"/>
                        <text x="70" y="222" font-size="9" fill="#721c24">Multi-gene</text>
                        <text x="210" y="222" font-size="9" fill="#666">*</text>
                        <text x="280" y="222" font-size="9" fill="#666">*</text>
                        <text x="360" y="222" font-size="9" fill="#666">*</text>
                        <text x="430" y="222" font-size="9" fill="#666">*</text>
                        <text x="510" y="222" font-size="9" fill="#666">*</text>
                        <text x="590" y="222" font-size="9" fill="#721c24" font-weight="bold">1</text>
                        <text x="670" y="222" font-size="9" fill="#666">*</text>
                        <text x="760" y="222" font-size="9" fill="#666">*</text>
                        <text x="870" y="222" font-size="9" fill="#666">*</text>
                        <text x="980" y="222" font-size="9" fill="#666">*</text>
                        <text x="1110" y="222" font-size="9" fill="#721c24" font-weight="bold">NOT COUNTED</text>

                        <!-- Legend -->
                        <text x="700" y="270" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Flag Value Legend</text>
                        <text x="700" y="290" text-anchor="middle" font-size="10" fill="#666">1 = Flag is set (True) | 0 = Flag is not set (False) | * = Irrelevant (overridden by multi_gene)</text>

                        <!-- Key insights -->
                        <text x="700" y="330" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Key Insights</text>

                        <rect x="100" y="350" width="400" height="120" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="300" y="370" text-anchor="middle" font-size="11" font-weight="bold" fill="#333">Mutual Exclusivity</text>
                        <text x="120" y="390" font-size="9" fill="#666">• onlyexo, onlyintron, mixed are mutually exclusive</text>
                        <text x="120" y="405" font-size="9" fill="#666">• valid_mixed and invalid_mixed are mutually exclusive</text>
                        <text x="120" y="420" font-size="9" fill="#666">• only_span_exin_model requires spanning evidence</text>
                        <text x="120" y="435" font-size="9" fill="#666">• multi_gene overrides all other considerations</text>
                        <text x="120" y="450" font-size="9" fill="#666">• Validation flags require has_validated_intron = 1</text>

                        <rect x="550" y="350" width="400" height="120" fill="white" stroke="#ccc" stroke-width="1"/>
                        <text x="750" y="370" text-anchor="middle" font-size="11" font-weight="bold" fill="#333">Priority Hierarchy</text>
                        <text x="570" y="390" font-size="9" fill="#666">1. multi_gene → NOT COUNTED (highest priority)</text>
                        <text x="570" y="405" font-size="9" fill="#666">2. only_span_exin_model → UNSPLICED</text>
                        <text x="570" y="420" font-size="9" fill="#666">3. onlyexo_model → SPLICED</text>
                        <text x="570" y="435" font-size="9" fill="#666">4. onlyintron_and_valid_model → UNSPLICED</text>
                        <text x="570" y="450" font-size="9" fill="#666">5. valid_mixed_model → UNSPLICED</text>
                        <text x="570" y="465" font-size="9" fill="#666">6. All others → AMBIGUOUS (lowest priority)</text>
                    </svg>
                </div>
            </div>

            <!-- Summary -->
            <div class="section">
                <h2>Summary</h2>

                <div class="highlight" style="display: block; text-align: center; padding: 1rem; margin: 2rem 0; font-size: 1.1rem;">
                    🎯 <strong>Boolean Flag System:</strong> Velocyto's sophisticated flag system enables precise read classification by systematically analyzing mapping patterns, validation evidence, and spanning relationships to make accurate spliced/unspliced/ambiguous decisions.
                </div>

                <h3>Flag Categories Summary</h3>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem; margin: 1rem 0;">
                    <div class="basic-flags" style="padding: 1rem; border-radius: 8px;">
                        <strong>Basic Mapping Flags</strong><br>
                        <small>• Fundamental read-to-feature relationships<br>
                        • Mutually exclusive categories<br>
                        • Foundation for all decisions</small>
                    </div>
                    <div class="validation-flags" style="padding: 1rem; border-radius: 8px;">
                        <strong>Validation Flags</strong><br>
                        <small>• Quality control through spanning evidence<br>
                        • Prevent false positives<br>
                        • Enable confident unspliced classification</small>
                    </div>
                    <div class="spanning-flags" style="padding: 1rem; border-radius: 8px;">
                        <strong>Spanning Flags</strong><br>
                        <small>• Direct pre-mRNA evidence<br>
                        • Highest priority for unspliced<br>
                        • Boundary-crossing detection</small>
                    </div>
                    <div class="complex-flags" style="padding: 1rem; border-radius: 8px;">
                        <strong>Complex Logic</strong><br>
                        <small>• Hierarchical decision making<br>
                        • Priority-based classification<br>
                        • Handles edge cases systematically</small>
                    </div>
                </div>

                <h3>Technical Excellence</h3>
                <div class="info-box">
                    <strong>🏗️ Design Highlights:</strong>
                    <ul>
                        <li><strong>Systematic Analysis:</strong> Boolean flags provide clear, testable conditions</li>
                        <li><strong>Quality Control:</strong> Validation prevents genomic DNA contamination</li>
                        <li><strong>Priority Hierarchy:</strong> Clear precedence rules for complex cases</li>
                        <li><strong>Evidence-Based:</strong> Spanning reads provide direct pre-mRNA evidence</li>
                        <li><strong>Comprehensive Coverage:</strong> Handles all possible mapping scenarios</li>
                        <li><strong>Debuggable Logic:</strong> Flag states enable detailed analysis of decisions</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
