<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Difference Between is_validated and has_exin_intron_span in Velocyto</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        header {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            margin: 0;
            font-size: 2.5rem;
        }
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-top: 0.5rem;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
            }
        }
        .card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }
        .card h2 {
            color: #2575fc;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 0.5rem;
            margin-top: 0;
        }
        .card.validated {
            border-left: 5px solid #28a745;
        }
        .card.spanning {
            border-left: 5px solid #ffc107;
        }
        .comparison {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        }
        .comparison h2 {
            color: #6a11cb;
            text-align: center;
            margin-top: 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }
        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        th {
            background-color: #f1f3f5;
            font-weight: 600;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .visualization {
            text-align: center;
            margin: 2rem 0;
        }
        .math-section {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        }
        .math-section h2 {
            color: #e83e8c;
            margin-top: 0;
        }
        .conclusion {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            margin: 2rem 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .conclusion h2 {
            margin-top: 0;
        }
        .key-point {
            background: rgba(255,255,255,0.2);
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        pre {
            background: #f1f3f5;
            padding: 1rem;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            font-family: 'Courier New', monospace;
        }
        .highlight {
            background: #fff3cd;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <header>
        <h1>Difference Between <code>is_validated</code> and <code>has_exin_intron_span</code></h1>
        <div class="subtitle">Understanding Velocyto's Splicing Quantification Mechanisms</div>
    </header>

    <div class="container">
        <div class="card validated">
            <h2><code>is_validated</code></h2>
            <p><span class="highlight">Feature-level property</span> indicating whether an intron has been confirmed by any read spanning exon-intron boundaries.</p>
            
            <h3>Characteristics:</h3>
            <ul>
                <li>Belongs to the <code>Feature</code> object (intron)</li>
                <li>Persistent property that can be set by any read</li>
                <li>Used to distinguish true introns from genomic noise</li>
                <li>Applied across the entire dataset analysis</li>
            </ul>
            
            <h3>Setting Process:</h3>
            <pre><code># When a read validates an intron
if segment_spans_exon_intron_boundary:
    intron_feature.is_validated = True</code></pre>
        </div>
        
        <div class="card spanning">
            <h2><code>has_exin_intron_span</code></h2>
            <p><span class="highlight">Read-level property</span> indicating whether a specific read spans an exon-intron boundary in its current alignment.</p>
            
            <h3>Characteristics:</h3>
            <ul>
                <li>Belongs to the <code>SegmentMatch</code> object (specific alignment)</li>
                <li>Transient property for a specific read's alignment</li>
                <li>Used to classify the current read as unspliced</li>
                <li>Evaluated for each individual read</li>
            </ul>
            
            <h3>Evaluation Process:</h3>
            <pre><code># When evaluating a read's alignment
if segment_match.maps_to_intron:
    if segment_overlaps_both_intron_and_exon:
        segment_match.has_exin_intron_span = True</code></pre>
        </div>
    </div>

    <div class="comparison">
        <h2>Direct Comparison</h2>
        <table>
            <thead>
                <tr>
                    <th>Aspect</th>
                    <th><code>is_validated</code></th>
                    <th><code>has_exin_intron_span</code></th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Level</strong></td>
                    <td>Feature (Intron)</td>
                    <td>Read Alignment (SegmentMatch)</td>
                </tr>
                <tr>
                    <td><strong>Scope</strong></td>
                    <td>Persistent across all reads</td>
                    <td>Specific to current read</td>
                </tr>
                <tr>
                    <td><strong>Purpose</strong></td>
                    <td>Intron validation/quality control</td>
                    <td>Read classification</td>
                </tr>
                <tr>
                    <td><strong>When Set</strong></td>
                    <td>When any read validates the intron</td>
                    <td>When current read spans boundary</td>
                </tr>
                <tr>
                    <td><strong>Impact</strong></td>
                    <td>Affects future read classifications</td>
                    <td>Affects current read classification</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="visualization">
        <h2>Visual Representation</h2>
        <svg width="100%" height="500" viewBox="0 0 800 500" xmlns="http://www.w3.org/2000/svg">
            <!-- Background -->
            <rect width="100%" height="100%" fill="#f8f9fa"/>
            
            <!-- Title -->
            <text x="400" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#333">
                Velocyto Intron Validation and Read Classification
            </text>
            
            <!-- Genome track -->
            <rect x="50" y="80" width="700" height="40" fill="#e9ecef" rx="5"/>
            <text x="400" y="105" text-anchor="middle" font-size="14" fill="#495057">Genomic Region</text>
            
            <!-- Exon 1 -->
            <rect x="50" y="80" width="150" height="40" fill="#28a745" rx="5"/>
            <text x="125" y="105" text-anchor="middle" font-size="12" fill="white">Exon 1</text>
            
            <!-- Intron (initially not validated) -->
            <rect x="200" y="80" width="400" height="40" fill="#6c757d" rx="5" id="intron"/>
            <text x="400" y="105" text-anchor="middle" font-size="12" fill="white" id="intron-text">Intron (is_validated = False)</text>
            
            <!-- Exon 2 -->
            <rect x="600" y="80" width="150" height="40" fill="#28a745" rx="5"/>
            <text x="675" y="105" text-anchor="middle" font-size="12" fill="white">Exon 2</text>
            
            <!-- Read 1 - Non-spanning -->
            <rect x="250" y="150" width="100" height="20" fill="#007bff" rx="3"/>
            <text x="300" y="165" text-anchor="middle" font-size="10" fill="white">Read 1</text>
            <line x1="300" y1="170" x2="400" y2="80" stroke="#007bff" stroke-width="2" stroke-dasharray="5,5"/>
            <text x="350" y="130" text-anchor="middle" font-size="12" fill="#007bff">
                maps_to_intron = True
            </text>
            <text x="350" y="145" text-anchor="middle" font-size="12" fill="#007bff">
                has_exin_intron_span = False
            </text>
            
            <!-- Read 2 - Spanning -->
            <rect x="350" y="200" width="200" height="20" fill="#ffc107" rx="3"/>
            <text x="450" y="215" text-anchor="middle" font-size="10" fill="#333">Read 2 (Spanning)</text>
            <line x1="350" y1="200" x2="200" y2="120" stroke="#ffc107" stroke-width="2"/>
            <line x1="550" y1="200" x2="600" y2="120" stroke="#ffc107" stroke-width="2"/>
            
            <!-- Validation indicator -->
            <circle cx="450" cy="250" r="20" fill="#28a745"/>
            <text x="450" y="255" text-anchor="middle" font-size="12" fill="white">✓</text>
            <line x1="450" y1="220" x2="450" y2="120" stroke="#28a745" stroke-width="2" marker-end="url(#arrow)"/>
            
            <!-- Arrow marker -->
            <defs>
                <marker id="arrow" markerWidth="10" markerHeight="10" refX="6" refY="3" orient="auto" markerUnits="strokeWidth">
                    <path d="M0,0 L0,6 L9,3 z" fill="#28a745"/>
                </marker>
            </defs>
            
            <!-- Updated intron status -->
            <text x="400" y="280" text-anchor="middle" font-size="14" fill="#28a745" font-weight="bold">
                Intron becomes validated (is_validated = True)
            </text>
            
            <!-- Read 3 - Mapping to validated intron -->
            <rect x="400" y="300" width="100" height="20" fill="#6f42c1" rx="3"/>
            <text x="450" y="315" text-anchor="middle" font-size="10" fill="white">Read 3</text>
            <line x1="450" y1="320" x2="400" y2="120" stroke="#6f42c1" stroke-width="2" stroke-dasharray="5,5"/>
            <text x="450" y="340" text-anchor="middle" font-size="12" fill="#6f42c1">
                maps_to_intron = True
            </text>
            <text x="450" y="355" text-anchor="middle" font-size="12" fill="#6f42c1">
                is_validated = True
            </text>
            <text x="450" y="370" text-anchor="middle" font-size="12" fill="#6f42c1">
                has_exin_intron_span = False
            </text>
            
            <!-- Legend -->
            <rect x="50" y="400" width="15" height="15" fill="#007bff"/>
            <text x="70" y="412" font-size="12" fill="#333">Non-spanning read mapping to intron</text>
            
            <rect x="350" y="400" width="15" height="15" fill="#ffc107"/>
            <text x="370" y="412" font-size="12" fill="#333">Spanning read (sets is_validated)</text>
            
            <rect x="50" y="430" width="15" height="15" fill="#6f42c1"/>
            <text x="70" y="442" font-size="12" fill="#333">Read mapping to validated intron</text>
            
            <circle cx="365" y="437" r="7" fill="#28a745"/>
            <text x="385" y="442" font-size="12" fill="#333">Validation event</text>
        </svg>
    </div>

    <div class="math-section">
        <h2>Mathematical Relationships</h2>
        
        <h3>Intron Validation Process</h3>
        <p>For an intron \( I \) to become validated:</p>
        <p>$$\text{is_validated}(I) = \text{True} \iff \exists \text{ read } r: \text{has_exin_intron_span}(r, I) = \text{True}$$</p>
        
        <h3>Read Classification Rules</h3>
        <p>A read \( r \) is classified as unspliced if either:</p>
        <p>$$\text{ClassifyUnspliced}(r) = \begin{cases} 
        \text{True}, & \text{if } \text{has_exin_intron_span}(r) = \text{True} \\
        \text{True}, & \text{if } \text{maps_to_intron}(r) \land \text{is_validated}(\text{intron}(r)) = \text{True} \\
        \text{False}, & \text{otherwise}
        \end{cases}$$</p>
        
        <h3>Temporal Relationship</h3>
        <p>The relationship between these properties over time \( t \):</p>
        <p>$$\text{is_validated}_t(I) = \bigvee_{i=1}^{t} \text{has_exin_intron_span}(r_i, I)$$</p>
        <p>Where \( \bigvee \) represents the logical OR operation over all reads processed up to time \( t \).</p>
    </div>

    <div class="conclusion">
        <h2>Key Takeaways</h2>
        
        <div class="key-point">
            <h3>1. Different Levels of Operation</h3>
            <p><code>is_validated</code> operates at the <strong>feature level</strong> (intron), while <code>has_exin_intron_span</code> operates at the <strong>read alignment level</strong> (segment match).</p>
        </div>
        
        <div class="key-point">
            <h3>2. Temporal Relationship</h3>
            <p><code>has_exin_intron_span</code> is a <strong>transient property</strong> evaluated for each read, whereas <code>is_validated</code> is a <strong>persistent property</strong> that accumulates validation evidence.</p>
        </div>
        
        <div class="key-point">
            <h3>3. Causal Relationship</h3>
            <p>When <code>has_exin_intron_span = True</code> for any read mapping to an intron, it causes <code>is_validated</code> for that intron to become <code>True</code>.</p>
        </div>
        
        <div class="key-point">
            <h3>4. Complementary Roles</h3>
            <p>Both properties work together to ensure accurate splicing quantification:
                <ul>
                    <li><code>has_exin_intron_span</code> identifies reads that directly evidence splicing</li>
                    <li><code>is_validated</code> maintains quality control for intron annotations</li>
                </ul>
            </p>
        </div>
    </div>
</body>
</html>