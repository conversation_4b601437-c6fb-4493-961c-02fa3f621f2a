<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Velocyto GTF Processing Pipeline</title>
    
    <!-- MathJax 3 for LaTeX rendering -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.8rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 1rem 0 0 0;
            font-size: 1.3rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
            border-radius: 0 8px 8px 0;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.8rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        
        .section h3 {
            color: #34495e;
            margin-top: 2rem;
            font-size: 1.4rem;
        }
        
        .code-box {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            font-size: 0.9rem;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .svg-container {
            text-align: center;
            margin: 2rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-x: auto;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 2px solid #17a2b8;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .success-box {
            background: #d4edda;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .pipeline-step {
            background: white;
            border: 2px solid #3498db;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            position: relative;
        }
        
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            position: absolute;
            top: -20px;
            left: 20px;
            font-size: 1.2rem;
        }
        
        .gtf-line {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 0.5rem;
            margin: 0.5rem 0;
            font-family: monospace;
            font-size: 0.8rem;
            overflow-x: auto;
        }
        
        .field-highlight {
            background: #fff3cd;
            padding: 0.1rem 0.3rem;
            border-radius: 3px;
            font-weight: bold;
        }
        
        .toc {
            background: #34495e;
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .toc h3 {
            margin-top: 0;
            color: #ecf0f1;
        }
        
        .toc ul {
            list-style: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 0.5rem 0;
            padding-left: 1rem;
        }
        
        .toc a {
            color: #3498db;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .toc a:hover {
            color: #5dade2;
        }
        
        .data-structure {
            background: white;
            border: 2px solid #6c757d;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .structure-title {
            background: #6c757d;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧬 Velocyto GTF Processing Pipeline</h1>
            <p>From Raw GTF Annotations to Efficient Feature Indexes</p>
        </div>
        
        <div class="content">
            <!-- Table of Contents -->
            <div class="toc">
                <h3>📚 Table of Contents</h3>
                <ul>
                    <li><a href="#overview">1. Processing Overview</a></li>
                    <li><a href="#gtf-parsing">2. GTF File Parsing</a></li>
                    <li><a href="#transcript-models">3. TranscriptModel Creation</a></li>
                    <li><a href="#intron-generation">4. Intron Generation</a></li>
                    <li><a href="#feature-indexing">5. Feature Index Building</a></li>
                    <li><a href="#validation-markup">6. Intron Validation Markup</a></li>
                    <li><a href="#optimization">7. Performance Optimization</a></li>
                    <li><a href="#data-structures">8. Final Data Structures</a></li>
                </ul>
            </div>

            <!-- Section 1: Overview -->
            <div class="section" id="overview">
                <h2>1. Processing Overview</h2>
                
                <div class="info-box">
                    <strong>🎯 Goal:</strong> Transform GTF annotation files into efficient, searchable data structures that enable fast genomic feature lookup during read processing.
                </div>
                
                <h3>1.1 Complete Processing Pipeline</h3>
                <div class="svg-container">
                    <svg width="1600" height="300" viewBox="0 0 1600 300">
                        <!-- Background -->
                        <rect width="1600" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
                        
                        <!-- Title -->
                        <text x="800" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">GTF Processing Pipeline Overview</text>
                        
                        <!-- Step 1: GTF File -->
                        <rect x="50" y="80" width="150" height="80" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
                        <text x="125" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#1976d2">GTF File</text>
                        <text x="60" y="125" font-size="9" fill="#666">• Raw annotations</text>
                        <text x="60" y="140" font-size="9" fill="#666">• Exon coordinates</text>
                        <text x="60" y="155" font-size="9" fill="#666">• Gene metadata</text>
                        
                        <!-- Step 2: Parse -->
                        <rect x="250" y="80" width="150" height="80" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                        <text x="325" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">Parse GTF</text>
                        <text x="260" y="125" font-size="9" fill="#666">• Extract fields</text>
                        <text x="260" y="140" font-size="9" fill="#666">• Validate format</text>
                        <text x="260" y="155" font-size="9" fill="#666">• Group by transcript</text>
                        
                        <!-- Step 3: TranscriptModels -->
                        <rect x="450" y="80" width="150" height="80" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="525" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">TranscriptModels</text>
                        <text x="460" y="125" font-size="9" fill="#666">• Create objects</text>
                        <text x="460" y="140" font-size="9" fill="#666">• Add exons</text>
                        <text x="460" y="155" font-size="9" fill="#666">• Link metadata</text>
                        
                        <!-- Step 4: Generate Introns -->
                        <rect x="650" y="80" width="150" height="80" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="5"/>
                        <text x="725" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#9c27b0">Generate Introns</text>
                        <text x="660" y="125" font-size="9" fill="#666">• Calculate boundaries</text>
                        <text x="660" y="140" font-size="9" fill="#666">• Create intron features</text>
                        <text x="660" y="155" font-size="9" fill="#666">• Handle strand logic</text>
                        
                        <!-- Step 5: Feature Indexes -->
                        <rect x="850" y="80" width="150" height="80" fill="#ffebee" stroke="#e91e63" stroke-width="2" rx="5"/>
                        <text x="925" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#e91e63">Feature Indexes</text>
                        <text x="860" y="125" font-size="9" fill="#666">• Sort features</text>
                        <text x="860" y="140" font-size="9" fill="#666">• Build indexes</text>
                        <text x="860" y="155" font-size="9" fill="#666">• Per chromosome</text>
                        
                        <!-- Step 6: Validation -->
                        <rect x="1050" y="80" width="150" height="80" fill="#d1ecf1" stroke="#17a2b8" stroke-width="2" rx="5"/>
                        <text x="1125" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#0c5460">Intron Validation</text>
                        <text x="1060" y="125" font-size="9" fill="#666">• Mark spanning reads</text>
                        <text x="1060" y="140" font-size="9" fill="#666">• Quality control</text>
                        <text x="1060" y="155" font-size="9" fill="#666">• Set is_validated</text>
                        
                        <!-- Step 7: Ready -->
                        <rect x="1250" y="80" width="150" height="80" fill="#d4edda" stroke="#28a745" stroke-width="2" rx="5"/>
                        <text x="1325" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#155724">Ready for Use</text>
                        <text x="1260" y="125" font-size="9" fill="#666">• Fast lookup</text>
                        <text x="1260" y="140" font-size="9" fill="#666">• Memory efficient</text>
                        <text x="1260" y="155" font-size="9" fill="#666">• Validated introns</text>
                        
                        <!-- Flow arrows -->
                        <path d="M 200 120 L 250 120" stroke="#666" stroke-width="3" marker-end="url(#arrow)"/>
                        <path d="M 400 120 L 450 120" stroke="#666" stroke-width="3" marker-end="url(#arrow)"/>
                        <path d="M 600 120 L 650 120" stroke="#666" stroke-width="3" marker-end="url(#arrow)"/>
                        <path d="M 800 120 L 850 120" stroke="#666" stroke-width="3" marker-end="url(#arrow)"/>
                        <path d="M 1000 120 L 1050 120" stroke="#666" stroke-width="3" marker-end="url(#arrow)"/>
                        <path d="M 1200 120 L 1250 120" stroke="#666" stroke-width="3" marker-end="url(#arrow)"/>
                        
                        <!-- Processing time -->
                        <text x="800" y="220" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">Processing Time</text>
                        <text x="800" y="240" text-anchor="middle" font-size="12" fill="#666">Human genome: ~2-5 minutes | Mouse genome: ~1-3 minutes</text>
                        <text x="800" y="260" text-anchor="middle" font-size="12" fill="#666">Memory usage: 3-6 GB for indexes | One-time cost for entire analysis</text>
                        
                        <!-- Arrow marker -->
                        <defs>
                            <marker id="arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>
                
                <h3>1.2 Key Processing Statistics</h3>
                <div style="display: flex; flex-wrap: wrap; gap: 1rem; justify-content: space-around;">
                    <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 1rem; text-align: center; min-width: 150px;">
                        <div style="font-size: 1.2rem; font-weight: bold; color: #2c3e50;">~200,000</div>
                        <div style="font-size: 0.9rem; color: #666;">Exons per chromosome</div>
                    </div>
                    <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 1rem; text-align: center; min-width: 150px;">
                        <div style="font-size: 1.2rem; font-weight: bold; color: #2c3e50;">~200,000</div>
                        <div style="font-size: 0.9rem; color: #666;">Generated introns</div>
                    </div>
                    <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 1rem; text-align: center; min-width: 150px;">
                        <div style="font-size: 1.2rem; font-weight: bold; color: #2c3e50;">~60,000</div>
                        <div style="font-size: 0.9rem; color: #666;">Genes (human)</div>
                    </div>
                    <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 1rem; text-align: center; min-width: 150px;">
                        <div style="font-size: 1.2rem; font-weight: bold; color: #2c3e50;">~250,000</div>
                        <div style="font-size: 0.9rem; color: #666;">Transcript isoforms</div>
                    </div>
                </div>
            </div>

            <!-- Section 2: GTF Parsing -->
            <div class="section" id="gtf-parsing">
                <h2>2. GTF File Parsing</h2>

                <div class="pipeline-step">
                    <div class="step-number">1</div>
                    <h3 style="margin-top: 0; padding-left: 60px;">Raw GTF File Processing</h3>

                    <h3>2.1 GTF Format Structure</h3>
                    <div class="info-box">
                        <strong>📋 GTF Format:</strong> Gene Transfer Format (GTF) is a tab-delimited text format for describing gene structure. Each line represents a genomic feature with 9 required fields.
                    </div>

                    <div class="gtf-line">
                        <span class="field-highlight">chr1</span>&nbsp;&nbsp;&nbsp;
                        <span class="field-highlight">HAVANA</span>&nbsp;&nbsp;&nbsp;
                        <span class="field-highlight">exon</span>&nbsp;&nbsp;&nbsp;
                        <span class="field-highlight">11869</span>&nbsp;&nbsp;&nbsp;
                        <span class="field-highlight">12227</span>&nbsp;&nbsp;&nbsp;
                        <span class="field-highlight">.</span>&nbsp;&nbsp;&nbsp;
                        <span class="field-highlight">+</span>&nbsp;&nbsp;&nbsp;
                        <span class="field-highlight">.</span>&nbsp;&nbsp;&nbsp;
                        <span class="field-highlight">gene_id "ENSG00000223972"; transcript_id "ENST00000456328"; exon_number "1";</span>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem; margin: 1rem 0;">
                        <div style="background: #e3f2fd; padding: 0.5rem; border-radius: 4px;">
                            <strong>Field 1:</strong> Chromosome<br>
                            <small>chr1, chr2, chrX, etc.</small>
                        </div>
                        <div style="background: #fff3e0; padding: 0.5rem; border-radius: 4px;">
                            <strong>Field 2:</strong> Source<br>
                            <small>HAVANA, ENSEMBL, etc.</small>
                        </div>
                        <div style="background: #e8f5e8; padding: 0.5rem; border-radius: 4px;">
                            <strong>Field 3:</strong> Feature Type<br>
                            <small>exon, gene, transcript</small>
                        </div>
                        <div style="background: #f3e5f5; padding: 0.5rem; border-radius: 4px;">
                            <strong>Fields 4-5:</strong> Coordinates<br>
                            <small>Start and end positions</small>
                        </div>
                        <div style="background: #ffebee; padding: 0.5rem; border-radius: 4px;">
                            <strong>Field 7:</strong> Strand<br>
                            <small>+ (forward) or - (reverse)</small>
                        </div>
                        <div style="background: #d1ecf1; padding: 0.5rem; border-radius: 4px;">
                            <strong>Field 9:</strong> Attributes<br>
                            <small>gene_id, transcript_id, etc.</small>
                        </div>
                    </div>

                    <h3>2.2 Parsing Implementation</h3>
                    <div class="code-box">
# GTF parsing implementation in read_transcriptmodels()
def read_transcriptmodels(self, gtf_file: str):
    """Parse GTF file and create TranscriptModel objects"""

    # Define regex patterns for attribute extraction
    regex_trid = re.compile('transcript_id "([^"]+)"')
    regex_trname = re.compile('transcript_name "([^"]+)"')
    regex_geneid = re.compile('gene_id "([^"]+)"')
    regex_genename = re.compile('gene_name "([^"]+)"')
    regex_exonno = re.compile('exon_number "*?([\w]+)')

    # Read and filter GTF lines (skip comments)
    gtf_lines = [line for line in open(gtf_file) if not line.startswith('#')]

    # Handle missing exon numbers (quality control)
    gtf_lines = self.peek_and_correct(gtf_lines)

    # Initialize data structures
    features = OrderedDict()  # transcript_id -> TranscriptModel

    # Process each GTF line
    for line in gtf_lines:
        fields = line.rstrip().split('\t')
        chrom, source, feature_type, start_str, end_str, score, strand, frame, attributes = fields

        # Only process exon features
        if feature_type == "exon":
            # Extract required attributes
            trid = regex_trid.search(attributes).group(1)
            geneid = regex_geneid.search(attributes).group(1)
            genename = regex_genename.search(attributes).group(1)
            exonno = int(regex_exonno.search(attributes).group(1))

            # Handle chromosome naming (remove 'chr' prefix)
            if "chr" in chrom[:4]:
                chrom = chrom[3:]

            # Create genomic coordinates
            start = int(start_str)
            end = int(end_str)
            chromstrand = chrom + strand

            # Create or update TranscriptModel
            if trid not in features:
                features[trid] = TranscriptModel(
                    trid=trid, trname=trname, geneid=geneid,
                    genename=genename, chromstrand=chromstrand
                )

            # Add exon to transcript model
            exon_feature = Feature(start=start, end=end, kind=ord("e"), exin_no=exonno)
            features[trid].append_exon(exon_feature)

    return features
                    </div>

                    <h3>2.3 Quality Control: Missing Exon Numbers</h3>
                    <div class="warning-box">
                        <strong>⚠️ Common Issue:</strong> Some GTF files lack exon_number attributes. Velocyto automatically detects this and infers exon numbers from genomic positions.
                    </div>

                    <div class="code-box">
# Quality control for missing exon numbers
def peek_and_correct(self, gtf_lines):
    """Detect and fix missing exon numbers"""

    # Check first 500 lines for missing exon_number
    regex_exonno = re.compile('exon_number "*?([\w]+)')
    missing_exon_numbers = False

    for line in gtf_lines[:500]:
        fields = line.split("\t")
        if fields[2] == "exon":  # feature_type
            if not regex_exonno.search(fields[8]):  # attributes
                missing_exon_numbers = True
                break

    if missing_exon_numbers:
        logging.warning("Missing exon_number entries detected. Inferring from position.")

        # Sort exons by transcript and position
        exon_data = []
        for line in gtf_lines:
            fields = line.split("\t")
            if fields[2] == "exon":
                trid = extract_transcript_id(fields[8])
                start = int(fields[3])
                strand = fields[6]
                exon_data.append([trid, start, strand, line])

        # Sort and assign exon numbers
        exon_data.sort()  # Sort by transcript_id, then start position

        corrected_lines = []
        current_transcript = None
        exon_number = 1

        for trid, start, strand, line in exon_data:
            if trid != current_transcript:
                current_transcript = trid
                exon_number = 1
            else:
                exon_number += 1

            # Add exon_number to attributes
            corrected_line = line.rstrip() + f' exon_number "{exon_number}";\n'
            corrected_lines.append(corrected_line)

        return corrected_lines

    return gtf_lines
                    </div>
                </div>
            </div>

            <!-- Section 3: TranscriptModel Creation -->
            <div class="section" id="transcript-models">
                <h2>3. TranscriptModel Creation</h2>

                <div class="pipeline-step">
                    <div class="step-number">2</div>
                    <h3 style="margin-top: 0; padding-left: 60px;">Building Transcript Objects</h3>

                    <h3>3.1 TranscriptModel Structure</h3>
                    <div class="data-structure">
                        <div class="structure-title">TranscriptModel Class</div>
                        <div class="code-box">
class TranscriptModel:
    """A transcript model as a list of Feature objects"""
    __slots__ = ["trid", "trname", "geneid", "genename", "chromstrand", "list_features"]

    def __init__(self, trid: str, trname: str, geneid: str, genename: str, chromstrand: str):
        self.trid = trid                    # Transcript ID (e.g., "ENST00000456328")
        self.trname = trname                # Transcript name (e.g., "DDX11L1-202")
        self.geneid = geneid                # Gene ID (e.g., "ENSG00000223972")
        self.genename = genename            # Gene name (e.g., "DDX11L1")
        self.chromstrand = chromstrand      # Chromosome + strand (e.g., "1+")
        self.list_features = []             # List of Feature objects (exons and introns)
                        </div>
                    </div>

                    <h3>3.2 Exon Addition Process</h3>
                    <div class="svg-container">
                        <svg width="1400" height="400" viewBox="0 0 1400 400">
                            <!-- Background -->
                            <rect width="1400" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                            <!-- Title -->
                            <text x="700" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">TranscriptModel Construction Process</text>

                            <!-- GTF Lines -->
                            <text x="100" y="70" font-size="12" font-weight="bold" fill="#333">Input: GTF Exon Lines</text>

                            <!-- GTF line 1 -->
                            <rect x="100" y="80" width="400" height="30" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                            <text x="110" y="100" font-size="9" font-family="monospace" fill="#666">chr1 HAVANA exon 11869 12227 . + . transcript_id "ENST00000456328"; exon_number "1";</text>

                            <!-- GTF line 2 -->
                            <rect x="100" y="120" width="400" height="30" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                            <text x="110" y="140" font-size="9" font-family="monospace" fill="#666">chr1 HAVANA exon 12613 12721 . + . transcript_id "ENST00000456328"; exon_number "2";</text>

                            <!-- GTF line 3 -->
                            <rect x="100" y="160" width="400" height="30" fill="#e3f2fd" stroke="#1976d2" stroke-width="1"/>
                            <text x="110" y="180" font-size="9" font-family="monospace" fill="#666">chr1 HAVANA exon 13221 14409 . + . transcript_id "ENST00000456328"; exon_number "3";</text>

                            <!-- Processing arrow -->
                            <path d="M 520 140 L 580 140" stroke="#666" stroke-width="3" marker-end="url(#process-arrow)"/>
                            <text x="550" y="130" text-anchor="middle" font-size="10" fill="#666">Parse & Group</text>

                            <!-- TranscriptModel -->
                            <rect x="600" y="80" width="300" height="120" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                            <text x="750" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">TranscriptModel: ENST00000456328</text>

                            <text x="620" y="125" font-size="9" fill="#666">trid: "ENST00000456328"</text>
                            <text x="620" y="140" font-size="9" fill="#666">geneid: "ENSG00000223972"</text>
                            <text x="620" y="155" font-size="9" fill="#666">genename: "DDX11L1"</text>
                            <text x="620" y="170" font-size="9" fill="#666">chromstrand: "1+"</text>
                            <text x="620" y="185" font-size="9" fill="#666">list_features: [Exon1, Exon2, Exon3]</text>

                            <!-- Feature objects -->
                            <text x="1000" y="70" font-size="12" font-weight="bold" fill="#333">Feature Objects Created</text>

                            <!-- Exon 1 -->
                            <rect x="950" y="80" width="150" height="30" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="1025" y="100" text-anchor="middle" font-size="9" fill="#155724">Exon 1: 11869-12227</text>

                            <!-- Exon 2 -->
                            <rect x="950" y="120" width="150" height="30" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="1025" y="140" text-anchor="middle" font-size="9" fill="#155724">Exon 2: 12613-12721</text>

                            <!-- Exon 3 -->
                            <rect x="950" y="160" width="150" height="30" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="1025" y="180" text-anchor="middle" font-size="9" fill="#155724">Exon 3: 13221-14409</text>

                            <!-- Grouping process -->
                            <text x="700" y="240" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Grouping by Chromosome and Strand</text>

                            <!-- Chromosome groups -->
                            <rect x="200" y="260" width="200" height="80" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                            <text x="300" y="280" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">Chromosome 1+</text>
                            <text x="210" y="300" font-size="9" fill="#666">• ENST00000456328</text>
                            <text x="210" y="315" font-size="9" fill="#666">• ENST00000515242</text>
                            <text x="210" y="330" font-size="9" fill="#666">• ... (thousands more)</text>

                            <rect x="450" y="260" width="200" height="80" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="5"/>
                            <text x="550" y="280" text-anchor="middle" font-size="11" font-weight="bold" fill="#9c27b0">Chromosome 1-</text>
                            <text x="460" y="300" font-size="9" fill="#666">• ENST00000423562</text>
                            <text x="460" y="315" font-size="9" fill="#666">• ENST00000438504</text>
                            <text x="460" y="330" font-size="9" fill="#666">• ... (thousands more)</text>

                            <rect x="700" y="260" width="200" height="80" fill="#ffebee" stroke="#e91e63" stroke-width="2" rx="5"/>
                            <text x="800" y="280" text-anchor="middle" font-size="11" font-weight="bold" fill="#e91e63">Chromosome 2+</text>
                            <text x="710" y="300" font-size="9" fill="#666">• ENST00000327044</text>
                            <text x="710" y="315" font-size="9" fill="#666">• ENST00000445118</text>
                            <text x="710" y="330" font-size="9" fill="#666">• ... (thousands more)</text>

                            <text x="1000" y="280" font-size="11" fill="#666">... and so on for</text>
                            <text x="1000" y="300" font-size="11" fill="#666">all chromosomes</text>
                            <text x="1000" y="320" font-size="11" fill="#666">and both strands</text>

                            <!-- Arrow marker -->
                            <defs>
                                <marker id="process-arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                    <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                                </marker>
                            </defs>
                        </svg>
                    </div>

                    <h3>3.3 Data Structure Organization</h3>
                    <div class="code-box">
# Final data structure after GTF parsing
annotations_by_chrm_strand = {
    "1+": OrderedDict({
        "ENST00000456328": TranscriptModel(...),
        "ENST00000515242": TranscriptModel(...),
        # ... thousands more transcripts
    }),
    "1-": OrderedDict({
        "ENST00000423562": TranscriptModel(...),
        # ... thousands more transcripts
    }),
    "2+": OrderedDict({...}),
    "2-": OrderedDict({...}),
    # ... all chromosomes and strands
}

# Each TranscriptModel contains:
transcript_model.list_features = [
    Feature(start=11869, end=12227, kind=ord("e"), exin_no=1),  # Exon 1
    Feature(start=12613, end=12721, kind=ord("e"), exin_no=2),  # Exon 2
    Feature(start=13221, end=14409, kind=ord("e"), exin_no=3),  # Exon 3
    # Note: Introns will be added in the next step
]
                    </div>

                    <div class="success-box">
                        <strong>✅ Key Benefits of This Organization:</strong>
                        <ul>
                            <li><strong>Strand Separation:</strong> Forward and reverse strand features are indexed separately</li>
                            <li><strong>Chromosome Grouping:</strong> Enables efficient per-chromosome processing</li>
                            <li><strong>Ordered Storage:</strong> OrderedDict preserves transcript order for consistent processing</li>
                            <li><strong>Memory Efficiency:</strong> Each transcript model stores only its own features</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Section 4: Intron Generation -->
            <div class="section" id="intron-generation">
                <h2>4. Intron Generation</h2>

                <div class="pipeline-step">
                    <div class="step-number">3</div>
                    <h3 style="margin-top: 0; padding-left: 60px;">Automatic Intron Creation from Exon Boundaries</h3>

                    <h3>4.1 Intron Generation Logic</h3>
                    <div class="info-box">
                        <strong>🧬 Key Insight:</strong> Introns are not explicitly defined in GTF files. Velocyto automatically generates intron features from the gaps between consecutive exons in each transcript.
                    </div>

                    <div class="svg-container">
                        <svg width="1400" height="500" viewBox="0 0 1400 500">
                            <!-- Background -->
                            <rect width="1400" height="500" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                            <!-- Title -->
                            <text x="700" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Intron Generation Process</text>

                            <!-- Before: Only Exons -->
                            <text x="200" y="70" font-size="12" font-weight="bold" fill="#333">Before: Only Exons from GTF</text>

                            <!-- Genomic coordinate line -->
                            <line x1="100" y1="100" x2="600" y2="100" stroke="#333" stroke-width="2"/>
                            <text x="350" y="90" text-anchor="middle" font-size="10" fill="#666">Genomic Coordinate →</text>

                            <!-- Exons only -->
                            <rect x="150" y="90" width="60" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="180" y="105" text-anchor="middle" font-size="8" fill="#155724">Exon 1</text>
                            <text x="180" y="125" text-anchor="middle" font-size="7" fill="#666">11869-12227</text>

                            <rect x="280" y="90" width="60" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="310" y="105" text-anchor="middle" font-size="8" fill="#155724">Exon 2</text>
                            <text x="310" y="125" text-anchor="middle" font-size="7" fill="#666">12613-12721</text>

                            <rect x="410" y="90" width="60" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="440" y="105" text-anchor="middle" font-size="8" fill="#155724">Exon 3</text>
                            <text x="440" y="125" text-anchor="middle" font-size="7" fill="#666">13221-14409</text>

                            <!-- Gap indicators -->
                            <path d="M 210 100 L 280 100" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                            <text x="245" y="95" text-anchor="middle" font-size="8" fill="#e74c3c">Gap 1</text>

                            <path d="M 340 100 L 410 100" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                            <text x="375" y="95" text-anchor="middle" font-size="8" fill="#e74c3c">Gap 2</text>

                            <!-- After: Exons + Introns -->
                            <text x="200" y="180" font-size="12" font-weight="bold" fill="#333">After: Exons + Generated Introns</text>

                            <!-- Genomic coordinate line -->
                            <line x1="100" y1="210" x2="600" y2="210" stroke="#333" stroke-width="2"/>

                            <!-- Exons -->
                            <rect x="150" y="200" width="60" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="180" y="215" text-anchor="middle" font-size="8" fill="#155724">Exon 1</text>

                            <rect x="280" y="200" width="60" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="310" y="215" text-anchor="middle" font-size="8" fill="#155724">Exon 2</text>

                            <rect x="410" y="200" width="60" height="20" fill="#d4edda" stroke="#28a745" stroke-width="1"/>
                            <text x="440" y="215" text-anchor="middle" font-size="8" fill="#155724">Exon 3</text>

                            <!-- Generated introns -->
                            <rect x="220" y="225" width="50" height="15" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                            <text x="245" y="237" text-anchor="middle" font-size="7" fill="#856404">Intron 1</text>
                            <text x="245" y="250" text-anchor="middle" font-size="6" fill="#666">12228-12612</text>

                            <rect x="350" y="225" width="50" height="15" fill="#fff3cd" stroke="#ffc107" stroke-width="1"/>
                            <text x="375" y="237" text-anchor="middle" font-size="7" fill="#856404">Intron 2</text>
                            <text x="375" y="250" text-anchor="middle" font-size="6" fill="#666">12722-13220</text>

                            <!-- Algorithm explanation -->
                            <text x="800" y="70" font-size="12" font-weight="bold" fill="#333">append_exon() Algorithm</text>
                            <rect x="750" y="80" width="500" height="180" fill="white" stroke="#ccc" stroke-width="1"/>

                            <text x="770" y="105" font-size="10" fill="#666" font-weight="bold">Step 1: Add First Exon</text>
                            <text x="770" y="125" font-size="9" fill="#666">list_features = [Exon1]</text>

                            <text x="770" y="150" font-size="10" fill="#666" font-weight="bold">Step 2: Add Second Exon</text>
                            <text x="770" y="170" font-size="9" fill="#666">• Calculate intron: (Exon1.end + 1) to (Exon2.start - 1)</text>
                            <text x="770" y="185" font-size="9" fill="#666">• Intron1: 12228 to 12612</text>
                            <text x="770" y="200" font-size="9" fill="#666">• list_features = [Exon1, Intron1, Exon2]</text>

                            <text x="770" y="225" font-size="10" fill="#666" font-weight="bold">Step 3: Add Third Exon</text>
                            <text x="770" y="245" font-size="9" fill="#666">• Calculate intron: (Exon2.end + 1) to (Exon3.start - 1)</text>
                            <text x="770" y="260" font-size="9" fill="#666">• list_features = [Exon1, Intron1, Exon2, Intron2, Exon3]</text>

                            <!-- Strand considerations -->
                            <text x="700" y="300" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Strand-Specific Intron Numbering</text>

                            <!-- Forward strand -->
                            <rect x="200" y="320" width="400" height="60" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                            <text x="400" y="340" text-anchor="middle" font-size="11" font-weight="bold" fill="#4caf50">Forward Strand (+)</text>
                            <text x="220" y="360" font-size="9" fill="#666">Intron numbers follow exon order: Intron1, Intron2, Intron3...</text>
                            <text x="220" y="375" font-size="9" fill="#666">intron_number = previous_exon.exin_no</text>

                            <!-- Reverse strand -->
                            <rect x="650" y="320" width="400" height="60" fill="#ffebee" stroke="#e91e63" stroke-width="2" rx="5"/>
                            <text x="850" y="340" text-anchor="middle" font-size="11" font-weight="bold" fill="#e91e63">Reverse Strand (-)</text>
                            <text x="670" y="360" font-size="9" fill="#666">Intron numbers are decremented: Intron3, Intron2, Intron1...</text>
                            <text x="670" y="375" font-size="9" fill="#666">intron_number = previous_exon.exin_no - 1</text>

                            <!-- Implementation code -->
                            <text x="700" y="420" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Implementation Details</text>
                            <rect x="200" y="430" width="1000" height="50" fill="#f8f9fa" stroke="#ccc" stroke-width="1"/>
                            <text x="220" y="450" font-size="9" font-family="monospace" fill="#666">if self.chromstrand[-1] == "+":</text>
                            <text x="240" y="465" font-size="9" font-family="monospace" fill="#666">intron_number = self.list_features[-1].exin_no  # Forward strand</text>
                            <text x="220" y="480" font-size="9" font-family="monospace" fill="#666">else:</text>
                            <text x="240" y="495" font-size="9" font-family="monospace" fill="#666">intron_number = self.list_features[-1].exin_no - 1  # Reverse strand</text>
                        </svg>
                    </div>

                    <h3>4.2 Implementation Code</h3>
                    <div class="code-box">
# append_exon() method in TranscriptModel class
def append_exon(self, exon_feature: Feature) -> None:
    """Append an exon and create an intron when needed"""

    exon_feature.transcript_model = self  # Link back to transcript

    if len(self.list_features) == 0:
        # First exon - just add it
        self.list_features.append(exon_feature)
    else:
        # Not the first exon - need to create intron first

        # Determine intron number based on strand
        if self.chromstrand[-1] == "+":
            # Forward strand: intron number = previous exon number
            intron_number = self.list_features[-1].exin_no
        else:
            # Reverse strand: intron number = previous exon number - 1
            intron_number = self.list_features[-1].exin_no - 1

        # Create intron feature
        intron_start = self.list_features[-1].end + 1      # Previous exon end + 1
        intron_end = exon_feature.start - 1               # Current exon start - 1

        intron_feature = Feature(
            start=intron_start,
            end=intron_end,
            kind=ord("i"),                    # 'i' for intron
            exin_no=intron_number,
            transcript_model=self
        )

        # Add intron, then exon
        self.list_features.append(intron_feature)
        self.list_features.append(exon_feature)
                    </div>

                    <h3>4.3 Quality Control: Long Intron Handling</h3>
                    <div class="warning-box">
                        <strong>⚠️ Long Intron Issue:</strong> Some introns can be extremely long (>1Mbp), potentially masking entire genes. Velocyto includes logic to handle these corner cases.
                    </div>

                    <div class="code-box">
# Long intron handling in chop_if_long_intron()
def chop_if_long_intron(self):
    """Handle extremely long introns that might mask other genes"""

    LONGEST_INTRON_ALLOWED = 1000000  # 1 Mbp threshold

    for i, feature in enumerate(self.list_features):
        if (feature.kind == ord("i") and
            (feature.end - feature.start) > LONGEST_INTRON_ALLOWED):

            logging.debug(f"Chopping long intron in {self.trid}: "
                         f"{feature.start}-{feature.end} "
                         f"({feature.end - feature.start} bp)")

            # Split or remove the problematic intron
            # Implementation details depend on specific requirements
                    </div>

                    <div class="success-box">
                        <strong>✅ Intron Generation Benefits:</strong>
                        <ul>
                            <li><strong>Automatic Creation:</strong> No manual intron annotation required</li>
                            <li><strong>Strand Awareness:</strong> Proper numbering for both forward and reverse strands</li>
                            <li><strong>Quality Control:</strong> Handles edge cases like extremely long introns</li>
                            <li><strong>Validation Ready:</strong> Introns created with is_validated=False, ready for markup</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Section 5: Feature Indexing -->
            <div class="section" id="feature-indexing">
                <h2>5. Feature Index Building</h2>

                <div class="pipeline-step">
                    <div class="step-number">4</div>
                    <h3 style="margin-top: 0; padding-left: 60px;">Creating Efficient Search Structures</h3>

                    <h3>5.1 Index Construction Process</h3>
                    <div class="info-box">
                        <strong>🔍 Purpose:</strong> Transform the hierarchical transcript structure into flat, sorted lists of features for efficient genomic interval searches during read processing.
                    </div>

                    <div class="svg-container">
                        <svg width="1400" height="400" viewBox="0 0 1400 400">
                            <!-- Background -->
                            <rect width="1400" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                            <!-- Title -->
                            <text x="700" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Feature Index Construction</text>

                            <!-- Input: Transcript Models -->
                            <text x="200" y="70" font-size="12" font-weight="bold" fill="#333">Input: TranscriptModels by Chromosome</text>

                            <!-- Transcript models -->
                            <rect x="100" y="80" width="200" height="100" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                            <text x="200" y="100" text-anchor="middle" font-size="11" font-weight="bold" fill="#4caf50">Chromosome 1+</text>
                            <text x="110" y="120" font-size="9" fill="#666">TranscriptModel A:</text>
                            <text x="120" y="135" font-size="8" fill="#666">[Exon1, Intron1, Exon2]</text>
                            <text x="110" y="150" font-size="9" fill="#666">TranscriptModel B:</text>
                            <text x="120" y="165" font-size="8" fill="#666">[Exon1, Intron1, Exon2, Intron2, Exon3]</text>

                            <!-- Flattening process -->
                            <path d="M 320 130 L 380 130" stroke="#666" stroke-width="3" marker-end="url(#flatten-arrow)"/>
                            <text x="350" y="120" text-anchor="middle" font-size="10" fill="#666">Flatten & Collect</text>

                            <!-- Flattened features -->
                            <rect x="400" y="80" width="200" height="100" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
                            <text x="500" y="100" text-anchor="middle" font-size="11" font-weight="bold" fill="#f57c00">All Features</text>
                            <text x="410" y="120" font-size="8" fill="#666">ExonA1, IntronA1, ExonA2,</text>
                            <text x="410" y="135" font-size="8" fill="#666">ExonB1, IntronB1, ExonB2,</text>
                            <text x="410" y="150" font-size="8" fill="#666">IntronB2, ExonB3, ...</text>
                            <text x="410" y="165" font-size="8" fill="#666">(Unsorted)</text>

                            <!-- Sorting process -->
                            <path d="M 620 130 L 680 130" stroke="#666" stroke-width="3" marker-end="url(#flatten-arrow)"/>
                            <text x="650" y="120" text-anchor="middle" font-size="10" fill="#666">Sort by Position</text>

                            <!-- Sorted FeatureIndex -->
                            <rect x="700" y="80" width="200" height="100" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="5"/>
                            <text x="800" y="100" text-anchor="middle" font-size="11" font-weight="bold" fill="#9c27b0">FeatureIndex</text>
                            <text x="710" y="120" font-size="8" fill="#666">Feature1 (pos: 1000)</text>
                            <text x="710" y="135" font-size="8" fill="#666">Feature2 (pos: 1500)</text>
                            <text x="710" y="150" font-size="8" fill="#666">Feature3 (pos: 2000)</text>
                            <text x="710" y="165" font-size="8" fill="#666">(Sorted by start position)</text>

                            <!-- Implementation code -->
                            <text x="700" y="220" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Implementation Code</text>
                            <rect x="100" y="230" width="1200" height="120" fill="white" stroke="#ccc" stroke-width="1"/>

                            <text x="120" y="250" font-size="10" font-family="monospace" fill="#666"># Build feature indexes for each chromosome+strand</text>
                            <text x="120" y="270" font-size="10" font-family="monospace" fill="#666">self.feature_indexes = defaultdict(FeatureIndex)</text>
                            <text x="120" y="290" font-size="10" font-family="monospace" fill="#666">for chromstrand_key, transcript_models in self.annotations_by_chrm_strand.items():</text>
                            <text x="140" y="310" font-size="10" font-family="monospace" fill="#666"># Flatten all features from all transcripts</text>
                            <text x="140" y="330" font-size="10" font-family="monospace" fill="#666">all_features = list(chain.from_iterable(transcript_models.values()))</text>
                            <text x="140" y="350" font-size="10" font-family="monospace" fill="#666"># Create sorted FeatureIndex</text>
                            <text x="140" y="370" font-size="10" font-family="monospace" fill="#666">self.feature_indexes[chromstrand_key] = FeatureIndex(sorted(all_features))</text>

                            <!-- Arrow marker -->
                            <defs>
                                <marker id="flatten-arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                    <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                                </marker>
                            </defs>
                        </svg>
                    </div>

                    <h3>5.2 FeatureIndex Class Structure</h3>
                    <div class="data-structure">
                        <div class="structure-title">FeatureIndex Class</div>
                        <div class="code-box">
class FeatureIndex:
    """Search help class used to find intervals that a read is spanning"""

    def __init__(self, ivls: List[Feature] = []):
        self.ivls = ivls                    # List of Feature objects
        self.ivls.sort()                    # Sort by genomic position
        self.iidx = 0                       # Current search position
        self.maxiidx = len(ivls) - 1        # Maximum valid index

    # Key methods for efficient search:
    def find_overlapping_ivls(self, read) -> Dict[TranscriptModel, List[SegmentMatch]]
    def mark_overlapping_ivls(self, read) -> None  # For intron validation
    def has_ivls_enclosing(self, read) -> bool     # For masking checks
    def reset(self) -> None                        # Reset search position
                        </div>
                    </div>

                    <h3>5.3 Sorting Strategy</h3>
                    <div class="code-box">
# Feature sorting logic (from Feature class)
def __lt__(self, other):
    """Sort features by genomic position"""
    if self.start == other.start:
        return self.end < other.end    # Secondary sort by end position
    return self.start < other.start    # Primary sort by start position

# This ensures features are ordered left-to-right on the genome:
# Feature1: start=1000, end=1500
# Feature2: start=1200, end=1800
# Feature3: start=2000, end=2500
# etc.
                    </div>

                    <h3>5.4 Memory and Performance Impact</h3>
                    <div style="display: flex; gap: 2rem; margin: 1rem 0;">
                        <div style="flex: 1; background: #e3f2fd; padding: 1rem; border-radius: 8px;">
                            <strong>Memory Usage</strong><br>
                            <small>Human genome: ~3-6 GB<br>
                            Mouse genome: ~2-4 GB<br>
                            Per chromosome: ~200-500 MB</small>
                        </div>
                        <div style="flex: 1; background: #fff3e0; padding: 1rem; border-radius: 8px;">
                            <strong>Search Performance</strong><br>
                            <small>O(log n) feature lookup<br>
                            State preservation with iidx<br>
                            Early termination optimization</small>
                        </div>
                        <div style="flex: 1; background: #e8f5e8; padding: 1rem; border-radius: 8px;">
                            <strong>Build Time</strong><br>
                            <small>Human genome: 2-5 minutes<br>
                            One-time cost per analysis<br>
                            Parallel by chromosome</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section 6: Validation Markup -->
            <div class="section" id="validation-markup">
                <h2>6. Intron Validation Markup</h2>

                <div class="pipeline-step">
                    <div class="step-number">5</div>
                    <h3 style="margin-top: 0; padding-left: 60px;">Marking Validated Introns</h3>

                    <h3>6.1 Validation Process</h3>
                    <div class="warning-box">
                        <strong>⚠️ Critical Step:</strong> Not all introns in the annotation are real. Validation ensures only introns with evidence of active splicing (spanning reads) are used for unspliced read classification.
                    </div>

                    <div class="code-box">
# Intron validation markup process
def mark_up_introns(self, bamfile, multimap=False):
    """Mark introns that have reads across exon-intron junctions"""

    # Build feature indexes if not already done
    if not hasattr(self, 'feature_indexes'):
        self.feature_indexes = defaultdict(FeatureIndex)
        for chromstrand_key, transcript_models in self.annotations_by_chrm_strand.items():
            all_features = list(chain.from_iterable(transcript_models.values()))
            self.feature_indexes[chromstrand_key] = FeatureIndex(sorted(all_features))

    # Process BAM file to find spanning reads
    for read in self.iter_alignments(bamfile, unique=not multimap):
        if read is None or read.is_spliced:
            continue  # Skip spliced reads for this step

        # Get appropriate feature index for this chromosome/strand
        chromstrand = read.chrom + read.strand
        if chromstrand in self.feature_indexes:
            feature_index = self.feature_indexes[chromstrand]

            # Mark overlapping introns as validated
            feature_index.mark_overlapping_ivls(read)

    # Log validation statistics
    self.log_validation_stats()
                    </div>

                    <h3>6.2 Validation Statistics</h3>
                    <div class="success-box">
                        <strong>✅ Typical Validation Results:</strong>
                        <ul>
                            <li><strong>Total Introns:</strong> ~200,000 per chromosome</li>
                            <li><strong>Validated Introns:</strong> ~60-80% (120,000-160,000)</li>
                            <li><strong>Unique Validated:</strong> ~50,000-80,000 unique genomic intervals</li>
                            <li><strong>Quality Indicator:</strong> Higher validation rates indicate better data quality</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Section 7: Final Data Structures -->
            <div class="section" id="data-structures">
                <h2>8. Final Data Structures</h2>

                <div class="pipeline-step">
                    <div class="step-number">6</div>
                    <h3 style="margin-top: 0; padding-left: 60px;">Ready for Read Processing</h3>

                    <h3>8.1 Complete Data Structure Hierarchy</h3>
                    <div class="svg-container">
                        <svg width="1400" height="300" viewBox="0 0 1400 300">
                            <!-- Background -->
                            <rect width="1400" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>

                            <!-- Title -->
                            <text x="700" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Final Data Structure Organization</text>

                            <!-- ExInCounter -->
                            <rect x="600" y="60" width="200" height="40" fill="#2c3e50" stroke="#2c3e50" stroke-width="2" rx="5"/>
                            <text x="700" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="white">ExInCounter</text>

                            <!-- Two main data structures -->
                            <rect x="200" y="140" width="300" height="120" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                            <text x="350" y="165" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">annotations_by_chrm_strand</text>
                            <text x="220" y="185" font-size="9" fill="#666">Dict[str, OrderedDict[str, TranscriptModel]]</text>
                            <text x="220" y="205" font-size="9" fill="#666">"1+": {"ENST001": TranscriptModel, ...}</text>
                            <text x="220" y="220" font-size="9" fill="#666">"1-": {"ENST002": TranscriptModel, ...}</text>
                            <text x="220" y="235" font-size="9" fill="#666">Used for: Gene assignment, metadata</text>

                            <rect x="600" y="140" width="300" height="120" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="5"/>
                            <text x="750" y="165" text-anchor="middle" font-size="12" font-weight="bold" fill="#9c27b0">feature_indexes</text>
                            <text x="620" y="185" font-size="9" fill="#666">Dict[str, FeatureIndex]</text>
                            <text x="620" y="205" font-size="9" fill="#666">"1+": FeatureIndex([Feature1, Feature2, ...])</text>
                            <text x="620" y="220" font-size="9" fill="#666">"1-": FeatureIndex([Feature3, Feature4, ...])</text>
                            <text x="620" y="235" font-size="9" fill="#666">Used for: Fast overlap detection</text>

                            <!-- Connections -->
                            <path d="M 700 100 L 350 140" stroke="#666" stroke-width="2" marker-end="url(#final-arrow)"/>
                            <path d="M 700 100 L 750 140" stroke="#666" stroke-width="2" marker-end="url(#final-arrow)"/>

                            <!-- Arrow marker -->
                            <defs>
                                <marker id="final-arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                    <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                                </marker>
                            </defs>
                        </svg>
                    </div>

                    <h3>8.2 Ready for Production Use</h3>
                    <div class="success-box">
                        <strong>✅ Processing Complete:</strong> The GTF file has been transformed into efficient, searchable data structures ready for high-throughput read processing:
                        <ul>
                            <li><strong>Sorted Features:</strong> O(log n) genomic interval searches</li>
                            <li><strong>Validated Introns:</strong> Quality-controlled intron annotations</li>
                            <li><strong>Memory Efficient:</strong> Optimized for large-scale processing</li>
                            <li><strong>Strand Aware:</strong> Separate indexes for forward and reverse strands</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Summary -->
            <div class="section">
                <h2>Summary</h2>

                <div class="highlight" style="display: block; text-align: center; padding: 1rem; margin: 2rem 0; font-size: 1.1rem;">
                    🎯 <strong>Complete Transformation:</strong> From raw GTF annotations to production-ready feature indexes, velocyto's GTF processing pipeline creates the foundation for accurate and efficient RNA velocity analysis.
                </div>

                <h3>Key Processing Steps</h3>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem; margin: 1rem 0;">
                    <div style="background: #e3f2fd; padding: 1rem; border-radius: 8px; text-align: center;">
                        <strong>1-2. Parse & Model</strong><br>
                        <small>GTF → TranscriptModels</small>
                    </div>
                    <div style="background: #fff3e0; padding: 1rem; border-radius: 8px; text-align: center;">
                        <strong>3. Generate</strong><br>
                        <small>Automatic intron creation</small>
                    </div>
                    <div style="background: #e8f5e8; padding: 1rem; border-radius: 8px; text-align: center;">
                        <strong>4-5. Index & Validate</strong><br>
                        <small>Efficient search structures</small>
                    </div>
                </div>

                <h3>Technical Excellence</h3>
                <div class="info-box">
                    <strong>🏗️ Architecture Highlights:</strong>
                    <ul>
                        <li><strong>Automatic Intron Generation:</strong> No manual annotation required</li>
                        <li><strong>Quality Control:</strong> Missing exon number detection and correction</li>
                        <li><strong>Efficient Indexing:</strong> Sorted features enable O(log n) searches</li>
                        <li><strong>Strand Awareness:</strong> Proper handling of forward and reverse transcripts</li>
                        <li><strong>Memory Optimization:</strong> Balanced memory usage vs. search performance</li>
                        <li><strong>Validation Ready:</strong> Intron validation markup for quality control</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
