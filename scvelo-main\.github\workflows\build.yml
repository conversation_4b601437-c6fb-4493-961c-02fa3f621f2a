name: Check Build

on:
    push:
        branches: [main]
    pull_request:
        branches: [main]

jobs:
    package:
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v2
            - name: Set up Python 3.10
              uses: actions/setup-python@v2
              with:
                  python-version: "3.10"
            - name: Install build dependencies
              run: python -m pip install --upgrade pip wheel twine build
            - name: Build package
              run: python -m build
            - name: Check package
              run: twine check --strict dist/*.whl
