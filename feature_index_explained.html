<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FeatureIndex Class Explained - Velocyto Tutorial</title>
    
    <!-- MathJax 3 for LaTeX rendering -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
            border-radius: 15px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #4a6491 100%);
            color: white;
            padding: 2.5rem;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.8rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            margin: 1rem 0 0 0;
            font-size: 1.3rem;
            opacity: 0.9;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .content {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border-left: 5px solid #3498db;
            background: #f8f9fa;
            border-radius: 0 10px 10px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        }
        
        .section h2 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.8rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        
        .section h3 {
            color: #34495e;
            margin-top: 2rem;
            font-size: 1.4rem;
        }
        
        .math-box {
            background: #fff;
            border: 2px solid #3498db;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            box-shadow: 0 4px 8px rgba(52, 152, 219, 0.1);
        }
        
        .code-box {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 0.2rem 0.5rem;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
            font-size: 1.1rem;
        }
        
        .workflow-step {
            display: flex;
            align-items: flex-start;
            margin: 1.5rem 0;
            padding: 1.2rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
            border-left: 4px solid #3498db;
        }
        
        .svg-container {
            text-align: center;
            margin: 2.5rem 0;
            padding: 1.5rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        
        .info-box {
            background: #d1ecf1;
            border: 2px solid #17a2b8;
            border-radius: 10px;
            padding: 1.2rem;
            margin: 1.5rem 0;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 1.2rem;
            margin: 1.5rem 0;
        }
        
        .success-box {
            background: #d4edda;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 1.2rem;
            margin: 1.5rem 0;
        }
        
        .toc {
            background: #34495e;
            color: white;
            padding: 1.8rem;
            border-radius: 10px;
            margin-bottom: 2.5rem;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .toc h3 {
            margin-top: 0;
            color: #ecf0f1;
            border-bottom: 1px solid #ecf0f1;
            padding-bottom: 0.5rem;
        }
        
        .toc ul {
            list-style: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 0.7rem 0;
            padding-left: 1.2rem;
            position: relative;
        }
        
        .toc li:before {
            content: "▶";
            position: absolute;
            left: 0;
            color: #3498db;
        }
        
        .toc a {
            color: #3498db;
            text-decoration: none;
            transition: color 0.3s;
            font-weight: 500;
        }
        
        .toc a:hover {
            color: #5dade2;
            text-decoration: underline;
        }
        
        .gene-model {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
            overflow-x: auto;
        }
        
        .decision-tree {
            background: white;
            border: 2px solid #3498db;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        }
        
        .read-type {
            padding: 0.6rem 1.2rem;
            border-radius: 25px;
            font-weight: bold;
            margin: 0.3rem;
            display: inline-block;
            text-align: center;
            min-width: 120px;
        }
        
        .spliced {
            background: #d4edda;
            color: #155724;
            border: 2px solid #28a745;
        }
        
        .unspliced {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffc107;
        }
        
        .ambiguous {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #dc3545;
        }
        
        .feature-type {
            padding: 0.4rem 0.8rem;
            border-radius: 5px;
            font-weight: bold;
            margin: 0.2rem;
            display: inline-block;
        }
        
        .exon {
            background: #28a745;
            color: white;
        }
        
        .intron {
            background: #6c757d;
            color: white;
        }
        
        .validated {
            background: #17a2b8;
            color: white;
        }
        
        .conclusion {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            margin: 2rem 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .conclusion h2 {
            margin-top: 0;
            font-size: 2rem;
        }
        
        .key-point {
            background: rgba(255,255,255,0.2);
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .footer {
            text-align: center;
            padding: 2rem;
            background: #2c3e50;
            color: #ecf0f1;
            margin-top: 2rem;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }
            
            .header {
                padding: 1.5rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧬 FeatureIndex Class Explained</h1>
            <p>A Deep Dive into Velocyto's Genomic Feature Search Engine</p>
        </div>
        
        <div class="content">
            <!-- Table of Contents -->
            <div class="toc">
                <h3>📋 Table of Contents</h3>
                <ul>
                    <li><a href="#introduction">1. Introduction and Overview</a></li>
                    <li><a href="#class-structure">2. FeatureIndex Class Structure</a></li>
                    <li><a href="#initialization">3. Initialization and Sorting</a></li>
                    <li><a href="#overlap-detection">4. Overlap Detection Methods</a></li>
                    <li><a href="#intron-validation">5. Intron Validation Process</a></li>
                    <li><a href="#mapping-records">6. Mapping Records Generation</a></li>
                    <li><a href="#performance">7. Performance Optimization</a></li>
                    <li><a href="#conclusion">8. Conclusion</a></li>
                </ul>
            </div>

            <!-- Section 1: Introduction -->
            <div class="section" id="introduction">
                <h2>1. Introduction and Overview</h2>
                
                <p>The <span class="highlight">FeatureIndex</span> class is a core component of the velocyto pipeline, responsible for efficiently finding overlaps between sequencing reads and genomic features (exons, introns, etc.). It serves as a search engine that enables rapid identification of which features a read maps to.</p>
                
                <div class="workflow-step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>Efficient Search:</strong> Provides fast lookup of genomic features that overlap with read segments
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>Intron Validation:</strong> Identifies and validates introns by detecting reads that span exon-intron boundaries
                    </div>
                </div>
                
                <div class="workflow-step">
                    <div class="step-number">3</div>
                    <div>
                        <strong>Mapping Records:</strong> Generates detailed records of read-to-feature mappings for downstream classification
                    </div>
                </div>
                
                <h3>1.1 Why FeatureIndex is Important</h3>
                <p>In RNA velocity analysis, accurate read classification depends on precisely mapping reads to genomic features:</p>
                
                <div class="math-box">
                    <p><strong>Mapping Challenge:</strong></p>
                    <p>Given a read with segments $R = \{s_1, s_2, ..., s_n\}$ and a genome with features $F = \{f_1, f_2, ..., f_m\}$, we need to find all overlaps:</p>
                    
                    $$\text{overlaps}(R, F) = \{(s_i, f_j) : \text{overlap}(s_i, f_j) = \text{True}\}$$
                    
                    <p>Where:</p>
                    <ul>
                        <li>$s_i = (start_i, end_i)$: Genomic coordinates of read segment</li>
                        <li>$f_j = (start_j, end_j)$: Genomic coordinates of feature</li>
                        <li>$\text{overlap}(s_i, f_j) = (start_i \leq end_j) \land (end_i \geq start_j)$</li>
                    </ul>
                </div>
                
                <div class="info-box">
                    <strong>🎯 Key Insight:</strong> Without an efficient indexing mechanism like FeatureIndex, the overlap detection process would require checking every read against every feature, resulting in O(n×m) complexity that would be computationally prohibitive for large datasets.
                </div>
            </div>

            <!-- Section 2: Class Structure -->
            <div class="section" id="class-structure">
                <h2>2. FeatureIndex Class Structure</h2>
                
                <p>The FeatureIndex class is designed for efficient genomic feature lookup. It maintains a sorted collection of features and provides methods for overlap detection and intron validation.</p>
                
                <h3>2.1 Class Attributes</h3>
                
                <div class="code-box">
// FeatureIndex class definition (simplified)
class FeatureIndex:
    """Search help class used to find the intervals that a read is spanning"""
    
    def __init__(self, ivls: List[vcy.Feature]=[]) -> None:
        self.ivls = ivls              // List of Feature objects
        self.ivls.sort()              // Features sorted by genomic position
        self.iidx = 0                 // Current feature index during iteration
        self.maxiidx = len(ivls) - 1 // Maximum valid index
        
    @property
    def last_interval_not_reached(self) -> bool:
        return self.iidx < self.maxiidx
        
    def reset(self) -> None:
        """Reset current feature index to beginning"""
        self.iidx = 0
                </div>
                
                <h3>2.2 Feature Object Structure</h3>
                
                <div class="gene-model">
                    <strong>Feature Object Attributes:</strong><br>
                    start: int          // Start position (1-based)<br>
                    end: int           // End position<br>
                    kind: int          // Feature type (101='e', 105='i', 109='m')<br>
                    exin_no: int       // Exon/intron number<br>
                    is_validated: bool // Whether intron is validated by spanning reads<br>
                    transcript_model: TranscriptModel // Parent transcript model
                </div>
                
                <h3>2.3 Class Diagram</h3>
                
                <div class="svg-container">
                    <svg width="1200" height="700" viewBox="0 0 1200 700">
                        <!-- Background -->
                        <rect width="1200" height="700" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
                        
                        <!-- Title -->
                        <text x="600" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">FeatureIndex Class Structure</text>
                        
                        <!-- FeatureIndex Class -->
                        <rect x="100" y="80" width="300" height="200" fill="white" stroke="#3498db" stroke-width="2" rx="10"/>
