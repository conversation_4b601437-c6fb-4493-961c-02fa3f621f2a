<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Understanding is_spliced and ref_skipped in Velocyto Read Class</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        header {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            margin: 0;
            font-size: 2.5rem;
        }
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-top: 0.5rem;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
            }
        }
        .card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }
        .card.spliced {
            border-left: 5px solid #28a745;
        }
        .card.ref-skipped {
            border-left: 5px solid #ffc107;
        }
        .card.code {
            border-left: 5px solid #6f42c1;
        }
        .card h2 {
            color: #2575fc;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 0.5rem;
            margin-top: 0;
        }
        .comparison {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        }
        .comparison h2 {
            color: #6a11cb;
            text-align: center;
            margin-top: 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }
        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        th {
            background-color: #f1f3f5;
            font-weight: 600;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .visualization {
            text-align: center;
            margin: 2rem 0;
        }
        .math-section {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        }
        .math-section h2 {
            color: #e83e8c;
            margin-top: 0;
        }
        .conclusion {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            margin: 2rem 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .conclusion h2 {
            margin-top: 0;
        }
        .key-point {
            background: rgba(255,255,255,0.2);
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        pre {
            background: #f1f3f5;
            padding: 1rem;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            font-family: 'Courier New', monospace;
        }
        .highlight {
            background: #fff3cd;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-weight: bold;
        }
        .cigar-table {
            margin: 1rem auto;
            border-collapse: collapse;
            width: 80%;
        }
        .cigar-table th, .cigar-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .cigar-table th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <header>
        <h1>Understanding <code>is_spliced</code> and <code>ref_skipped</code></h1>
        <div class="subtitle">Explaining Velocyto's Read Class Properties for Splicing Detection</div>
    </header>

    <div class="container">
        <div class="card spliced">
            <h2><code>is_spliced</code> Property</h2>
            <p>A <span class="highlight">read-only property</span> that determines if a read represents a spliced transcript (mature mRNA).</p>
            
            <h3>Definition:</h3>
            <pre><code>@property
def is_spliced(self) -> bool:
    """
    Determines if a read is spliced based on reference skips.
    
    In BAM files, spliced alignments have CIGAR operations 
    with code 3 (BAM_CREF_SKIP), which represent intron skipping.
    """
    return self.ref_skipped</code></pre>
            
            <h3>Function:</h3>
            <ul>
                <li>Returns <code>True</code> if the read skips intronic regions</li>
                <li>Returns <code>False</code> if the read aligns continuously to the reference</li>
                <li>Directly indicates whether the RNA molecule is mature (spliced) or pre-mRNA (unspliced)</li>
            </ul>
        </div>
        
        <div class="card ref-skipped">
            <h2><code>ref_skipped</code> Attribute</h2>
            <p>A <span class="highlight">boolean flag</span> that indicates whether the read alignment contains reference skips (introns).</p>
            
            <h3>Definition:</h3>
            <pre><code>def __init__(self, ..., ref_skipped: bool) -> None:
    # ...
    self.ref_skipped = ref_skipped</code></pre>
            
            <h3>Function:</h3>
            <ul>
                <li>Set to <code>True</code> when CIGAR parsing detects operation code 3</li>
                <li>Set to <code>False</code> when no reference skips are present</li>
                <li>Acts as the underlying data for the <code>is_spliced</code> property</li>
            </ul>
        </div>
    </div>

    <div class="card code">
        <h2>Complete Read Class</h2>
        <pre><code>class Read:
    """Container for reads from sam alignment file"""
    __slots__ = ["bc", "umi", "chrom", "strand", "pos", 
                 "segments", "clip5", "clip3", "ref_skipped"]

    def __init__(self, bc: str, umi: str, chrom: str, strand: str, 
                 pos: int, segments: List, clip5: Any, clip3: Any, 
                 ref_skipped: bool) -> None:
        self.bc = bc          # Cell barcode
        self.umi = umi        # Unique molecular identifier
        self.chrom = chrom    # Chromosome
        self.strand = strand  # Strand (+ or -)
        self.pos = pos        # Position
        self.segments = segments  # Alignment segments
        self.clip5 = clip5    # 5' clipping
        self.clip3 = clip3    # 3' clipping
        self.ref_skipped = ref_skipped  # Intron skipping flag

    @property
    def is_spliced(self) -> bool:
        """Returns True if read skips intronic regions"""
        return self.ref_skipped</code></pre>
    </div>

    <div class="comparison">
        <h2>Relationship Between <code>is_spliced</code> and <code>ref_skipped</code></h2>
        <table>
            <thead>
                <tr>
                    <th>Aspect</th>
                    <th><code>is_spliced</code></th>
                    <th><code>ref_skipped</code></th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Type</strong></td>
                    <td>Property (computed)</td>
                    <td>Attribute (stored)</td>
                </tr>
                <tr>
                    <td><strong>Value Source</strong></td>
                    <td>Returns <code>self.ref_skipped</code></td>
                    <td>Set during CIGAR parsing</td>
                </tr>
                <tr>
                    <td><strong>Biological Meaning</strong></td>
                    <td>Indicates spliced mRNA</td>
                    <td>Indicates intron skipping in alignment</td>
                </tr>
                <tr>
                    <td><strong>When Set</strong></td>
                    <td>Always computed on access</td>
                    <td>During read processing</td>
                </tr>
                <tr>
                    <td><strong>Usage</strong></td>
                    <td>RNA velocity classification</td>
                    <td>BAM alignment interpretation</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="visualization">
        <h2>Visual Representation of Spliced vs Unspliced Reads</h2>
        <svg width="100%" height="600" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
            <!-- Background -->
            <rect width="100%" height="100%" fill="#f8f9fa"/>
            
            <!-- Title -->
            <text x="400" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#333">
                Spliced vs Unspliced Reads in RNA Sequencing
            </text>
            
            <!-- Gene structure -->
            <text x="50" y="80" font-size="16" font-weight="bold" fill="#495057">Gene Structure:</text>
            
            <!-- Exon 1 -->
            <rect x="150" y="70" width="150" height="40" fill="#28a745" rx="5"/>
            <text x="225" y="95" text-anchor="middle" font-size="12" fill="white">Exon 1</text>
            
            <!-- Intron -->
            <rect x="300" y="70" width="200" height="40" fill="#6c757d" rx="5"/>
            <text x="400" y="95" text-anchor="middle" font-size="12" fill="white">Intron</text>
            
            <!-- Exon 2 -->
            <rect x="500" y="70" width="150" height="40" fill="#28a745" rx="5"/>
            <text x="575" y="95" text-anchor="middle" font-size="12" fill="white">Exon 2</text>
            
            <!-- Spliced Read (skipping intron) -->
            <text x="50" y="160" font-size="16" font-weight="bold" fill="#28a745">Spliced Read (is_spliced = True):</text>
            <rect x="175" y="180" width="100" height="20" fill="#28a745" rx="3"/>
            <text x="225" y="195" text-anchor="middle" font-size="10" fill="white">Segment 1</text>
            
            <line x1="275" y1="190" x2="500" y2="190" stroke="#28a745" stroke-width="2" stroke-dasharray="5,5"/>
            <text x="387" y="180" text-anchor="middle" font-size="10" fill="#28a745">BAM_CREF_SKIP (3)</text>
            
            <rect x="500" y="180" width="100" height="20" fill="#28a745" rx="3"/>
            <text x="550" y="195" text-anchor="middle" font-size="10" fill="white">Segment 2</text>
            
            <text x="225" y="220" text-anchor="middle" font-size="12" fill="#28a745">
                ref_skipped = True
            </text>
            <text x="225" y="235" text-anchor="middle" font-size="12" fill="#28a745">
                is_spliced = True
            </text>
            <text x="225" y="250" text-anchor="middle" font-size="12" fill="#28a745">
                CIGAR: 100M300N100M
            </text>
            
            <!-- Unspliced Read (covering intron) -->
            <text x="50" y="300" font-size="16" font-weight="bold" fill="#6c757d">Unspliced Read (is_spliced = False):</text>
            <rect x="175" y="320" width="375" height="20" fill="#6c757d" rx="3"/>
            <text x="362" y="335" text-anchor="middle" font-size="10" fill="white">Continuous Alignment</text>
            
            <text x="362" y="360" text-anchor="middle" font-size="12" fill="#6c757d">
                ref_skipped = False
            </text>
            <text x="362" y="375" text-anchor="middle" font-size="12" fill="#6c757d">
                is_spliced = False
            </text>
            <text x="362" y="390" text-anchor="middle" font-size="12" fill="#6c757d">
                CIGAR: 475M
            </text>
            
            <!-- CIGAR Operations Legend -->
            <text x="50" y="450" font-size="16" font-weight="bold" fill="#495057">CIGAR Operations:</text>
            <table class="cigar-table" x="100" y="470">
                <tr>
                    <th>Code</th>
                    <th>Operation</th>
                    <th>Description</th>
                    <th>In Velocyto</th>
                </tr>
                <tr>
                    <td>M</td>
                    <td>BAM_CMATCH</td>
                    <td>Alignment match</td>
                    <td>Exon alignment</td>
                </tr>
                <tr>
                    <td>N</td>
                    <td>BAM_CREF_SKIP</td>
                    <td>Reference skip</td>
                    <td>Intron skipping (<span class="highlight">sets ref_skipped = True</span>)</td>
                </tr>
                <tr>
                    <td>D</td>
                    <td>BAM_CDEL</td>
                    <td>Deletion</td>
                    <td>Not relevant for splicing</td>
                </tr>
                <tr>
                    <td>I</td>
                    <td>BAM_CINS</td>
                    <td>Insertion</td>
                    <td>Not relevant for splicing</td>
                </tr>
            </table>
        </svg>
    </div>

    <div class="math-section">
        <h2>Mathematical and Logical Relationships</h2>
        
        <h3>Property Definition</h3>
        <p>$$\text{is_spliced} = \text{ref_skipped}$$</p>
        
        <h3>CIGAR Parsing Logic</h3>
        <p>During read processing, the CIGAR string is parsed to determine <code>ref_skipped</code>:</p>
        <p>$$\text{ref_skipped} = \begin{cases} 
        \text{True}, & \text{if } \exists \text{ operation } op_i = 3 \\
        \text{False}, & \text{otherwise}
        \end{cases}$$</p>
        
        <h3>Biological Interpretation</h3>
        <p>$$\text{RNA State} = \begin{cases} 
        \text{Mature mRNA (Spliced)}, & \text{if } \text{is_spliced} = \text{True} \\
        \text{Pre-mRNA (Unspliced)}, & \text{if } \text{is_spliced} = \text{False}
        \end{cases}$$</p>
    </div>

    <div class="conclusion">
        <h2>Key Takeaways</h2>
        
        <div class="key-point">
            <h3>1. Direct Relationship</h3>
            <p><code>is_spliced</code> is a simple property that directly returns the value of <code>ref_skipped</code>.</p>
        </div>
        
        <div class="key-point">
            <h3>2. Biological Significance</h3>
            <p>These properties are fundamental to RNA velocity analysis:
                <ul>
                    <li><code>is_spliced = True</code>: Mature mRNA that has undergone splicing</li>
                    <li><code>is_spliced = False</code>: Pre-mRNA that contains intronic sequences</li>
                </ul>
            </p>
        </div>
        
        <div class="key-point">
            <h3>3. Technical Implementation</h3>
            <p>The <code>ref_skipped</code> flag is set during CIGAR parsing when operation code 3 (BAM_CREF_SKIP) is encountered, indicating that the read alignment skips a portion of the reference genome (the intron).</p>
        </div>
        
        <div class="key-point">
            <h3>4. Practical Usage</h3>
            <p>In velocyto's counting logic, reads with <code>is_spliced = True</code> contribute to the spliced count matrix, while reads with <code>is_spliced = False</code> contribute to the unspliced count matrix.</p>
        </div>
    </div>
</body>
</html>