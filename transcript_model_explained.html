<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transcript Model in Velocyto</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        header {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            margin: 0;
            font-size: 2.5rem;
        }
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-top: 0.5rem;
        }
        .section {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        }
        .section h2 {
            color: #2575fc;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 0.5rem;
            margin-top: 0;
        }
        .card-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 1.5rem 0;
        }
        .card {
            background: #f1f3f5;
            border-radius: 8px;
            padding: 1.2rem;
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card h3 {
            margin-top: 0;
            color: #495057;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }
        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        th {
            background-color: #e9ecef;
            font-weight: 600;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .visualization {
            text-align: center;
            margin: 2rem 0;
        }
        .conclusion {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            margin: 2rem 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .conclusion h2 {
            margin-top: 0;
        }
        .key-point {
            background: rgba(255,255,255,0.2);
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        pre {
            background: #f1f3f5;
            padding: 1rem;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            font-family: 'Courier New', monospace;
        }
        .highlight {
            background: #fff3cd;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-weight: bold;
        }
        .important {
            background: #ffeaa7;
            padding: 1rem;
            border-left: 4px solid #fdcb6e;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <header>
        <h1>Transcript Model in Velocyto</h1>
        <div class="subtitle">Understanding Gene Structure Representation for RNA Velocity Analysis</div>
    </header>

    <div class="section">
        <h2>What is a Transcript Model?</h2>
        <p>A <strong>Transcript Model</strong> in velocyto is a representation of a gene's structure, containing information about its exons and introns. It's a crucial component for mapping reads and determining whether they represent spliced or unspliced RNA.</p>
        
        <div class="important">
            <p><strong>Key Concept:</strong> A Transcript Model is essentially a blueprint of a gene's structure, consisting of alternating exons and introns that define how RNA is processed.</p>
        </div>
        
        <p>In velocyto, each transcript model contains:</p>
        <ul>
            <li>Transcript identification information (ID, name)</li>
            <li>Gene identification information (ID, name)</li>
            <li>Chromosomal location and strand</li>
            <li>A list of features (exons and introns) in the correct order</li>
        </ul>
    </div>

    <div class="section">
        <h2>TranscriptModel Class Structure</h2>
        
        <pre><code>class TranscriptModel:
    """A simple object representing a transcript model as a list of `vcy.Feature` objects"""
    __slots__ = ["trid", "trname", "geneid", "genename", "chromstrand", "list_features"]
    
    def __init__(self, trid: str, trname: str, geneid: str, genename: str, chromstrand: str) -> None:
        self.trid = trid          # Transcript ID
        self.trname = trname      # Transcript name
        self.geneid = geneid      # Gene ID
        self.genename = genename  # Gene name
        self.chromstrand = chromstrand  # Chromosome and strand (e.g., "17+")
        self.list_features: List[vcy.Feature] = []  # List of exons and introns</code></pre>
        
        <h3>Key Attributes:</h3>
        <table>
            <thead>
                <tr>
                    <th>Attribute</th>
                    <th>Description</th>
                    <th>Example</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><code>trid</code></td>
                    <td>Transcript ID</td>
                    <td>ENST00000380152</td>
                </tr>
                <tr>
                    <td><code>trname</code></td>
                    <td>Transcript name</td>
                    <td>TP53-201</td>
                </tr>
                <tr>
                    <td><code>geneid</code></td>
                    <td>Gene ID</td>
                    <td>ENSG00000141510</td>
                </tr>
                <tr>
                    <td><code>genename</code></td>
                    <td>Gene name</td>
                    <td>TP53</td>
                </tr>
                <tr>
                    <td><code>chromstrand</code></td>
                    <td>Chromosome and strand</td>
                    <td>17+ (chromosome 17, positive strand)</td>
                </tr>
                <tr>
                    <td><code>list_features</code></td>
                    <td>List of exons and introns</td>
                    <td>[Exon1, Intron1, Exon2, Intron2, Exon3]</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="section">
        <h2>Relationship Between TranscriptModel and Feature</h2>
        
        <div class="card-container">
            <div class="card">
                <h3>TranscriptModel</h3>
                <p>Represents an entire transcript (gene isoform) with all its exons and introns.</p>
                <p>Contains a list of <code>Feature</code> objects in the correct order.</p>
            </div>
            
            <div class="card">
                <h3>Feature</h3>
                <p>Represents a single genomic feature: exon, intron, or repeat region.</p>
                <p>Contains a reference back to its <code>transcript_model</code>.</p>
            </div>
            
            <div class="card">
                <h3>Bidirectional Link</h3>
                <p>Each <code>Feature</code> knows its <code>transcript_model</code>.</p>
                <p>Each <code>TranscriptModel</code> contains a list of its <code>Feature</code> objects.</p>
            </div>
        </div>
        
        <h3>Feature Class (Relevant Parts):</h3>
        <pre><code>class Feature:
    __slots__ = ["start", "end", "kind", "exin_no", "is_validated", "transcript_model"]
    
    def __init__(self, start: int, end: int, kind: int, exin_no: str, transcript_model: Any=None) -> None:
        self.start = start
        self.end = end
        self.transcript_model = transcript_model  # Reference back to TranscriptModel
        self.kind = kind  # ord("e") for exon, ord("i") for intron
        self.exin_no = int(exin_no)  # Exon/intron number
        self.is_validated = False</code></pre>
    </div>

    <div class="visualization">
        <h2>Visualizing Transcript Model Structure</h2>
        <svg width="100%" height="700" viewBox="0 0 900 700" xmlns="http://www.w3.org/2000/svg">
            <!-- Background -->
            <rect width="100%" height="100%" fill="#f8f9fa"/>
            
            <!-- Title -->
            <text x="450" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#333">
                Transcript Model Structure in Velocyto
            </text>
            
            <!-- Gene structure -->
            <text x="50" y="80" font-size="16" font-weight="bold" fill="#495057">Gene Structure:</text>
            
            <!-- Chromosome -->
            <rect x="100" y="100" width="700" height="30" fill="#e9ecef" rx="5"/>
            <text x="450" y="120" text-anchor="middle" font-size="12" fill="#495057">Chromosome 17 (Positive Strand)</text>
            
            <!-- Transcript Model -->
            <text x="50" y="170" font-size="16" font-weight="bold" fill="#2575fc">Transcript Model: TP53-201 (ENST00000380152)</text>
            
            <!-- Exon 1 -->
            <rect x="150" y="200" width="100" height="40" fill="#28a745" rx="5"/>
            <text x="200" y="220" text-anchor="middle" font-size="10" fill="white">Exon 1</text>
            <text x="200" y="235" text-anchor="middle" font-size="8" fill="#6c757d">Feature: e1</text>
            
            <!-- Intron 1 -->
            <rect x="250" y="200" width="100" height="40" fill="#6c757d" rx="5"/>
            <text x="300" y="220" text-anchor="middle" font-size="10" fill="white">Intron 1</text>
            <text x="300" y="235" text-anchor="middle" font-size="8" fill="#6c757d">Feature: i1</text>
            
            <!-- Exon 2 -->
            <rect x="350" y="200" width="100" height="40" fill="#28a745" rx="5"/>
            <text x="400" y="220" text-anchor="middle" font-size="10" fill="white">Exon 2</text>
            <text x="400" y="235" text-anchor="middle" font-size="8" fill="#6c757d">Feature: e2</text>
            
            <!-- Intron 2 -->
            <rect x="450" y="200" width="100" height="40" fill="#6c757d" rx="5"/>
            <text x="500" y="220" text-anchor="middle" font-size="10" fill="white">Intron 2</text>
            <text x="500" y="235" text-anchor="middle" font-size="8" fill="#6c757d">Feature: i2</text>
            
            <!-- Exon 3 -->
            <rect x="550" y="200" width="100" height="40" fill="#28a745" rx="5"/>
            <text x="600" y="220" text-anchor="middle" font-size="10" fill="white">Exon 3</text>
            <text x="600" y="235" text-anchor="middle" font-size="8" fill="#6c757d">Feature: e3</text>
            
            <!-- TranscriptModel object -->
            <rect x="100" y="280" width="700" height="100" fill="#f1f3f5" rx="10" stroke="#2575fc" stroke-width="2"/>
            <text x="450" y="300" text-anchor="middle" font-size="16" font-weight="bold" fill="#2575fc">TranscriptModel Object</text>
            
            <!-- TranscriptModel attributes -->
            <text x="120" y="325" font-size="12" fill="#495057">trid: ENST00000380152</text>
            <text x="120" y="340" font-size="12" fill="#495057">trname: TP53-201</text>
            <text x="120" y="355" font-size="12" fill="#495057">geneid: ENSG00000141510</text>
            <text x="120" y="370" font-size="12" fill="#495057">genename: TP53</text>
            <text x="120" y="385" font-size="12" fill="#495057">chromstrand: 17+</text>
            
            <!-- list_features -->
            <rect x="500" y="320" width="280" height="50" fill="#e9ecef" rx="5"/>
            <text x="640" y="335" text-anchor="middle" font-size="12" fill="#495057">list_features:</text>
            <text x="640" y="355" text-anchor="middle" font-size="10" fill="#6c757d">[e1, i1, e2, i2, e3]</text>
            
            <!-- Feature objects -->
            <text x="50" y="420" font-size="16" font-weight="bold" fill="#28a745">Feature Objects:</text>
            
            <!-- Feature 1 (Exon 1) -->
            <rect x="100" y="440" width="120" height="80" fill="#f1f3f5" rx="5" stroke="#28a745" stroke-width="2"/>
            <text x="160" y="460" text-anchor="middle" font-size="12" font-weight="bold" fill="#28a745">Feature (Exon 1)</text>
            <text x="160" y="475" font-size="10" fill="#495057">start: 1000</text>
            <text x="160" y="490" font-size="10" fill="#495057">end: 1100</text>
            <text x="160" y="505" font-size="10" fill="#495057">kind: ord("e")</text>
            <text x="160" y="520" font-size="10" fill="#495057">exin_no: 1</text>
            
            <!-- Feature 2 (Intron 1) -->
            <rect x="250" y="440" width="120" height="80" fill="#f1f3f5" rx="5" stroke="#6c757d" stroke-width="2"/>
            <text x="310" y="460" text-anchor="middle" font-size="12" font-weight="bold" fill="#6c757d">Feature (Intron 1)</text>
            <text x="310" y="475" font-size="10" fill="#495057">start: 1101</text>
            <text x="310" y="490" font-size="10" fill="#495057">end: 2000</text>
            <text x="310" y="505" font-size="10" fill="#495057">kind: ord("i")</text>
            <text x="310" y="520" font-size="10" fill="#495057">exin_no: 1</text>
            
            <!-- Feature 3 (Exon 2) -->
            <rect x="400" y="440" width="120" height="80" fill="#f1f3f5" rx="5" stroke="#28a745" stroke-width="2"/>
            <text x="460" y="460" text-anchor="middle" font-size="12" font-weight="bold" fill="#28a745">Feature (Exon 2)</text>
            <text x="460" y="475" font-size="10" fill="#495057">start: 2001</text>
            <text x="460" y="490" font-size="10" fill="#495057">end: 2100</text>
            <text x="460" y="505" font-size="10" fill="#495057">kind: ord("e")</text>
            <text x="460" y="520" font-size="10" fill="#495057">exin_no: 2</text>
            
            <!-- Bidirectional relationship -->
            <text x="50" y="560" font-size="16" font-weight="bold" fill="#6f42c1">Bidirectional Relationship:</text>
            
            <!-- Arrows showing relationship -->
            <line x1="450" y1="380" x2="160" y2="440" stroke="#6f42c1" stroke-width="2" marker-end="url(#arrow)"/>
            <line x1="450" y1="380" x2="310" y2="440" stroke="#6f42c1" stroke-width="2" marker-end="url(#arrow)"/>
            <line x1="450" y1="380" x2="460" y2="440" stroke="#6f42c1" stroke-width="2" marker-end="url(#arrow)"/>
            
            <line x1="160" y1="440" x2="450" y2="380" stroke="#6f42c1" stroke-width="2" marker-start="url(#arrow)"/>
            <line x1="310" y1="440" x2="450" y2="380" stroke="#6f42c1" stroke-width="2" marker-start="url(#arrow)"/>
            <line x1="460" y1="440" x2="450" y2="380" stroke="#6f42c1" stroke-width="2" marker-start="url(#arrow)"/>
            
            <text x="300" y="420" text-anchor="middle" font-size="12" fill="#6f42c1">TranscriptModel contains Features</text>
            <text x="600" y="420" text-anchor="middle" font-size="12" fill="#6f42c1">Features reference TranscriptModel</text>
            
            <!-- Arrow marker definition -->
            <defs>
                <marker id="arrow" markerWidth="10" markerHeight="10" refX="6" refY="3" orient="auto" markerUnits="strokeWidth">
                    <path d="M0,0 L0,6 L9,3 z" fill="#6f42c1"/>
                </marker>
            </defs>
        </svg>
    </div>

    <div class="section">
        <h2>How Transcript Models Are Built</h2>
        
        <h3>Creating a Transcript Model from GTF Annotation</h3>
        <pre><code># In the counter.py file, transcript models are created from GTF files:

def read_transcriptmodels(self, gtf_file: str) -> Dict[str, Dict[str, vcy.TranscriptModel]]:
    # ... parsing logic ...
    
    # For each exon in the GTF file:
    if feature_type == "exon":
        # Extract transcript and gene information
        trid = regex_trid.search(tags).group(1)
        geneid = regex_geneid.search(tags).group(1)
        # ... other information ...
        
        # Create or retrieve TranscriptModel
        try:
            # Append exon to existing transcript model
            features[trid].append_exon(vcy.Feature(start=start, end=end, kind=ord("e"), exin_no=exonno))
        except KeyError:
            # Create new transcript model
            features[trid] = vcy.TranscriptModel(trid=trid, trname=trname, geneid=geneid, 
                                                genename=genename, chromstrand=chromstrand)
            features[trid].append_exon(vcy.Feature(start=start, end=end, kind=ord("e"), exin_no=exonno))</code></pre>
        
        <h3>Appending Exons and Creating Introns</h3>
        <pre><code>def append_exon(self, exon_feature: vcy.Feature) -> None:
    """Append an exon and create an intron when needed"""
    exon_feature.transcript_model = self  # Link feature to transcript model
    
    if len(self.list_features) == 0:
        # First exon
        self.list_features.append(exon_feature)
    else:
        # Create intron between previous feature and this exon
        if self.chromstrand[-1] == "+":
            intron_number = self.list_features[-1].exin_no
        else:
            intron_number = self.list_features[-1].exin_no - 1
            
        # Create intron feature
        self.list_features.append(vcy.Feature(
            start=self.list_features[-1].end + 1,
            end=exon_feature.start - 1,
            kind=ord("i"),
            exin_no=intron_number,
            transcript_model=self))
            
        # Add the exon
        self.list_features.append(exon_feature)</code></pre>
    </div>

    <div class="section">
        <h2>Role in RNA Velocity Analysis</h2>
        
        <div class="card-container">
            <div class="card">
                <h3>Read Mapping</h3>
                <p>Transcript models are used to map reads to specific genomic features (exons/introns).</p>
            </div>
            
            <div class="card">
                <h3>Splicing Classification</h3>
                <p>By mapping reads to transcript models, velocyto determines if reads are spliced or unspliced.</p>
            </div>
            
            <div class="card">
                <h3>Gene Quantification</h3>
                <p>Transcript models group features by gene, enabling gene-level expression quantification.</p>
            </div>
            
            <div class="card">
                <h3>Intron Validation</h3>
                <p>Transcript models provide the context for validating introns through exon-intron spanning reads.</p>
            </div>
        </div>
        
        <h3>Example Usage in Read Classification:</h3>
        <pre><code># When classifying a read, velocyto checks which transcript models it overlaps with:
for transcript_model, segments_list in molitem.mappings_record.items():
    for segment_match in segments_list:
        if segment_match.maps_to_intron:
            # This read maps to an intron of this transcript model
            if segment_match.feature.is_validated:
                # This is a validated intron (has supporting spanning reads)
                has_validated_intron = True
                
                # Check if this specific read spans exon-intron boundaries
                if (segment_match.feature.end_overlaps_with_part_of(segment_match.segment) or
                    segment_match.feature.start_overlaps_with_part_of(segment_match.segment)):
                    has_exin_intron_span = True
                    
        elif segment_match.maps_to_exon:
            # This read maps to an exon of this transcript model
            has_exons = True</code></pre>
    </div>

    <div class="conclusion">
        <h2>Key Takeaways</h2>
        
        <div class="key-point">
            <h3>1. Transcript Models Represent Gene Structure</h3>
            <p>Transcript models are essential data structures that represent the organization of exons and introns in genes, enabling accurate mapping and classification of sequencing reads.</p>
        </div>
        
        <div class="key-point">
            <h3>2. Bidirectional Relationship with Features</h3>
            <p>Each transcript model contains a list of feature objects (exons/introns), and each feature maintains a reference back to its transcript model, creating a bidirectional relationship.</p>
        </div>
        
        <div class="key-point">
            <h3>3. Critical for RNA Velocity Analysis</h3>
            <p>Transcript models are fundamental to velocyto's ability to distinguish spliced from unspliced reads, which is the basis for RNA velocity calculations.</p>
        </div>
        
        <div class="key-point">
            <h3>4. Built from Genome Annotations</h3>
            <p>Transcript models are constructed from GTF/GFF annotation files, ensuring that read classification is based on known gene structures.</p>
        </div>
    </div>
</body>
</html>