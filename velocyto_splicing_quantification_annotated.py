"""
Comprehensive Annotated Code for Velocyto Spliced/Unspliced Read Quantification

This file contains the key components of velocyto's algorithm for quantifying 
spliced and unspliced reads, with detailed annotations explaining the process.
"""

from typing import *
from collections import defaultdict
import pysam
import numpy as np


# 1. READ CLASS - Basic unit of alignment information
# ==================================================

class Read:
    """Container for reads from sam alignment file"""
    __slots__ = ["bc", "umi", "chrom", "strand", "pos", "segments", "clip5", "clip3", "ref_skipped"]

    def __init__(self, bc: str, umi: str, chrom: str, strand: str, pos: int, segments: List, clip5: Any, clip3: Any, ref_skipped: bool) -> None:
        self.bc, self.umi, self.chrom, self.strand, self.pos, self.segments, self.clip5, self.clip3, self.ref_skipped = \
            bc, umi, chrom, strand, pos, segments, clip5, clip3, ref_skipped

    @property
    def is_spliced(self) -> bool:
        """
        Determines if a read is spliced based on reference skips.
        
        In BAM files, spliced alignments have CIGAR operations with code 3 (BAM_CREF_SKIP),
        which represent intron skipping. When a read aligns to exons but skips the intron
        region, it's considered a spliced read (mature mRNA).
        """
        return self.ref_skipped  # len(self.segments) > 1
        
    @property
    def start(self) -> int:
        return self.segments[0][0]

    @property
    def end(self) -> int:
        return self.segments[-1][1]

    @property
    def span(self) -> int:
        return self.end - self.start + 1


# 2. FEATURE CLASS - Genomic feature representation
# =================================================

class Feature:
    """A simple class representing an annotated genomic feature (e.g. exon, intron, masked repeat)"""
    __slots__ = ["start", "end", "kind", "exin_no", "is_validated", "transcript_model"]
    
    def __init__(self, start: int, end: int, kind: int, exin_no: str, transcript_model: Any=None) -> None:
        self.start = start
        self.end = end
        self.transcript_model = transcript_model
        self.kind = kind  # it should be ord("e"), ord("i"), ord("m"), ....
        self.exin_no = int(exin_no)
        self.is_validated = False  # Will be set to True if reads span exon-intron boundaries
    
    def get_downstream_exon(self) -> Any:
        """To use only for introns. Returns the vcy.Feature corresponding to the neighbour exon downstream"""
        if self.transcript_model.chromstrand[-1] == "+":
            ix = self.exin_no * 2
        else:
            # in the case on strand -
            ix = len(self.transcript_model.list_features) - 2 * self.exin_no + 1
        return self.transcript_model.list_features[ix]

    def get_upstream_exon(self) -> Any:
        """To use only for introns. Returns the vcy.Feature corresponding to the neighbour exon downstream"""
        if self.transcript_model.chromstrand[-1] == "+":
            ix = (self.exin_no * 2) - 2
        else:
            # in the case on strand -
            ix = len(self.transcript_model.list_features) - 2 * self.exin_no - 1
        return self.transcript_model.list_features[ix]

    def end_overlaps_with_part_of(self, segment: Tuple[int, int], minimum_flanking: int=5) -> bool:
        """Check if segment overlaps with the end of this feature"""
        return (segment[0] + minimum_flanking < self.end) and (segment[-1] - minimum_flanking > self.end)
        
    def start_overlaps_with_part_of(self, segment: Tuple[int, int], minimum_flanking: int=5) -> bool:
        """Check if segment overlaps with the start of this feature"""
        return (segment[0] + minimum_flanking < self.start) and (segment[-1] - minimum_flanking > self.start)


# 3. MOLITEM CLASS - Grouping reads with same barcode/UMI
# =======================================================

def dictionary_intersect(d1: DefaultDict[Any, List], d2: DefaultDict[Any, List]) -> DefaultDict[Any, List]:
    """Set intersection (&) operation on default dictionary"""
    keys_set = set(d1) & set(d2)
    return defaultdict(list, ((k, d1[k] + d2[k]) for k in keys_set))

class Molitem:
    """Object that represents a molecule in the counting pipeline"""
    __slots__ = ["mappings_record"]

    def __init__(self) -> None:
        self.mappings_record: DefaultDict[object, List[object]] = None

    def add_mappings_record(self, mappings_record: DefaultDict[object, List[object]]) -> None:
        """
        Add mapping information to this molecule.
        
        In velocyto, reads with the same cell barcode and UMI are grouped into a "molecule".
        This helps reduce amplification bias and provides more accurate quantification.
        """
        if self.mappings_record is None:
            self.mappings_record = mappings_record
        else:
            # When a read aligns to multiple locations, we take the intersection of compatible transcript models
            self.mappings_record = dictionary_intersect(self.mappings_record, mappings_record)


# 4. LOGIC CLASSES - Counting strategies
# ======================================

class Logic:
    """Base class from which all the logics should inherit"""
    
    def count(self, molitem: Molitem, cell_bcidx: int, dict_layers_columns: Dict[str, np.ndarray], geneid2ix: Dict[str, int]) -> Union[None, int]:
        """
        Abstract method for counting a molecule.
        
        This is where the core logic for classifying reads as spliced/unspliced/ambiguous happens.
        Different subclasses implement different stringency levels.
        """
        return None

class Permissive10X(Logic):
    """
    Permissive logic for 10X Genomics chemistry.
    
    This is the default logic which is most permissive in counting reads:
    - Exonic reads → Counted as spliced
    - Intronic reads → Counted as unspliced
    - Reads spanning exon-intron boundaries → Counted as unspliced
    """
    
    def count(self, molitem: Molitem, cell_bcidx: int, dict_layers_columns: Dict[str, np.ndarray], geneid2ix: Dict[str, int]) -> int:
        """
        Count a molecule according to permissive 10X logic.
        
        This method analyzes the mapping of a molecule to transcript models and classifies it
        as spliced, unspliced, or ambiguous based on which genomic features it aligns to.
        """
        # Get the count matrices for each category
        spliced = dict_layers_columns["spliced"]
        unspliced = dict_layers_columns["unspliced"]
        ambiguous = dict_layers_columns["ambiguous"]
        
        # The hits are not compatible with any annotated transcript model
        if len(molitem.mappings_record) == 0:
            return 2  # No compatible genes found
            
        # Compatible with one or more transcript models:
        else:
            # Check that there are not different possible genes
            if len(set(i.geneid for i in molitem.mappings_record.keys())) == 1:
                gene_check: Set[str] = set()
                
                # Initialize flags for different mapping patterns
                has_onlyintron_model = 0
                has_only_span_exin_model = 1
                has_onlyintron_and_valid_model = 0
                has_valid_mixed_model = 0
                has_invalid_mixed_model = 0
                has_onlyexo_model = 0
                has_mixed_model = 0
                multi_gene = 0
                
                # Analyze each transcript model this molecule maps to
                for transcript_model, segments_list in molitem.mappings_record.items():
                    gene_check.add(transcript_model.geneid)
                    if len(gene_check) > 1:
                        multi_gene = 1
                    
                    # Initialize flags for this transcript model
                    has_introns = 0
                    has_exons = 0
                    has_validated_intron = 0
                    has_exin_intron_span = 0
                    
                    # Analyze each segment of the alignment
                    for segment_match in segments_list:
                        if segment_match.maps_to_intron:
                            # This segment maps to an intron
                            has_introns = 1
                            if segment_match.feature.is_validated:
                                # This is a validated intron (has supporting reads that span exon-intron boundaries)
                                has_validated_intron = 1
                                # Check if this segment spans exon-intron boundaries
                                if segment_match.feature.end_overlaps_with_part_of(segment_match.segment):
                                    downstream_exon = segment_match.feature.get_downstream_exon()
                                    if downstream_exon.start_overlaps_with_part_of(segment_match.segment):
                                        has_exin_intron_span = 1
                                if segment_match.feature.start_overlaps_with_part_of(segment_match.segment):
                                    upstream_exon = segment_match.feature.get_upstream_exon()
                                    if upstream_exon.end_overlaps_with_part_of(segment_match.segment):
                                        has_exin_intron_span = 1
                        elif segment_match.maps_to_exon:
                            # This segment maps to an exon
                            has_exons = 1
                    
                    # Classify the transcript model based on its mapping pattern
                    if has_validated_intron and not has_exons:
                        has_onlyintron_and_valid_model = 1
                    if has_introns and not has_exons:
                        has_onlyintron_model = 1
                    if has_exons and not has_introns:
                        has_onlyexo_model = 1
                    if has_exons and has_introns and not has_validated_intron and not has_exin_intron_span:
                        has_invalid_mixed_model = 1
                        has_mixed_model = 1
                    if has_exons and has_introns and has_validated_intron and not has_exin_intron_span:
                        has_valid_mixed_model = 1
                        has_mixed_model = 1
                    if not has_exin_intron_span:
                        has_only_span_exin_model = 0
                
                # Handle different classification scenarios
                if multi_gene:
                    # Many genes are compatible with the observation, do not count
                    return 1
                else:
                    if not len(molitem.mappings_record):
                        # No gene is compatible with the observation, do not count
                        return 2
                    else:
                        if has_onlyexo_model and not has_onlyintron_model and not has_mixed_model:
                            # More common situation, normal exonic read, count as spliced
                            gene_ix = geneid2ix[transcript_model.geneid]
                            spliced[gene_ix, cell_bcidx] += 1
                            return 0
                        if has_only_span_exin_model:
                            # All the compatible transcript models have spanning exon-intron boundaries, count unspliced
                            gene_ix = geneid2ix[transcript_model.geneid]
                            unspliced[gene_ix, cell_bcidx] += 1
                            return 0
                        if has_onlyintron_and_valid_model and not has_mixed_model and not has_onlyexo_model:
                            if len(segments_list) == 1:
                                # Singleton in validated intron
                                gene_ix = geneid2ix[transcript_model.geneid]
                                unspliced[gene_ix, cell_bcidx] += 1
                                return 0
                            else:
                                # Non-singleton in validated intron
                                gene_ix = geneid2ix[transcript_model.geneid]
                                unspliced[gene_ix, cell_bcidx] += 1
                                return 0
                        if has_onlyintron_model and not has_onlyintron_and_valid_model and not has_mixed_model and not has_onlyexo_model:
                            if len(segments_list) == 1:
                                # Singleton in non-validated intron
                                gene_ix = geneid2ix[transcript_model.geneid]
                                unspliced[gene_ix, cell_bcidx] += 1
                                return 0
                            else:
                                # Non-singleton in non-validated intron
                                gene_ix = geneid2ix[transcript_model.geneid]
                                unspliced[gene_ix, cell_bcidx] += 1
                                return 0
                        if has_invalid_mixed_model and not has_valid_mixed_model and not has_onlyintron_model and not has_onlyexo_model and not has_only_span_exin_model:
                            # Not validated and mapping to exon and introns, happens rarely in 10X / irrelevant. Count anyways
                            gene_ix = geneid2ix[transcript_model.geneid]
                            unspliced[gene_ix, cell_bcidx] += 1
                            return 0
                        if has_valid_mixed_model and not has_onlyintron_model and not has_onlyexo_model and not has_only_span_exin_model:
                            # Validated and mapping to exon and introns, happens rarely in 10X. Count as unspliced.
                            gene_ix = geneid2ix[transcript_model.geneid]
                            unspliced[gene_ix, cell_bcidx] += 1
                            return 0
                        if has_onlyintron_model and has_onlyexo_model and not has_mixed_model:
                            # Ambiguity among the transcript models compatible with the mapping, most common case! Count ambiguous
                            gene_ix = geneid2ix[transcript_model.geneid]
                            ambiguous[gene_ix, cell_bcidx] += 1
                            return 0
                        if has_onlyintron_model and not has_onlyexo_model and has_mixed_model:
                            # Very rare, at least in 10X.
                            gene_ix = geneid2ix[transcript_model.geneid]
                            unspliced[gene_ix, cell_bcidx] += 1
                            return 0
                        if not has_onlyintron_model and has_onlyexo_model and has_mixed_model:
                            # Ambiguity among the transcript models compatible with the mapping. Count ambiguous
                            gene_ix = geneid2ix[transcript_model.geneid]
                            ambiguous[gene_ix, cell_bcidx] += 1
                            return 0
                        if has_onlyintron_model and has_onlyexo_model and has_mixed_model:
                            # Ambiguity among the transcript models compatible with the mapping. Very rare. Count ambiguous
                            gene_ix = geneid2ix[transcript_model.geneid]
                            ambiguous[gene_ix, cell_bcidx] += 1
                            return 0
                return 4
            else:
                return 3


# 5. COUNTER CLASS - Main counting pipeline (simplified)
# ======================================================

class ExInCounter:
    """Main class to do the counting of introns and exons"""
    
    def parse_cigar_tuple(self, cigartuples: List[Tuple], pos: int) -> Tuple[List[Tuple[int, int]], bool, int, int]:
        """
        Parse CIGAR tuples to extract segments and determine if read is spliced.
        
        This is where the ref_skipped flag is determined based on CIGAR operations:
        - Operation 0 (BAM_CMATCH): Alignment match
        - Operation 3 (BAM_CREF_SKIP): Reference skip (intron)
        """
        segments = []
        ref_skip = False  # This will be set to True if we encounter a reference skip
        clip5 = clip3 = 0
        p = pos
        for i, (operation_id, length) in enumerate(cigartuples):
            if operation_id == 0:  # BAM_CMATCH - alignment match
                segments.append((p, p + length - 1))
                p += length
            elif operation_id == 3:  # BAM_CREF_SKIP - reference skip (intron)
                ref_skip = True  # This read spans an intron, so it's spliced
                p += length
            elif operation_id == 4:  # BAM_CSOFT_CLIP
                if p == pos:
                    clip5 = length  # At start of alignment
                else:
                    clip3 = length  # At end of alignment
                p += length
        
        return segments, ref_skip, clip5, clip3
    
    def count_cell_batch(self) -> Tuple[Dict[str, np.ndarray], List[str]]:
        """
        Perform molecule counting for the current batch of cells.
        
        This is where the actual counting happens, using the selected logic.
        """
        molitems: DefaultDict[str, Molitem] = defaultdict(Molitem)
        
        # Process each read and group by barcode/UMI
        for r in self.reads_to_count:
            # Find overlapping genomic features
            mappings_record = self.find_overlapping_features(r)
            if len(mappings_record):
                # Group reads by barcode and UMI into molecules
                bcumi = f"{r.bc}${r.umi}"
                molitems[bcumi].add_mappings_record(mappings_record)
        
        # Count each molecule using the selected logic
        bc2idx: Dict[str, int] = dict(zip(self.cell_batch, range(len(self.cell_batch))))
        for bcumi, molitem in molitems.items():
            bc = bcumi.split("$")[0]  # extract the bc part from the bc+umi
            bcidx = bc2idx[bc]
            # Use the selected logic to classify and count this molecule
            self.logic.count(molitem, bcidx, dict_layers_columns, self.geneid2ix)
        
        return dict_layers_columns, [idx2bc[i] for i in range(len(idx2bc))]


# 6. CONSTANTS - Feature type definitions
# =======================================

# These constants define the different types of genomic features
EXON = 1
INTRON = 8

# CIGAR operation codes
CIGAR = {0: "BAM_CMATCH",
         1: "BAM_CINS",
         2: "BAM_CDEL",
         3: "BAM_CREF_SKIP",  # This is the key operation for identifying spliced reads
         4: "BAM_CSOFT_CLIP",
         5: "BAM_CHARD_CLIP",
         6: "BAM_CPAD",
         7: "BAM_CEQUAL",
         8: "BAM_CDIFF",
         9: "BAM_CBACK"}

"""
ANNOTATED EXPLANATION OF VELOCYTO'S SPLICING QUANTIFICATION PROCESS:

1. READ IDENTIFICATION:
   - Velocyto parses BAM files using pysam
   - For each read, it examines the CIGAR string to determine if it's spliced
   - Operation code 3 (BAM_CREF_SKIP) indicates intron skipping, marking the read as spliced

2. MOLECULE GROUPING:
   - Reads with identical cell barcode (bc) and unique molecular identifier (umi) are grouped
   - This reduces amplification bias and provides more accurate quantification

3. FEATURE MAPPING:
   - Each read is mapped to annotated genomic features (exons, introns)
   - Introns can be validated if reads span exon-intron boundaries

4. COUNTING LOGIC:
   - The Permissive10X logic (default) classifies molecules as:
     * Spliced: Map only to exons
     * Unspliced: Map to introns or span exon-intron junctions
     * Ambiguous: Map to both exons and introns in an ambiguous pattern

5. FINAL QUANTIFICATION:
   - Results are stored in count matrices for spliced, unspliced, and ambiguous reads
   - These matrices form the basis for RNA velocity calculations
"""