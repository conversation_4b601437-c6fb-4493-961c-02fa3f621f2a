#!/usr/bin/env python3
"""
Example usage of the ReadClassifier for BAM/GTF read classification.

This script demonstrates how to use the ReadClassifier to process BAM and GTF files
and identify spliced/unspliced reads with detailed tagging.

Author: AI Assistant
Date: 2025-08-03
"""

import os
import sys
from read_classifier import ReadClassifier


def example_basic_usage():
    """Basic example of using ReadClassifier."""
    print("="*60)
    print("BASIC USAGE EXAMPLE")
    print("="*60)
    
    # File paths (replace with your actual files)
    bam_file = "sample.bam"
    gtf_file = "annotations.gtf"
    output_file = "read_classifications.tsv"
    
    print(f"BAM file: {bam_file}")
    print(f"GTF file: {gtf_file}")
    print(f"Output file: {output_file}")
    
    # Check if files exist
    if not os.path.exists(bam_file):
        print(f"WARNING: BAM file {bam_file} not found. Please update the path.")
        return
    
    if not os.path.exists(gtf_file):
        print(f"WARNING: GTF file {gtf_file} not found. Please update the path.")
        return
    
    try:
        # Initialize the classifier
        print("\n1. Initializing ReadClassifier...")
        classifier = ReadClassifier(
            gtf_file=gtf_file,
            stranded=True,  # Consider strand information
            verbose=True    # Enable detailed logging
        )
        
        # Process the BAM file
        print("\n2. Processing BAM file...")
        results = classifier.process_bam_file(
            bam_file=bam_file,
            output_file=output_file,
            max_reads=1000  # Limit to 1000 reads for this example
        )
        
        # Print summary
        print("\n3. Classification Summary:")
        classifier.print_summary(results)
        
        # Show some example results
        print("\n4. Example Results (first 5 reads):")
        print("-" * 80)
        for i, result in enumerate(results[:5]):
            print(f"Read {i+1}:")
            print(f"  Name: {result['read_name']}")
            print(f"  Classification: {result['classification']}")
            print(f"  Gene: {result['gene_id']}")
            print(f"  Location: {result['chromosome']}:{result['start']}-{result['end']} ({result['strand']})")
            print(f"  Is spliced: {result['is_spliced']}")
            print(f"  Details: {result['details']}")
            print()
        
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure you have valid BAM and GTF files and that velocyto is properly installed.")


def example_unstranded_analysis():
    """Example of unstranded analysis."""
    print("="*60)
    print("UNSTRANDED ANALYSIS EXAMPLE")
    print("="*60)
    
    bam_file = "sample.bam"
    gtf_file = "annotations.gtf"
    
    if not os.path.exists(bam_file) or not os.path.exists(gtf_file):
        print("WARNING: Sample files not found. Please update file paths.")
        return
    
    try:
        # Initialize classifier for unstranded data
        classifier = ReadClassifier(
            gtf_file=gtf_file,
            stranded=False,  # Ignore strand information
            verbose=False
        )
        
        # Process with different parameters
        results = classifier.process_bam_file(
            bam_file=bam_file,
            output_file="unstranded_classifications.tsv",
            max_reads=500
        )
        
        classifier.print_summary(results)
        
    except Exception as e:
        print(f"Error: {e}")


def example_batch_processing():
    """Example of processing multiple BAM files."""
    print("="*60)
    print("BATCH PROCESSING EXAMPLE")
    print("="*60)
    
    # List of BAM files to process
    bam_files = ["sample1.bam", "sample2.bam", "sample3.bam"]
    gtf_file = "annotations.gtf"
    
    if not os.path.exists(gtf_file):
        print("WARNING: GTF file not found. Please update the path.")
        return
    
    # Initialize classifier once
    try:
        classifier = ReadClassifier(gtf_file=gtf_file, verbose=False)
        
        all_results = []
        
        for bam_file in bam_files:
            if os.path.exists(bam_file):
                print(f"\nProcessing {bam_file}...")
                
                results = classifier.process_bam_file(
                    bam_file=bam_file,
                    output_file=f"{bam_file}_classifications.tsv",
                    max_reads=100  # Small number for demo
                )
                
                all_results.extend(results)
                classifier.print_summary(results)
            else:
                print(f"WARNING: {bam_file} not found, skipping...")
        
        # Combined summary
        if all_results:
            print("\n" + "="*60)
            print("COMBINED SUMMARY FOR ALL FILES")
            print("="*60)
            classifier.print_summary(all_results)
            
    except Exception as e:
        print(f"Error: {e}")


def example_custom_analysis():
    """Example of custom analysis of results."""
    print("="*60)
    print("CUSTOM ANALYSIS EXAMPLE")
    print("="*60)
    
    bam_file = "sample.bam"
    gtf_file = "annotations.gtf"
    
    if not os.path.exists(bam_file) or not os.path.exists(gtf_file):
        print("WARNING: Sample files not found. Please update file paths.")
        return
    
    try:
        classifier = ReadClassifier(gtf_file=gtf_file, verbose=False)
        results = classifier.process_bam_file(bam_file, max_reads=1000)
        
        # Custom analysis
        print("Custom Analysis Results:")
        print("-" * 40)
        
        # Count reads per gene
        gene_counts = {}
        for result in results:
            gene_id = result['gene_id']
            if gene_id not in ['multi_gene', 'no_gene']:
                if gene_id not in gene_counts:
                    gene_counts[gene_id] = {'spliced': 0, 'unspliced': 0, 'ambiguous': 0}
                gene_counts[gene_id][result['classification']] += 1
        
        # Show top 10 genes by read count
        sorted_genes = sorted(gene_counts.items(), 
                            key=lambda x: sum(x[1].values()), 
                            reverse=True)
        
        print("Top 10 genes by read count:")
        for gene_id, counts in sorted_genes[:10]:
            total = sum(counts.values())
            print(f"  {gene_id}: {total} reads (S:{counts['spliced']}, U:{counts['unspliced']}, A:{counts['ambiguous']})")
        
        # Splicing ratio analysis
        print("\nSplicing ratios for top genes:")
        for gene_id, counts in sorted_genes[:5]:
            total_su = counts['spliced'] + counts['unspliced']
            if total_su > 0:
                ratio = counts['spliced'] / total_su
                print(f"  {gene_id}: {ratio:.2f} (spliced/(spliced+unspliced))")
        
    except Exception as e:
        print(f"Error: {e}")


def main():
    """Run all examples."""
    print("ReadClassifier Usage Examples")
    print("=" * 60)
    print("This script demonstrates various ways to use the ReadClassifier.")
    print("Please update the file paths in the examples to match your data.")
    print()
    
    # Run examples
    example_basic_usage()
    print("\n" + "="*80 + "\n")
    
    example_unstranded_analysis()
    print("\n" + "="*80 + "\n")
    
    example_batch_processing()
    print("\n" + "="*80 + "\n")
    
    example_custom_analysis()
    
    print("\n" + "="*80)
    print("Examples completed!")
    print("For command-line usage, run: python read_classifier.py --help")


if __name__ == "__main__":
    main()
