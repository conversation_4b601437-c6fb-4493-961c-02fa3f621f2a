"""
Velocyto Classification Rules Implementation

This file contains the specific code that implements the classification rules for 
spliced and unspliced read quantification in velocyto.
"""

from typing import *
from collections import defaultdict
import numpy as np


class Permissive10X:
    """
    Implementation of velocyto's classification rules for 10X Genomics data.
    
    Classification Rules:
    1. Multi-gene mapping → AMBIGUOUS (cannot determine origin)
    2. No gene mapping → Not counted (intergenic)
    3. Exon-only mapping → SPLICED (mature mRNA)
    4. Exon-intron spanning → UNSPLICED (active splicing)
    5. Validated intron → UNSPLICED (pre-mRNA)
    """
    
    def count(self, molitem: object, cell_bcidx: int, dict_layers_columns: Dict[str, np.ndarray], geneid2ix: Dict[str, int]) -> int:
        """
        Count a molecule according to the specific classification rules.
        """
        # Get the count matrices for each category
        spliced = dict_layers_columns["spliced"]
        unspliced = dict_layers_columns["unspliced"]
        ambiguous = dict_layers_columns["ambiguous"]
        
        # RULE 2: No gene mapping
        # =======================
        # If the read doesn't map to any annotated gene, it's not counted (intergenic)
        if len(molitem.mappings_record) == 0:
            return 2  # Return code for "no gene compatible"
            
        # RULE 1: Multi-gene mapping
        # ==========================
        # Check if the read maps to multiple genes
        else:
            if len(set(i.geneid for i in molitem.mappings_record.keys())) > 1:
                # Multi-gene mapping: Cannot determine origin → AMBIGUOUS
                gene_ix = geneid2ix[transcript_model.geneid]
                ambiguous[gene_ix, cell_bcidx] += 1
                return 1  # Return code for "multi-gene"
                
            # Single gene mapping - analyze the mapping pattern
            else:
                # Initialize flags for different mapping patterns
                has_only_exons = True      # Assume exon-only until proven otherwise
                has_introns = False        # Assume no introns until found
                has_spanning = False       # Assume no exon-intron spanning until found
                has_validated_intron = False  # Assume no validated introns until found
                
                # Analyze each transcript model this molecule maps to
                for transcript_model, segments_list in molitem.mappings_record.items():
                    # Analyze each segment of the alignment
                    for segment_match in segments_list:
                        if segment_match.maps_to_intron:
                            # This segment maps to an intron
                            has_introns = True
                            has_only_exons = False  # Not exon-only anymore
                            
                            # Check if this is a validated intron
                            if segment_match.feature.is_validated:
                                has_validated_intron = True
                                
                                # Check if this segment spans exon-intron boundaries
                                # RULE 4: Exon-intron spanning
                                if (segment_match.feature.end_overlaps_with_part_of(segment_match.segment) or
                                    segment_match.feature.start_overlaps_with_part_of(segment_match.segment)):
                                    has_spanning = True
                                    
                        elif segment_match.maps_to_exon:
                            # This segment maps to an exon
                            # (has_only_exons remains True if no introns found)
                            pass
                
                # Apply classification rules based on mapping patterns
                
                # RULE 3: Exon-only mapping
                # =========================
                if has_only_exons and not has_introns:
                    # Read maps only to exonic sequences → SPLICED (mature mRNA)
                    gene_ix = geneid2ix[transcript_model.geneid]
                    spliced[gene_ix, cell_bcidx] += 1
                    return 0
                    
                # RULE 4: Exon-intron spanning
                # ============================
                elif has_spanning:
                    # Read spans exon-intron boundaries → UNSPLICED (active splicing)
                    gene_ix = geneid2ix[transcript_model.geneid]
                    unspliced[gene_ix, cell_bcidx] += 1
                    return 0
                    
                # RULE 5: Validated intron
                # ========================
                elif has_validated_intron and not has_spanning:
                    # Read maps to intron validated by spanning reads → UNSPLICED (pre-mRNA)
                    gene_ix = geneid2ix[transcript_model.geneid]
                    unspliced[gene_ix, cell_bcidx] += 1
                    return 0
                    
                # Other cases (introns that are neither spanning nor validated)
                # =============================================================
                elif has_introns and not has_validated_intron and not has_spanning:
                    # Treat as unspliced (per permissive logic)
                    gene_ix = geneid2ix[transcript_model.geneid]
                    unspliced[gene_ix, cell_bcidx] += 1
                    return 0
                    
                # Ambiguous cases (exon and intron mapping without clear spanning)
                # =================================================================
                else:
                    # Ambiguous mapping pattern → AMBIGUOUS
                    gene_ix = geneid2ix[transcript_model.geneid]
                    ambiguous[gene_ix, cell_bcidx] += 1
                    return 0


# Supporting code for the classification rules
# ============================================

class SegmentMatch:
    """
    Represents a match between a read segment and a genomic feature.
    """
    def __init__(self, feature, segment, is_spliced):
        self.feature = feature      # The genomic feature (exon/intron)
        self.segment = segment      # The read segment coordinates
        self.is_spliced = is_spliced  # Whether this segment is part of a spliced read
        
    @property
    def maps_to_exon(self) -> bool:
        """Check if this segment maps to an exon."""
        return self.feature.kind == ord("e")
        
    @property
    def maps_to_intron(self) -> bool:
        """Check if this segment maps to an intron."""
        return self.feature.kind == ord("i")


class Feature:
    """
    A genomic feature (exon or intron).
    """
    def __init__(self, start: int, end: int, kind: int, exin_no: str):
        self.start = start
        self.end = end
        self.kind = kind  # ord("e") for exon, ord("i") for intron
        self.exin_no = int(exin_no)
        self.is_validated = False  # Set to True if validated by spanning reads
        
    def end_overlaps_with_part_of(self, segment: Tuple[int, int], minimum_flanking: int = 5) -> bool:
        """
        Check if segment overlaps with the end of this feature.
        Used to detect exon-intron spanning reads.
        """
        return (segment[0] + minimum_flanking < self.end) and (segment[-1] - minimum_flanking > self.end)
        
    def start_overlaps_with_part_of(self, segment: Tuple[int, int], minimum_flanking: int = 5) -> bool:
        """
        Check if segment overlaps with the start of this feature.
        Used to detect exon-intron spanning reads.
        """
        return (segment[0] + minimum_flanking < self.start) and (segment[-1] - minimum_flanking > self.start)


# Example of how validation works
# ===============================

def validate_introns(reads_to_count, feature_indexes):
    """
    Example of how intron validation works in velocyto.
    
    Introns are validated by checking if reads span exon-intron boundaries.
    """
    for r in reads_to_count:
        # Consider only non-spliced reads for validation
        if not r.is_spliced:
            # Look for overlap between the intervals and the read
            ii = feature_indexes[r.chrom + r.strand]
            mappings_record = ii.find_overlapping_ivls(r)
            
            # Check if any mapping spans exon-intron boundaries
            for transcript_model, segments_list in mappings_record.items():
                for segment_match in segments_list:
                    if segment_match.maps_to_intron:
                        # Check if this segment spans to neighboring exons
                        if (segment_match.feature.end_overlaps_with_part_of(segment_match.segment) or
                            segment_match.feature.start_overlaps_with_part_of(segment_match.segment)):
                            # Validate this intron
                            segment_match.feature.is_validated = True


"""
SUMMARY OF CLASSIFICATION RULE IMPLEMENTATION:

1. MULTI-GENE MAPPING → AMBIGUOUS
   - Code: `len(set(i.geneid for i in molitem.mappings_record.keys())) > 1`
   - Action: Increment ambiguous count

2. NO GENE MAPPING → NOT COUNTED (INTERGENIC)
   - Code: `len(molitem.mappings_record) == 0`
   - Action: Return without incrementing any count

3. EXON-ONLY MAPPING → SPLICED
   - Code: `has_only_exons and not has_introns`
   - Action: Increment spliced count

4. EXON-INTRON SPANNING → UNSPLICED
   - Code: `has_spanning` (determined by overlap checks)
   - Action: Increment unspliced count

5. VALIDATED INTRON → UNSPLICED
   - Code: `has_validated_intron and not has_spanning`
   - Action: Increment unspliced count

The key functions for detecting spanning reads are:
- `end_overlaps_with_part_of()` 
- `start_overlaps_with_part_of()`

These check if a read segment overlaps with both an intron and its neighboring exon,
which is the signature of an exon-intron spanning read.
"""