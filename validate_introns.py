#!/usr/bin/env python3
"""
Intron Validation Script using velocyto

This script takes a BAM file and GTF file as input and outputs validated intron locations.
It uses the velocyto library directly to perform intron validation by checking if reads
span exon-intron boundaries.

Usage:
    python validate_introns.py <bam_file> <gtf_file> [output_file]

Requirements:
    - velocyto
    - pysam
    - numpy
"""

import sys
import logging
import argparse
from typing import List, Dict, Tuple, Set, Any, Optional
from collections import defaultdict
import os

# Import velocyto components
import velocyto as vcy
import numpy as np


# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class IntronValidator:
    """Wrapper class that uses velocyto's ExInCounter for intron validation"""

    def __init__(self, sample_id: str = "intron_validation"):
        """Initialize the validator using velocyto components"""
        self.sample_id = sample_id

        # Create a logic object that performs intron validation
        # ValidatedIntrons10X has perform_validation_markup = True
        self.logic = vcy.logic.Permissive10X()


        # Initialize the ExInCounter with validation enabled
        self.counter = vcy.ExInCounter(
            sampleid=sample_id,
            logic=self.logic,
            valid_bcset=None,  # No barcode filtering
            umi_extension="without_umi",  # No UMI processing needed
            onefilepercell=False,
            dump_option="0",
            outputfolder="./",
            loom_numeric_dtype="float32"
        )

        # Override barcode detection since we don't need it for intron validation
        self.counter.cellbarcode_str = "CB"
        self.counter.umibarcode_str = "UB"

    def load_annotations(self, gtf_file: str):
        """Load transcript annotations from GTF file using velocyto"""
        logger.info(f"Loading annotations from {gtf_file}")

        # Use velocyto's GTF reader
        self.counter.read_transcriptmodels(gtf_file)

        # Log statistics
        total_transcripts = sum(len(models) for models in self.counter.annotations_by_chrm_strand.values())
        logger.info(f"Loaded {total_transcripts} transcript models from {len(self.counter.annotations_by_chrm_strand)} chromosome+strand combinations")

        # Count total introns
        total_introns = 0
        for chromstrand, models in self.counter.annotations_by_chrm_strand.items():
            for model in models.values():
                for feature in model.list_features:
                    if feature.kind == ord("i"):
                        total_introns += 1

        logger.info(f"Found {total_introns} total introns to validate")

    def validate_introns(self, bam_file: str):
        """Validate introns using velocyto's mark_up_introns method"""
        logger.info(f"Validating introns using reads from {bam_file}")

        # Use velocyto's intron markup functionality
        # This will mark introns as validated if reads span exon-intron boundaries
        self.counter.mark_up_introns((bam_file,), multimap=False)

        logger.info("Intron validation completed")

    def get_validated_introns(self) -> List[Dict]:
        """Extract validated introns from velocyto data structures"""
        validated_introns = []
        total_introns = 0
        validated_count = 0

        logger.info("Extracting validated introns...")

        for chromstrand, feature_index in self.counter.feature_indexes.items():
            chrom = chromstrand[:-1]
            strand = chromstrand[-1]

            for feature in feature_index.ivls:
                if feature.kind == ord("i"):  # Intron feature
                    total_introns += 1

                    if feature.is_validated:
                        validated_count += 1

                        intron_info = {
                            'chromosome': chrom,
                            'start': feature.start,
                            'end': feature.end,
                            'strand': strand,
                            'gene_id': feature.transcript_model.geneid,
                            'gene_name': feature.transcript_model.genename,
                            'transcript_id': feature.transcript_model.trid,
                            'transcript_name': feature.transcript_model.trname,
                            'intron_number': feature.exin_no,
                            'length': len(feature),
                            'validation_status': 'validated'
                        }
                        validated_introns.append(intron_info)

        logger.info(f"Found {validated_count} validated introns out of {total_introns} total introns")
        logger.info(f"Validation rate: {validated_count/total_introns*100:.1f}%" if total_introns > 0 else "No introns found")

        return validated_introns

    def get_all_introns(self) -> List[Dict]:
        """Extract all introns (validated and non-validated) from velocyto data structures"""
        all_introns = []

        logger.info("Extracting all introns...")

        for chromstrand, feature_index in self.counter.feature_indexes.items():
            chrom = chromstrand[:-1]
            strand = chromstrand[-1]

            for feature in feature_index.ivls:
                if feature.kind == ord("i"):  # Intron feature
                    intron_info = {
                        'chromosome': chrom,
                        'start': feature.start,
                        'end': feature.end,
                        'strand': strand,
                        'gene_id': feature.transcript_model.geneid,
                        'gene_name': feature.transcript_model.genename,
                        'transcript_id': feature.transcript_model.trid,
                        'transcript_name': feature.transcript_model.trname,
                        'intron_number': feature.exin_no,
                        'length': len(feature),
                        'validation_status': 'validated' if feature.is_validated else 'not_validated'
                    }
                    all_introns.append(intron_info)

        return all_introns


def write_output(output_file: str, introns: List[Dict], include_all: bool = False):
    """Write introns to output file"""
    logger.info(f"Writing {len(introns)} introns to {output_file}")

    with open(output_file, 'w') as f:
        # Write header
        header = [
            'chromosome', 'start', 'end', 'strand', 'gene_id', 'gene_name',
            'transcript_id', 'transcript_name', 'intron_number', 'length', 'validation_status'
        ]
        f.write('\t'.join(header) + '\n')

        # Sort introns by chromosome, start position
        sorted_introns = sorted(introns, key=lambda x: (x['chromosome'], x['start']))

        # Write introns
        for intron in sorted_introns:
            row = [str(intron[col]) for col in header]
            f.write('\t'.join(row) + '\n')


def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(
        description="Validate intron locations using BAM and GTF files with velocyto",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python validate_introns.py sample.bam annotations.gtf
    python validate_introns.py sample.bam annotations.gtf validated_introns.tsv
    python validate_introns.py sample.bam annotations.gtf --all-introns --verbose
        """
    )

    parser.add_argument('bam_file', help='Input BAM file (should be coordinate sorted)')
    parser.add_argument('gtf_file', help='Input GTF annotation file')
    parser.add_argument('output_file', nargs='?', default='validated_introns.tsv',
                       help='Output file for introns (default: validated_introns.tsv)')
    parser.add_argument('--all-introns', action='store_true',
                       help='Output all introns (validated and non-validated)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--sample-id', default='intron_validation',
                       help='Sample ID for velocyto processing (default: intron_validation)')

    args = parser.parse_args()

    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        # Also set velocyto logging to debug
        logging.getLogger('velocyto').setLevel(logging.DEBUG)

    # Check if files exist
    if not os.path.exists(args.bam_file):
        logger.error(f"BAM file not found: {args.bam_file}")
        sys.exit(1)

    if not os.path.exists(args.gtf_file):
        logger.error(f"GTF file not found: {args.gtf_file}")
        sys.exit(1)

    try:
        # Initialize validator using velocyto
        logger.info("Initializing intron validator using velocyto...")
        validator = IntronValidator(args.sample_id)

        # Load annotations
        logger.info("Loading transcript annotations...")
        validator.load_annotations(args.gtf_file)

        # Validate introns
        logger.info("Validating introns...")
        validator.validate_introns(args.bam_file)

        # Get introns
        if args.all_introns:
            introns = validator.get_all_introns()
            logger.info(f"Extracted {len(introns)} total introns")
        else:
            introns = validator.get_validated_introns()
            logger.info(f"Extracted {len(introns)} validated introns")

        # Write output
        write_output(args.output_file, introns, args.all_introns)

        # Summary statistics
        if args.all_introns:
            validated_count = sum(1 for i in introns if i['validation_status'] == 'validated')
            total_count = len(introns)
            logger.info(f"Summary: {validated_count}/{total_count} introns validated ({validated_count/total_count*100:.1f}%)")
        else:
            logger.info(f"Summary: {len(introns)} validated introns written")

        logger.info(f"Results written to: {args.output_file}")

    except Exception as e:
        logger.error(f"Error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)




def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(
        description="Validate intron locations using BAM and GTF files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python validate_introns.py sample.bam annotations.gtf
    python validate_introns.py sample.bam annotations.gtf validated_introns.tsv
    python validate_introns.py sample.bam annotations.gtf --verbose
        """
    )

    parser.add_argument('bam_file', help='Input BAM file')
    parser.add_argument('gtf_file', help='Input GTF annotation file')
    parser.add_argument('output_file', nargs='?', default='validated_introns.tsv',
                       help='Output file for validated introns (default: validated_introns.tsv)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')

    args = parser.parse_args()

    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # Initialize validator
        validator = IntronValidator()

        # Load annotations
        validator.load_annotations(args.gtf_file)

        # Validate introns
        validator.validate_introns(args.bam_file)

        # Get validated introns
        validated_introns = validator.get_validated_introns()

        # Write output
        validator.write_output(args.output_file, validated_introns)

        # Summary statistics
        total_introns = 0
        validated_count = len(validated_introns)

        for feature_index in validator.feature_indexes.values():
            for feature in feature_index.features:
                if feature.kind == 'i':
                    total_introns += 1

        logger.info(f"Validation complete!")
        logger.info(f"Total introns: {total_introns}")
        logger.info(f"Validated introns: {validated_count}")
        logger.info(f"Validation rate: {validated_count/total_introns*100:.1f}%" if total_introns > 0 else "No introns found")
        logger.info(f"Results written to: {args.output_file}")

    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
