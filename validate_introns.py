#!/usr/bin/env python3
"""
Intron Validation Script

This script takes a BAM file and GTF file as input and outputs validated intron locations.
Introns are validated by checking if reads span exon-intron boundaries.

Usage:
    python validate_introns.py <bam_file> <gtf_file> [output_file]

Based on the intron validation logic from velocyto.py
"""

import sys
import re
import logging
import argparse
from typing import List, Dict, Tuple, Set, Any, Optional
from collections import defaultdict, OrderedDict
import pysam


# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


# Constants
MIN_FLANK = 6  # Minimum flanking bases for overlap detection
LONGEST_INTRON_ALLOWED = 1000000  # 1Mbp maximum intron length


class Feature:
    """A genomic feature representing an exon, intron, or other annotated region"""
    
    def __init__(self, start: int, end: int, kind: str, exin_no: int, transcript_model=None):
        self.start = start
        self.end = end
        self.kind = kind  # 'e' for exon, 'i' for intron
        self.exin_no = exin_no
        self.transcript_model = transcript_model
        self.is_validated = False
    
    def __lt__(self, other):
        if self.start == other.start:
            return self.end < other.end
        return self.start < other.start
    
    def __len__(self):
        return (self.end - self.start) + 1
    
    def __repr__(self):
        return f"Feature({self.start}-{self.end}, {self.kind}{self.exin_no}, validated={self.is_validated})"
    
    def intersects(self, segment: Tuple[int, int], minimum_flanking: int = MIN_FLANK) -> bool:
        """Check if feature intersects with a read segment"""
        return (segment[1] - minimum_flanking > self.start) and \
               (segment[0] + minimum_flanking < self.end)
    
    def contains(self, segment: Tuple[int, int], minimum_flanking: int = MIN_FLANK) -> bool:
        """Check if feature completely contains a read segment"""
        return (segment[0] + minimum_flanking >= self.start) and \
               (segment[1] - minimum_flanking <= self.end) and \
               ((segment[1] - segment[0]) > minimum_flanking)
    
    def start_overlaps_with_part_of(self, segment: Tuple[int, int], minimum_flanking: int = MIN_FLANK) -> bool:
        """Check if read segment overlaps with the start of this feature"""
        return (segment[0] + minimum_flanking < self.start) and \
               (segment[1] - minimum_flanking > self.start)
    
    def end_overlaps_with_part_of(self, segment: Tuple[int, int], minimum_flanking: int = MIN_FLANK) -> bool:
        """Check if read segment overlaps with the end of this feature"""
        return (segment[0] + minimum_flanking < self.end) and \
               (segment[1] - minimum_flanking > self.end)
    
    def doesnt_start_after(self, segment: Tuple[int, int]) -> bool:
        """Check if feature doesn't start after the segment"""
        return self.start < segment[1]
    
    def ends_upstream_of(self, read_pos: int) -> bool:
        """Check if feature ends before the read position"""
        return self.end < read_pos
    
    def get_downstream_exon(self):
        """Get the downstream exon for this intron"""
        if self.transcript_model.chromstrand[-1] == "+":
            ix = self.exin_no * 2
        else:
            ix = len(self.transcript_model.list_features) - 2 * self.exin_no + 1
        return self.transcript_model.list_features[ix]
    
    def get_upstream_exon(self):
        """Get the upstream exon for this intron"""
        if self.transcript_model.chromstrand[-1] == "+":
            ix = (self.exin_no * 2) - 2
        else:
            ix = len(self.transcript_model.list_features) - 2 * self.exin_no - 1
        return self.transcript_model.list_features[ix]


class TranscriptModel:
    """A transcript model containing exons and introns"""
    
    def __init__(self, trid: str, trname: str, geneid: str, genename: str, chromstrand: str):
        self.trid = trid
        self.trname = trname
        self.geneid = geneid
        self.genename = genename
        self.chromstrand = chromstrand
        self.list_features: List[Feature] = []
    
    def __iter__(self):
        for feature in self.list_features:
            yield feature
    
    @property
    def start(self) -> int:
        return self.list_features[0].start if self.list_features else 0
    
    @property
    def end(self) -> int:
        return self.list_features[-1].end if self.list_features else 0
    
    def append_exon(self, exon_feature: Feature) -> None:
        """Append an exon and create an intron when needed"""
        exon_feature.transcript_model = self
        
        if len(self.list_features) == 0:
            # First exon
            self.list_features.append(exon_feature)
        else:
            # Create intron between previous exon and this exon
            if self.chromstrand[-1] == "+":
                intron_number = self.list_features[-1].exin_no
            else:
                intron_number = self.list_features[-1].exin_no - 1
            
            # Create intron feature
            intron = Feature(
                start=self.list_features[-1].end + 1,
                end=exon_feature.start - 1,
                kind='i',
                exin_no=intron_number,
                transcript_model=self
            )
            self.list_features.append(intron)
            self.list_features.append(exon_feature)
    
    def __repr__(self):
        features_str = '-'.join(f"{f.kind}{f.exin_no}" for f in self.list_features)
        return f"TranscriptModel({self.trid}, {features_str})"


class Read:
    """A read from BAM alignment"""
    
    def __init__(self, chrom: str, strand: str, pos: int, segments: List[Tuple[int, int]], ref_skipped: bool):
        self.chrom = chrom
        self.strand = strand
        self.pos = pos
        self.segments = segments
        self.ref_skipped = ref_skipped
    
    @property
    def is_spliced(self) -> bool:
        return self.ref_skipped
    
    @property
    def start(self) -> int:
        return self.segments[0][0] if self.segments else self.pos
    
    @property
    def end(self) -> int:
        return self.segments[-1][1] if self.segments else self.pos
    
    def __repr__(self):
        return f"Read({self.chrom}:{self.start}-{self.end}, {self.strand}, spliced={self.is_spliced})"


class FeatureIndex:
    """Index for efficient feature lookup and overlap detection"""
    
    def __init__(self, features: List[Feature]):
        self.features = sorted(features)
        self.iidx = 0  # Current index position
        self.maxiidx = len(self.features) - 1
    
    @property
    def last_interval_not_reached(self) -> bool:
        return self.iidx < self.maxiidx
    
    def reset(self):
        """Reset index to beginning"""
        self.iidx = 0
    
    def mark_overlapping_features(self, read: Read) -> None:
        """Mark introns that are validated by reads spanning exon-intron boundaries"""
        if len(self.features) == 0:
            return
        
        # Skip features that end before this read starts
        while self.last_interval_not_reached and self.features[self.iidx].ends_upstream_of(read.pos):
            self.iidx += 1
        
        # Check each segment of the read
        for segment in read.segments:
            i = self.iidx
            while i <= self.maxiidx and self.features[i].doesnt_start_after(segment):
                feature = self.features[i]
                
                # Only check introns for validation
                if feature.kind == 'i':
                    # Check if read overlaps with downstream exon
                    if feature.end_overlaps_with_part_of(segment):
                        try:
                            downstream_exon = feature.get_downstream_exon()
                            if downstream_exon.start_overlaps_with_part_of(segment):
                                feature.is_validated = True
                                logger.debug(f"Validated intron: {feature}")
                        except (IndexError, AttributeError):
                            # Handle cases where downstream exon doesn't exist
                            pass
                    
                    # Check if read overlaps with upstream exon
                    if feature.start_overlaps_with_part_of(segment):
                        try:
                            upstream_exon = feature.get_upstream_exon()
                            if upstream_exon.end_overlaps_with_part_of(segment):
                                feature.is_validated = True
                                logger.debug(f"Validated intron: {feature}")
                        except (IndexError, AttributeError):
                            # Handle cases where upstream exon doesn't exist
                            pass
                
                i += 1


class GTFParser:
    """Parser for GTF files to extract transcript models"""
    
    def __init__(self):
        # Regex patterns for GTF parsing
        self.regex_trid = re.compile(r'transcript_id "([^"]+)"')
        self.regex_trname = re.compile(r'transcript_name "([^"]+)"')
        self.regex_geneid = re.compile(r'gene_id "([^"]+)"')
        self.regex_genename = re.compile(r'gene_name "([^"]+)"')
        self.regex_exonno = re.compile(r'exon_number "?(\d+)"?')
    
    def parse_gtf(self, gtf_file: str) -> Dict[str, Dict[str, TranscriptModel]]:
        """Parse GTF file and return transcript models by chromosome+strand"""
        logger.info(f"Parsing GTF file: {gtf_file}")
        
        annotations_by_chrm_strand = {}
        
        # Read and sort GTF lines
        gtf_lines = [line for line in open(gtf_file) if not line.startswith('#')]
        gtf_lines = sorted(gtf_lines, key=self._sorting_key)
        
        curr_chromstrand = None
        features = OrderedDict()
        
        for line_num, line in enumerate(gtf_lines):
            fields = line.rstrip().split('\t')
            if len(fields) < 9:
                continue
                
            chrom, feature_class, feature_type, start_str, end_str, _, strand, _, tags = fields
            
            # Normalize chromosome name
            if chrom.startswith('chr'):
                chrom = chrom[3:]
            
            # Only process exon features
            if feature_type != "exon":
                continue
            
            # Extract attributes
            try:
                trid = self.regex_trid.search(tags).group(1)
                geneid = self.regex_geneid.search(tags).group(1)
                
                trname_match = self.regex_trname.search(tags)
                trname = trname_match.group(1) if trname_match else trid
                
                genename_match = self.regex_genename.search(tags)
                genename = genename_match.group(1) if genename_match else geneid
                
                exonno_match = self.regex_exonno.search(tags)
                if not exonno_match:
                    logger.warning(f"No exon_number found in line {line_num + 1}")
                    continue
                exonno = int(exonno_match.group(1))
                
            except (AttributeError, ValueError) as e:
                logger.warning(f"Error parsing line {line_num + 1}: {e}")
                continue
            
            start = int(start_str)
            end = int(end_str)
            chromstrand = chrom + strand
            
            # Handle chromosome/strand changes
            if chromstrand != curr_chromstrand:
                if curr_chromstrand is not None:
                    annotations_by_chrm_strand[curr_chromstrand] = features
                    logger.debug(f"Processed {len(features)} transcripts for {curr_chromstrand}")
                
                features = OrderedDict()
                curr_chromstrand = chromstrand
                logger.debug(f"Processing chromosome {chrom} strand {strand}")
            
            # Create or update transcript model
            if trid not in features:
                features[trid] = TranscriptModel(trid, trname, geneid, genename, chromstrand)
            
            # Add exon to transcript model
            exon = Feature(start=start, end=end, kind='e', exin_no=exonno)
            features[trid].append_exon(exon)
        
        # Handle last chromosome
        if curr_chromstrand is not None:
            annotations_by_chrm_strand[curr_chromstrand] = features
            logger.debug(f"Processed {len(features)} transcripts for {curr_chromstrand}")
        
        logger.info(f"Parsed {len(annotations_by_chrm_strand)} chromosome+strand combinations")
        return annotations_by_chrm_strand
    
    def _sorting_key(self, entry: str) -> Tuple[str, bool, int, str]:
        """Sorting key equivalent to sort -k1,1 -k7,7 -k4,4n"""
        fields = entry.split('\t')
        if len(fields) < 7:
            return ('', True, 0, entry)
        return (fields[0], fields[6] == "+", int(fields[3]), entry)


class BAMReader:
    """Reader for BAM files to extract read alignments"""

    def __init__(self, bam_file: str):
        self.bam_file = bam_file

    def parse_cigar_tuple(self, cigartuples: List[Tuple], pos: int) -> Tuple[List[Tuple[int, int]], bool]:
        """Parse CIGAR tuples to get genomic segments and splice status"""
        segments = []
        ref_skip = False
        p = pos

        for operation_id, length in cigartuples:
            if operation_id == 0:  # Match/mismatch (M)
                segments.append((p, p + length - 1))
                p += length
            elif operation_id == 3:  # Reference skip (N) - indicates splicing
                ref_skip = True
                p += length
            elif operation_id == 2:  # Deletion (D)
                p += length
            elif operation_id == 1:  # Insertion (I)
                pass  # Don't advance reference position
            elif operation_id == 4:  # Soft clipping (S)
                pass  # Don't advance reference position
            elif operation_id == 5:  # Hard clipping (H)
                pass  # Don't advance reference position

        return segments, ref_skip

    def iter_alignments(self, unique_only: bool = True) -> Read:
        """Iterate over BAM file and yield Read objects"""
        logger.info(f"Reading BAM file: {self.bam_file}")

        with pysam.AlignmentFile(self.bam_file, "rb") as bamfile:
            read_count = 0
            processed_count = 0

            for alignment in bamfile:
                read_count += 1
                if read_count % 1000000 == 0:
                    logger.info(f"Processed {read_count // 1000000}M reads")

                # Skip unmapped reads
                if alignment.is_unmapped:
                    continue

                # Skip non-unique alignments if requested
                if unique_only and alignment.has_tag("NH") and alignment.get_tag("NH") != 1:
                    continue

                # Get chromosome name
                chrom = alignment.reference_name
                if chrom.startswith('chr'):
                    chrom = chrom[3:]
                    if chrom == "M":
                        chrom = "MT"

                # Get strand
                strand = '-' if alignment.is_reverse else '+'

                # Parse CIGAR to get segments
                pos = alignment.reference_start + 1  # Convert to 1-based
                segments, ref_skipped = self.parse_cigar_tuple(alignment.cigartuples, pos)

                if not segments:
                    continue

                # Create Read object
                read = Read(chrom, strand, pos, segments, ref_skipped)

                # Skip reads with very long spans (likely mapping errors)
                if read.end - read.start > 3000000:
                    continue

                processed_count += 1
                yield read

            logger.info(f"Processed {processed_count} valid reads from {read_count} total reads")


class IntronValidator:
    """Main class for validating introns using BAM and GTF data"""

    def __init__(self):
        self.annotations_by_chrm_strand = {}
        self.feature_indexes = {}

    def load_annotations(self, gtf_file: str):
        """Load transcript annotations from GTF file"""
        parser = GTFParser()
        self.annotations_by_chrm_strand = parser.parse_gtf(gtf_file)

        # Create feature indexes for efficient lookup
        logger.info("Creating feature indexes...")
        for chromstrand, transcript_models in self.annotations_by_chrm_strand.items():
            # Collect all features from all transcript models
            all_features = []
            for tm in transcript_models.values():
                all_features.extend(tm.list_features)

            self.feature_indexes[chromstrand] = FeatureIndex(all_features)
            logger.debug(f"Created index for {chromstrand} with {len(all_features)} features")

    def validate_introns(self, bam_file: str):
        """Validate introns using reads from BAM file"""
        logger.info("Starting intron validation...")

        bam_reader = BAMReader(bam_file)
        curr_chrom = ""
        chromosomes_seen = set()

        for read in bam_reader.iter_alignments():
            # Skip spliced reads (they don't validate introns)
            if read.is_spliced:
                continue

            # Handle chromosome changes
            if read.chrom != curr_chrom:
                if read.chrom in chromosomes_seen:
                    raise ValueError(f"BAM file is not sorted by chromosome. Found {read.chrom} after processing it before.")

                chromosomes_seen.add(read.chrom)
                curr_chrom = read.chrom
                logger.debug(f"Processing chromosome {curr_chrom}")

                # Reset feature indexes for new chromosome
                for chromstrand in self.feature_indexes:
                    self.feature_indexes[chromstrand].reset()

            # Get feature index for this chromosome+strand
            chromstrand = read.chrom + read.strand
            if chromstrand not in self.feature_indexes:
                continue  # No annotations for this chromosome+strand

            # Mark overlapping features (validate introns)
            feature_index = self.feature_indexes[chromstrand]
            feature_index.mark_overlapping_features(read)

    def get_validated_introns(self) -> List[Dict]:
        """Get list of validated introns with their information"""
        validated_introns = []

        for chromstrand, feature_index in self.feature_indexes.items():
            chrom = chromstrand[:-1]
            strand = chromstrand[-1]

            for feature in feature_index.features:
                if feature.kind == 'i' and feature.is_validated:
                    intron_info = {
                        'chromosome': chrom,
                        'start': feature.start,
                        'end': feature.end,
                        'strand': strand,
                        'gene_id': feature.transcript_model.geneid,
                        'gene_name': feature.transcript_model.genename,
                        'transcript_id': feature.transcript_model.trid,
                        'transcript_name': feature.transcript_model.trname,
                        'intron_number': feature.exin_no,
                        'length': len(feature)
                    }
                    validated_introns.append(intron_info)

        return validated_introns

    def write_output(self, output_file: str, validated_introns: List[Dict]):
        """Write validated introns to output file"""
        logger.info(f"Writing {len(validated_introns)} validated introns to {output_file}")

        with open(output_file, 'w') as f:
            # Write header
            header = [
                'chromosome', 'start', 'end', 'strand', 'gene_id', 'gene_name',
                'transcript_id', 'transcript_name', 'intron_number', 'length'
            ]
            f.write('\t'.join(header) + '\n')

            # Write validated introns
            for intron in validated_introns:
                row = [str(intron[col]) for col in header]
                f.write('\t'.join(row) + '\n')


def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(
        description="Validate intron locations using BAM and GTF files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python validate_introns.py sample.bam annotations.gtf
    python validate_introns.py sample.bam annotations.gtf validated_introns.tsv
    python validate_introns.py sample.bam annotations.gtf --verbose
        """
    )

    parser.add_argument('bam_file', help='Input BAM file')
    parser.add_argument('gtf_file', help='Input GTF annotation file')
    parser.add_argument('output_file', nargs='?', default='validated_introns.tsv',
                       help='Output file for validated introns (default: validated_introns.tsv)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')

    args = parser.parse_args()

    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # Initialize validator
        validator = IntronValidator()

        # Load annotations
        validator.load_annotations(args.gtf_file)

        # Validate introns
        validator.validate_introns(args.bam_file)

        # Get validated introns
        validated_introns = validator.get_validated_introns()

        # Write output
        validator.write_output(args.output_file, validated_introns)

        # Summary statistics
        total_introns = 0
        validated_count = len(validated_introns)

        for feature_index in validator.feature_indexes.values():
            for feature in feature_index.features:
                if feature.kind == 'i':
                    total_introns += 1

        logger.info(f"Validation complete!")
        logger.info(f"Total introns: {total_introns}")
        logger.info(f"Validated introns: {validated_count}")
        logger.info(f"Validation rate: {validated_count/total_introns*100:.1f}%" if total_introns > 0 else "No introns found")
        logger.info(f"Results written to: {args.output_file}")

    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
