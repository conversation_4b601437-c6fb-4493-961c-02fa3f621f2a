<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Understanding Fig Gamma in Velocyto</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        header {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            margin: 0;
            font-size: 2.5rem;
        }
        h2 {
            color: #2575fc;
            border-bottom: 2px solid #2575fc;
            padding-bottom: 0.5rem;
            margin-top: 2rem;
        }
        h3 {
            color: #6a11cb;
        }
        .section {
            background: white;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .formula {
            background: #f1f8ff;
            padding: 1rem;
            border-left: 4px solid #2575fc;
            margin: 1.5rem 0;
            overflow-x: auto;
        }
        .visualization {
            text-align: center;
            margin: 2rem 0;
        }
        .visualization svg {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .explanation {
            margin: 1rem 0;
            padding: 1rem;
            background: #e8f4fc;
            border-radius: 5px;
        }
        .conclusion {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 1.5rem;
            border-radius: 8px;
            margin-top: 2rem;
        }
        code {
            background: #f1f1f1;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .highlight {
            background: #fff9c4;
            padding: 0.2rem;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <header>
        <h1>Understanding Fig Gamma in Velocyto</h1>
        <p>Exploring the mathematical foundations of RNA velocity estimation</p>
    </header>

    <div class="section">
        <h2>Introduction to RNA Velocity</h2>
        <p>RNA velocity is a powerful technique for analyzing single-cell RNA sequencing data that provides insights into the direction and speed of gene expression changes over time. The <span class="highlight">velocyto</span> package is one of the pioneering tools for calculating RNA velocity.</p>
        
        <div class="explanation">
            <h3>Key Concept: "Fig Gamma"</h3>
            <p>"Fig gamma" refers to the phase portrait visualization in velocyto that shows the relationship between spliced (S) and unspliced (U) RNA molecules for each gene. These plots display:</p>
            <ul>
                <li>Scatter points representing individual cells with their spliced vs unspliced expression levels</li>
                <li>A fitted line with slope gamma (γ) that represents the steady-state relationship</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <h2>Biological Model of Transcription and Splicing</h2>
        <p>The gamma fitting in velocyto is based on a mechanistic model of transcription and splicing:</p>
        
        <div class="formula">
            \[\frac{dU}{dt} = \alpha - \gamma \cdot S\]
        </div>
        
        <p>Where:</p>
        <ul>
            <li>\(U\): Unspliced RNA abundance</li>
            <li>\(S\): Spliced RNA abundance</li>
            <li>\(\alpha\): Transcription rate</li>
            <li>\(\gamma\): Degradation/splicing rate</li>
        </ul>
        
        <div class="visualization">
            <svg width="600" height="300" viewBox="0 0 600 300">
                <!-- Nucleus -->
                <ellipse cx="100" cy="150" rx="60" ry="80" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
                <text x="100" y="150" text-anchor="middle" dominant-baseline="middle" font-weight="bold">Nucleus</text>
                
                <!-- Gene -->
                <rect x="80" y="120" width="40" height="10" fill="#1976d2"/>
                <text x="100" y="115" text-anchor="middle" font-size="12">Gene</text>
                
                <!-- Transcription -->
                <line x1="120" y1="125" x2="200" y2="100" stroke="#4caf50" stroke-width="2" marker-end="url(#arrow)"/>
                <text x="160" y="95" text-anchor="middle" font-size="12" fill="#4caf50">Transcription</text>
                
                <!-- Unspliced RNA -->
                <circle cx="220" cy="100" r="8" fill="#ff9800"/>
                <text x="240" y="100" dominant-baseline="middle" font-size="12">U</text>
                
                <!-- Splicing -->
                <line x1="228" y1="100" x2="300" y2="120" stroke="#9c27b0" stroke-width="2" marker-end="url(#arrow)"/>
                <text x="265" y="95" text-anchor="middle" font-size="12" fill="#9c27b0">Splicing</text>
                
                <!-- Spliced RNA -->
                <circle cx="320" cy="120" r="8" fill="#f44336"/>
                <text x="340" y="120" dominant-baseline="middle" font-size="12">S</text>
                
                <!-- Degradation -->
                <line x1="328" y1="120" x2="380" y2="150" stroke="#795548" stroke-width="2" marker-end="url(#arrow)"/>
                <text x="350" y="140" text-anchor="middle" font-size="12" fill="#795548">Degradation</text>
                
                <!-- Cytoplasm -->
                <rect x="200" y="50" width="250" height="150" fill="none" stroke="#424242" stroke-dasharray="5,5"/>
                <text x="325" y="45" text-anchor="middle" font-size="12">Cell</text>
                
                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrow" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto" markerUnits="strokeWidth">
                        <path d="M0,0 L0,6 L9,3 z" fill="#000"/>
                    </marker>
                </defs>
            </svg>
            <p><strong>Figure 1:</strong> Biological process of transcription and splicing</p>
        </div>
    </div>

    <div class="section">
        <h2>Mathematical Formulation of Gamma Fitting</h2>
        <p>At steady state, when \(\frac{dU}{dt} = 0\), we have:</p>
        
        <div class="formula">
            \[0 = \alpha - \gamma \cdot S\]
            \[U = \frac{\alpha}{\gamma} + \frac{1}{\gamma} \cdot S\]
        </div>
        
        <p>This means that at steady state, unspliced and spliced RNA have a linear relationship. Velocyto fits this relationship using least squares regression:</p>
        
        <div class="formula">
            \[U = \gamma \cdot S + q\]
        </div>
        
        <p>Where:</p>
        <ul>
            <li>\(\gamma\): The slope of the line (degradation rate)</li>
            <li>\(q\): The offset (y-intercept)</li>
        </ul>
        
        <div class="explanation">
            <h3>Fitting Process in Velocyto</h3>
            <p>The <code>fit_gammas</code> method in velocyto performs the fitting using several approaches:</p>
            <ol>
                <li><strong>Simple linear regression</strong>: \(U = \gamma \cdot S + q\)</li>
                <li><strong>Weighted regression</strong>: Using weights to emphasize cells in steady state</li>
                <li><strong>Constrained fitting</strong>: Limiting gamma values for biological plausibility</li>
            </ol>
        </div>
    </div>

    <div class="section">
        <h2>Phase Portrait Visualization</h2>
        <p>The phase portrait plots (what users refer to as "fig gamma") visualize the relationship between spliced and unspliced RNA for each gene:</p>
        
        <div class="visualization">
            <svg width="500" height="400" viewBox="0 0 500 400">
                <!-- Axes -->
                <line x1="50" y1="350" x2="450" y2="350" stroke="black" stroke-width="2"/>
                <line x1="50" y1="350" x2="50" y2="50" stroke="black" stroke-width="2"/>
                
                <!-- Axis labels -->
                <text x="250" y="380" text-anchor="middle" font-weight="bold">Spliced (S)</text>
                <text x="20" y="200" text-anchor="middle" transform="rotate(-90, 20, 200)" font-weight="bold">Unspliced (U)</text>
                
                <!-- Data points -->
                <circle cx="80" cy="320" r="4" fill="#2196f3" opacity="0.7"/>
                <circle cx="110" cy="300" r="4" fill="#2196f3" opacity="0.7"/>
                <circle cx="140" cy="280" r="4" fill="#2196f3" opacity="0.7"/>
                <circle cx="170" cy="260" r="4" fill="#2196f3" opacity="0.7"/>
                <circle cx="200" cy="240" r="4" fill="#2196f3" opacity="0.7"/>
                <circle cx="230" cy="220" r="4" fill="#2196f3" opacity="0.7"/>
                <circle cx="260" cy="210" r="4" fill="#2196f3" opacity="0.7"/>
                <circle cx="290" cy="200" r="4" fill="#2196f3" opacity="0.7"/>
                <circle cx="320" cy="190" r="4" fill="#2196f3" opacity="0.7"/>
                <circle cx="350" cy="185" r="4" fill="#2196f3" opacity="0.7"/>
                <circle cx="380" cy="180" r="4" fill="#2196f3" opacity="0.7"/>
                <circle cx="410" cy="175" r="4" fill="#2196f3" opacity="0.7"/>
                
                <!-- Outlier points -->
                <circle cx="150" cy="230" r="4" fill="#f44336" opacity="0.7"/>
                <circle cx="250" cy="180" r="4" fill="#f44336" opacity="0.7"/>
                <circle cx="330" cy="150" r="4" fill="#f44336" opacity="0.7"/>
                
                <!-- Fitted line -->
                <line x1="80" y1="320" x2="410" y2="175" stroke="#4caf50" stroke-width="3"/>
                
                <!-- Gamma annotation -->
                <text x="300" y="250" font-size="16" fill="#4caf50" font-weight="bold">γ = slope</text>
                <line x1="300" y1="240" x2="250" y2="270" stroke="#4caf50" stroke-dasharray="5,5"/>
                
                <!-- Grid lines -->
                <g stroke="#ccc" stroke-dasharray="2,2">
                    <line x1="50" y1="300" x2="450" y2="300"/>
                    <line x1="50" y1="250" x2="450" y2="250"/>
                    <line x1="50" y1="200" x2="450" y2="200"/>
                    <line x1="50" y1="150" x2="450" y2="150"/>
                    <line x1="50" y1="100" x2="450" y2="100"/>
                    <line x1="100" y1="350" x2="100" y2="50"/>
                    <line x1="150" y1="350" x2="150" y2="50"/>
                    <line x1="200" y1="350" x2="200" y2="50"/>
                    <line x1="250" y1="350" x2="250" y2="50"/>
                    <line x1="300" y1="350" x2="300" y2="50"/>
                    <line x1="350" y1="350" x2="350" y2="50"/>
                    <line x1="400" y1="350" x2="400" y2="50"/>
                </g>
                
                <!-- Axis ticks -->
                <g font-size="10" text-anchor="middle">
                    <text x="100" y="365">1</text>
                    <text x="150" y="365">2</text>
                    <text x="200" y="365">3</text>
                    <text x="250" y="365">4</text>
                    <text x="300" y="365">5</text>
                    <text x="350" y="365">6</text>
                    <text x="400" y="365">7</text>
                </g>
                <g font-size="10" text-anchor="end">
                    <text x="45" y="300">1</text>
                    <text x="45" y="250">2</text>
                    <text x="45" y="200">3</text>
                    <text x="45" y="150">4</text>
                    <text x="45" y="100">5</text>
                </g>
            </svg>
            <p><strong>Figure 2:</strong> Phase portrait showing the relationship between spliced and unspliced RNA</p>
            <p>Blue points represent cells following the expected steady-state relationship, while red points are outliers. The green line represents the fitted gamma relationship.</p>
        </div>
    </div>

    <div class="section">
        <h2>Using Gamma Values for RNA Velocity Calculation</h2>
        <p>Once gamma values are estimated for each gene, RNA velocity can be calculated:</p>
        
        <div class="formula">
            \[\text{Velocity} = U_{\text{observed}} - U_{\text{expected}} = U_{\text{observed}} - (\gamma \cdot S + q)\]
        </div>
        
        <p>This velocity represents the difference between the observed unspliced RNA and the amount expected at steady state, indicating whether expression is increasing (positive velocity) or decreasing (negative velocity).</p>
        
        <div class="visualization">
            <svg width="600" height="300" viewBox="0 0 600 300">
                <!-- Cell states -->
                <circle cx="100" cy="150" r="30" fill="#bbdefb"/>
                <text x="100" y="150" text-anchor="middle" dominant-baseline="middle">Cell A</text>
                <text x="100" y="190" text-anchor="middle" font-size="12">Low velocity</text>
                
                <circle cx="300" cy="150" r="30" fill="#64b5f6"/>
                <text x="300" y="150" text-anchor="middle" dominant-baseline="middle">Cell B</text>
                <text x="300" y="190" text-anchor="middle" font-size="12">Medium velocity</text>
                
                <circle cx="500" cy="150" r="30" fill="#1976d2"/>
                <text x="500" y="150" text-anchor="middle" dominant-baseline="middle">Cell C</text>
                <text x="500" y="190" text-anchor="middle" font-size="12">High velocity</text>
                
                <!-- Velocity arrows -->
                <line x1="130" y1="150" x2="160" y2="150" stroke="#4caf50" stroke-width="2" marker-end="url(#arrow2)"/>
                <line x1="330" y1="150" x2="380" y2="150" stroke="#ff9800" stroke-width="3" marker-end="url(#arrow2)"/>
                <line x1="530" y1="150" x2="580" y2="150" stroke="#f44336" stroke-width="4" marker-end="url(#arrow2)"/>
                
                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrow2" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto" markerUnits="strokeWidth">
                        <path d="M0,0 L0,6 L9,3 z" fill="#000"/>
                    </marker>
                </defs>
                
                <!-- Labels -->
                <text x="145" y="140" text-anchor="middle" font-size="12">→</text>
                <text x="355" y="140" text-anchor="middle" font-size="12">→→</text>
                <text x="555" y="140" text-anchor="middle" font-size="12">→→→</text>
            </svg>
            <p><strong>Figure 3:</strong> RNA velocity represented as vectors indicating direction and magnitude of gene expression changes</p>
        </div>
    </div>

    <div class="conclusion">
        <h2>Conclusion</h2>
        <p>"Fig gamma" in velocyto refers to the phase portrait visualization that shows the relationship between spliced and unspliced RNA molecules for each gene. The gamma fitting process:</p>
        <ol>
            <li>Models the biological process of transcription and splicing</li>
            <li>Uses linear regression to fit the steady-state relationship \(U = \gamma \cdot S + q\)</li>
            <li>Estimates gamma values that represent degradation/splicing rates</li>
            <li>Visualizes these relationships in scatter plots with fitted lines</li>
            <li>Uses gamma values to calculate RNA velocity for each gene in each cell</li>
        </ol>
        <p>This approach enables velocyto to predict the direction and speed of gene expression changes, providing insights into cellular dynamics and developmental trajectories in single-cell RNA sequencing data.</p>
    </div>
</body>
</html>