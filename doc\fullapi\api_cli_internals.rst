CLI Internals
=============

The following page describes the internal API used by the Command Line Pipeline.
These functions and objects are not meant for interactive usage.
So this page is useful if you want to change the behavior of the molecule counting pipeline.

velocyto\.counter module
------------------------

.. automodule:: velocyto.counter
    :members:
    :undoc-members:
    :show-inheritance:

velocyto\.transcript_model module
---------------------------------

.. automodule:: velocyto.transcript_model
    :members:
    :undoc-members:
    :show-inheritance:

velocyto\.segment_match module
------------------------------

.. automodule:: velocyto.segment_match
    :members:
    :undoc-members:
    :show-inheritance:

velocyto\.feature module
------------------------

.. automodule:: velocyto.feature
    :members:
    :undoc-members:
    :show-inheritance:

velocyto\.indexes module
------------------------

.. automodule:: velocyto.indexes
    :members:
    :undoc-members:
    :show-inheritance:

velocyto\.molitem module
------------------------

.. automodule:: velocyto.molitem
    :members:
    :undoc-members:
    :show-inheritance:

velocyto\.gene_info module
--------------------------

.. automodule:: velocyto.gene_info
    :members:
    :undoc-members:
    :show-inheritance:


velocyto\.read module
---------------------

.. automodule:: velocyto.read
    :members:
    :undoc-members:
    :show-inheritance:

