archive/
cache/
data/
figs/
figures/
scripts/
write/
scanpy*/
benchmarking/
htmlcov/

scvelo.egg-info/

# Python / Byte-compiled / optimized / DLL
__pycache__/
*.py[cod]
*.so
.cache

# always-ignore notebooks
*.ipynb

# OS or Editor files and folders
.DS_Store
Thumbs.db
.ipynb_checkpoints/
.directory
/.idea/
.vscode/
=19

# Docs build
docs/build
docs/source/scvelo*

# always-ignore directories
/build/
/dist/

.coverage
.eggs

# Files generated by unit tests
.hypothesis/
.coverage*
htmlcov/
